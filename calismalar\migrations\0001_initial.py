# Generated by Django 5.2 on 2025-05-03 22:16

import calismalar.models
import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Calisma',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('baslik', models.CharField(max_length=200, verbose_name='<PERSON><PERSON><PERSON><PERSON><PERSON>')),
                ('aciklama', models.TextField(verbose_name='Açıklama')),
                ('slug', models.SlugField(max_length=250, unique=True)),
                ('olusturma_tarihi', models.DateTimeField(auto_now_add=True, verbose_name='Oluşturma Tarihi')),
                ('guncelleme_tarihi', models.DateTimeField(auto_now=True, verbose_name='<PERSON><PERSON><PERSON><PERSON><PERSON>')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='calismalar', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Çalışma',
                'verbose_name_plural': 'Çalışmalar',
                'ordering': ['-olusturma_tarihi'],
            },
        ),
        migrations.CreateModel(
            name='CalismaFotograf',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('fotograf', models.ImageField(upload_to=calismalar.models.calisma_fotograf_yolu, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'gif'])], verbose_name='Fotoğraf')),
                ('aciklama', models.CharField(blank=True, max_length=255, verbose_name='Açıklama')),
                ('sira', models.PositiveIntegerField(default=0, verbose_name='Sıra')),
                ('olusturma_tarihi', models.DateTimeField(auto_now_add=True, verbose_name='Oluşturma Tarihi')),
                ('calisma', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fotograflar', to='calismalar.calisma')),
            ],
            options={
                'verbose_name': 'Çalışma Fotoğrafı',
                'verbose_name_plural': 'Çalışma Fotoğrafları',
                'ordering': ['sira', 'olusturma_tarihi'],
            },
        ),
    ]
