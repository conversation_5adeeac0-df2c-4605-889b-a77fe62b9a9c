from django.db import models
from django.contrib.auth.models import User
from django.utils.text import slugify
import os
import uuid

def calisma_fotograf_yolu(instance, filename):
    """Fotoğraf dosyalarını güvenli bir şekilde yüklemek için özel bir yol oluşturur."""
    # Dosya adını güvenli hale getir
    ext = os.path.splitext(filename)[1].lower()
    if not ext:
        ext = '.jpg'  # Varsayılan uzantı
    elif ext[0] != '.':
        ext = '.' + ext

    # İzin verilen uzantıları kontrol et
    allowed_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
    if ext not in allowed_extensions:
        ext = '.jpg'  # Geçersiz uzantıyı varsayılan ile değiştir

    # Benzersiz bir dosya adı oluştur
    safe_filename = f"{uuid.uuid4().hex}{ext}"

    # Kullanıcı ID'sine göre klasörleme yap
    # Eğer instance.calisma.user.id yoksa (örneğin yeni bir kayıt oluşturuluyorsa)
    # geçici bir klasör kullan
    try:
        # Önce calisma.user.id'yi kontrol et
        if hasattr(instance, 'calisma') and hasattr(instance.calisma, 'user') and instance.calisma.user:
            user_id = str(instance.calisma.user.id)
        # Eğer request.user varsa onu kullan
        elif hasattr(instance, 'user') and instance.user:
            user_id = str(instance.user.id)
        else:
            user_id = 'temp'
    except Exception as e:
        print(f"Fotoğraf yolu oluşturma hatası: {e}")
        user_id = 'temp'

    return os.path.join('calisma_fotograflari', user_id, safe_filename)

class Kategori(models.Model):
    """Çalışma kategorilerini temsil eden model."""
    ad = models.CharField(max_length=100, verbose_name='Kategori Adı')
    slug = models.SlugField(unique=True)
    aciklama = models.TextField(blank=True, verbose_name='Açıklama')

    class Meta:
        verbose_name = 'Kategori'
        verbose_name_plural = 'Kategoriler'
        ordering = ['ad']

    def __str__(self):
        return self.ad

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.ad)
        super().save(*args, **kwargs)

class Etiket(models.Model):
    """Çalışma etiketlerini temsil eden model."""
    ad = models.CharField(max_length=50, unique=True, verbose_name='Etiket Adı')
    slug = models.SlugField(unique=True)

    class Meta:
        verbose_name = 'Etiket'
        verbose_name_plural = 'Etiketler'
        ordering = ['ad']

    def __str__(self):
        return self.ad

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.ad)
        super().save(*args, **kwargs)

class Calisma(models.Model):
    """Kullanıcıların atölye çalışmalarını temsil eden model."""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='calismalar')
    baslik = models.CharField(max_length=200, verbose_name='Başlık')
    aciklama = models.TextField(verbose_name='Açıklama')
    slug = models.SlugField(unique=True, max_length=250)
    kategori = models.ForeignKey(Kategori, on_delete=models.SET_NULL, null=True, blank=True, related_name='calismalar')
    etiketler = models.ManyToManyField(Etiket, blank=True, related_name='calismalar')
    goruntulenme_sayisi = models.PositiveIntegerField(default=0, verbose_name='Görüntülenme Sayısı')
    begeni_sayisi = models.PositiveIntegerField(default=0, verbose_name='Beğeni Sayısı')
    olusturma_tarihi = models.DateTimeField(auto_now_add=True, verbose_name='Oluşturma Tarihi')
    guncelleme_tarihi = models.DateTimeField(auto_now=True, verbose_name='Güncelleme Tarihi')

    class Meta:
        verbose_name = 'Çalışma'
        verbose_name_plural = 'Çalışmalar'
        ordering = ['-olusturma_tarihi']
        indexes = [
            models.Index(fields=['-olusturma_tarihi']),
            models.Index(fields=['kategori']),
            models.Index(fields=['user']),
        ]

    def __str__(self):
        return self.baslik

    def save(self, *args, **kwargs):
        """Slug oluşturma ve kaydetme işlemi."""
        if not self.slug:
            # Slug oluştur
            base_slug = slugify(self.baslik)
            # Benzersiz slug oluştur
            unique_slug = base_slug
            num = 1
            while Calisma.objects.filter(slug=unique_slug).exists():
                unique_slug = f"{base_slug}-{num}"
                num += 1
            self.slug = unique_slug
        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        """Çalışma silindiğinde ilişkili fotoğrafları da sil."""
        # Fotoğrafları al
        fotograflar = list(self.fotograflar.all())

        # Önce modeli sil (CASCADE ile fotoğraf kayıtları da silinecek)
        super().delete(*args, **kwargs)

        # Fotoğraf dosyalarını fiziksel olarak sil
        for fotograf in fotograflar:
            if fotograf.fotograf:
                storage, path = fotograf.fotograf.storage, fotograf.fotograf.path
                if storage.exists(path):
                    storage.delete(path)

class CalismaFotograf(models.Model):
    """Çalışmalara ait fotoğrafları temsil eden model."""
    calisma = models.ForeignKey(Calisma, on_delete=models.CASCADE, related_name='fotograflar')
    fotograf = models.ImageField(
        upload_to=calisma_fotograf_yolu,
        verbose_name='Fotoğraf'
    )
    aciklama = models.CharField(max_length=255, blank=True, verbose_name='Açıklama')
    sira = models.PositiveIntegerField(default=0, verbose_name='Sıra')
    olusturma_tarihi = models.DateTimeField(auto_now_add=True, verbose_name='Oluşturma Tarihi')

    class Meta:
        verbose_name = 'Çalışma Fotoğrafı'
        verbose_name_plural = 'Çalışma Fotoğrafları'
        ordering = ['sira', 'olusturma_tarihi']
        indexes = [
            models.Index(fields=['calisma', 'sira']),
        ]

    def __str__(self):
        return f"{self.calisma.baslik} - Fotoğraf {self.sira}"

    def delete(self, *args, **kwargs):
        """Fotoğraf dosyasını fiziksel olarak sil."""
        # Dosya yolunu al
        storage, path = self.fotograf.storage, self.fotograf.path

        # Modeli sil
        super().delete(*args, **kwargs)

        # Dosyayı sil
        if storage.exists(path):
            storage.delete(path)
