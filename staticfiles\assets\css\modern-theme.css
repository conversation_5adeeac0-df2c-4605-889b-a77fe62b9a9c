/*
* MODERN THEME CSS
* Bu dosya, Küp Cadısı web sitesinin modern ve minimalist tasarım dilini tanımlar.
* Tüm sayfalar için ortak kullanılacak CSS stillerini içerir.
*
* Son Güncellemeler:
* - anasayfa-styles.css dosyasındaki stiller entegre edilmiştir.
* - calisma-detay.css dosyasındaki stiller entegre edilmiştir.
* - CSS optimizasyonu yapılmıştır (07.05.2025).
* - Renk paleti güncellendi - Sonbahar renkleri eklendi (09.05.2025).
* - <PERSON><PERSON> yeşili renk kullanımı artırıldı (10.05.2025).
* - <PERSON> renk Toprak Kahvesi (#A66F3F) olarak değiştirildi (11.05.2025).
* - <PERSON><PERSON> renkler (Rocky Mountain Mavi ve Göl Mavisi) daha fazla kullanıldı (11.05.2025).
* - Gölgelendirmeler ve derinlik efektleri eklendi (11.05.2025).
* - Animasyonlu bileşenler eklendi (11.05.2025).
* - Profil sayfası stilleri eklendi (12.05.2025).
* - Kod tekrarları temizlendi ve optimizasyon yapıldı (12.05.2025).
* - Renk değişkenleri _variables.css dosyasına taşındı (15.05.2025).
*/

/* Renk ve Değişken Tanımları İçin _variables.css Dosyasını İçe Aktar */
@import url('./_variables.css');

/* GENEL SAYFA STİLLERİ */
.modern-section {
  background-color: var(--soft-white);
  background-image: linear-gradient(135deg,
                    rgba(var(--color-sand-rgb), 0.1) 0%,
                    rgba(var(--color-brown-rgb), 0.05) 50%,
                    rgba(var(--color-moss-rgb), 0.08) 100%);
  position: relative;
  overflow: hidden;
  border-bottom: 1px solid rgba(var(--color-brown-rgb), 0.1);
  padding: var(--section-spacing) 0;
}

.modern-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("../img/pattern.svg");
  background-repeat: repeat;
  background-size: 300px;
  opacity: 0.05;
  z-index: 0;
  pointer-events: none;
}

.modern-container {
  position: relative;
  z-index: 1;
  padding: 0 var(--container-padding-x);
}

/* BAŞLIK VE HEADER STİLLERİ */
.modern-header {
  text-align: center;
  padding-bottom: var(--space-xl);
  margin-bottom: var(--space-xl);
}

/* Sonbahar teması için başlık stilleri */
.autumn-heading {
  color: var(--color-brown);
  position: relative;
  display: inline-block;
  padding-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(var(--color-brown-rgb), 0.2);
}

.autumn-heading::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: linear-gradient(to right, var(--color-brown), var(--color-sand));
  border-radius: 3px;
}

.meta-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 1rem 0;
}

.meta-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--color-moss);
  margin: 0 5px;
  display: inline-block;
}

.meta-dot:nth-child(2) {
  width: 12px;
  height: 12px;
  background-color: var(--color-brown);
}

/* Tipografi Stilleri - Gelişmiş Görsel Hiyerarşi */
body {
  font-family: var(--body-font);
  font-weight: var(--font-weight-regular);
  line-height: 1.7;
  color: var(--text-soft);
}

/* Başlık Hiyerarşisi */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--heading-font);
  color: var(--color-brown);
  letter-spacing: -0.02em;
  line-height: 1.3;
  margin-top: 0;
  position: relative;
}

/* Ana Başlık - En Yüksek Hiyerarşi */
h1 {
  font-size: 2.75rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: 1.75rem;
  position: relative;
  color: var(--color-brown);
  text-shadow: 0 2px 4px rgba(var(--color-brown-rgb), 0.1);
}

h1::after {
  content: '';
  position: absolute;
  bottom: -0.6rem;
  left: 0;
  width: 80px;
  height: 4px;
  background: linear-gradient(to right,
              rgba(var(--color-brown-rgb), 1),
              rgba(var(--color-brown-rgb), 0.3));
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(var(--color-brown-rgb), 0.1);
}

/* Alt Başlık - İkinci Hiyerarşi Seviyesi */
h2 {
  font-size: 2.25rem;
  font-weight: var(--font-weight-semibold);
  margin-bottom: 1.5rem;
  position: relative;
  display: inline-block;
  color: var(--color-navy);
}

h2::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(to right,
              rgba(var(--color-navy-rgb), 1),
              rgba(var(--color-navy-rgb), 0.3));
  border-radius: 3px;
}

/* Bölüm Başlığı - Üçüncü Hiyerarşi Seviyesi */
h3 {
  font-size: 1.75rem;
  font-weight: var(--font-weight-semibold);
  margin-bottom: 1.25rem;
  color: var(--color-moss);
  position: relative;
  display: inline-block;
}

h3::after {
  content: '';
  position: absolute;
  bottom: -0.4rem;
  left: 0;
  width: 40px;
  height: 2px;
  background: linear-gradient(to right,
              rgba(var(--color-moss-rgb), 1),
              rgba(var(--color-moss-rgb), 0.3));
  border-radius: 2px;
}

/* Alt Bölüm Başlığı - Dördüncü Hiyerarşi Seviyesi */
h4 {
  font-size: 1.4rem;
  font-weight: var(--font-weight-medium);
  margin-bottom: 1rem;
  color: var(--color-brown);
  position: relative;
  display: inline-block;
}

h4::after {
  content: '';
  position: absolute;
  bottom: -0.3rem;
  left: 0;
  width: 30px;
  height: 2px;
  background-color: rgba(var(--color-brown-rgb), 0.3);
  border-radius: 2px;
}

/* Kart Başlığı - Beşinci Hiyerarşi Seviyesi */
h5 {
  font-size: 1.2rem;
  font-weight: var(--font-weight-medium);
  margin-bottom: 0.75rem;
  color: var(--color-navy);
}

/* En Küçük Başlık - Altıncı Hiyerarşi Seviyesi */
h6 {
  font-size: 1.1rem;
  font-weight: var(--font-weight-medium);
  margin-bottom: 0.5rem;
  color: var(--text-soft);
  letter-spacing: 0.5px;
}

/* Merkezi Başlıklar İçin Özel Stil */
.text-center h1::after,
.text-center h2::after {
  left: 50%;
  transform: translateX(-50%);
}

/* Başlık Varyasyonları */
.heading-underline-none::after {
  display: none;
}

.heading-highlight {
  display: inline-block;
  background: linear-gradient(to bottom, transparent 60%, rgba(var(--color-sand-rgb), 0.4) 60%);
  padding: 0 5px;
}

.heading-box {
  display: inline-block;
  background-color: rgba(var(--color-brown-rgb), 0.1);
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius-md);
  box-shadow: 0 2px 5px rgba(var(--color-brown-rgb), 0.1);
}

/* Paragraf Stilleri */
p {
  font-family: var(--body-font);
  font-weight: var(--font-weight-regular);
  line-height: 1.7;
  margin-bottom: 1rem;
}

/* Öne Çıkan Metin */
.lead {
  font-size: 1.25rem;
  font-weight: var(--font-weight-light);
  line-height: 1.8;
  color: var(--color-navy);
  margin-bottom: 1.5rem;
  border-left: 3px solid var(--color-sand);
  padding-left: 1rem;
}

/* Metin Varyasyonları */
.text-small {
  font-size: 0.875rem;
}

.text-large {
  font-size: 1.125rem;
  line-height: 1.8;
}

.text-muted {
  color: var(--text-muted);
}

.text-highlight {
  background-color: rgba(var(--color-sand-rgb), 0.2);
  padding: 0.1em 0.3em;
  border-radius: 3px;
}

.text-important {
  font-weight: var(--font-weight-semibold);
  color: var(--color-brown);
}

.text-callout {
  display: block;
  padding: 1.25rem;
  background-color: rgba(var(--color-moss-rgb), 0.05);
  border: 1px solid rgba(var(--color-moss-rgb), 0.1);
  margin: 1.5rem 0;
  border-radius: var(--border-radius-md);
  position: relative;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
}

.text-callout::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background-color: var(--color-moss);
}

/* KAYDIRMA ÇUBUĞU STİLLERİ */
/* Tüm kaydırma çubukları için ortak stiller */
.similar-calismalar,
.modern-sidebar-list,
.kategori-listesi,
.modern-tag-cloud {
  overflow-y: auto;
  scrollbar-width: thin;
}

.similar-calismalar,
.modern-sidebar-list {
  max-height: 350px;
}

.kategori-listesi {
  max-height: 250px;
  border-radius: 8px;
}

.modern-tag-cloud {
  max-height: 120px;
  padding-right: 5px;
}

/* Webkit tarayıcılar için kaydırma çubuğu stilleri */
.similar-calismalar::-webkit-scrollbar,
.modern-sidebar-list::-webkit-scrollbar,
.kategori-listesi::-webkit-scrollbar,
.modern-tag-cloud::-webkit-scrollbar {
  width: 8px;
}

.similar-calismalar::-webkit-scrollbar-track,
.modern-sidebar-list::-webkit-scrollbar-track,
.kategori-listesi::-webkit-scrollbar-track,
.modern-tag-cloud::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.similar-calismalar::-webkit-scrollbar-thumb,
.modern-sidebar-list::-webkit-scrollbar-thumb,
.kategori-listesi::-webkit-scrollbar-thumb,
.modern-tag-cloud::-webkit-scrollbar-thumb {
  background: var(--color-moss);
  border-radius: 10px;
}

/* Similar çalışma öğelerinin stilini düzenle */
.similar-calisma-item {
  display: flex !important;
  padding: 10px !important;
  text-decoration: none !important;
  color: var(--text-color) !important;
  transition: all 0.3s ease !important;
  border-bottom: 1px solid rgba(var(--light-accent-rgb), 0.2) !important;
}

.similar-calisma-item:hover {
  background-color: rgba(var(--color-moss-rgb), 0.1) !important;
}

.similar-calisma-item:last-child {
  border-bottom: none !important;
}

/* KART BİLEŞENİ */
.modern-card,
.sidebar-card,
.benzer-calismalar-karti,
.urun-bilgi-karti,
.etiketler-karti,
.benzer-urunler-karti {
  background-color: var(--soft-light);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  border: 1px solid rgba(var(--color-brown-rgb), 0.1);
  box-shadow: 0 15px 35px rgba(var(--color-brown-rgb), 0.15),
              0 8px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  height: auto !important;
  min-height: 0 !important;
  margin-bottom: var(--space-lg);
  position: relative;
}

/* Kişisel Bilgiler Kartı - Özellikli Kart */
.personal-info-card {
  background: linear-gradient(135deg,
              rgba(var(--color-sand-rgb), 0.3) 0%,
              rgba(var(--color-brown-rgb), 0.1) 100%);
  border: none;
  box-shadow: 0 15px 35px rgba(var(--color-brown-rgb), 0.15),
              0 8px 20px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}

.personal-info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("../img/pattern.svg");
  background-repeat: repeat;
  background-size: 200px;
  opacity: 0.05;
  z-index: 0;
  pointer-events: none;
}

.personal-info-card .modern-card-header {
  background: linear-gradient(to right, var(--color-brown), rgba(var(--color-brown-rgb), 0.7));
  color: var(--soft-white);
  border-bottom: none;
  padding: var(--space-lg) var(--space-lg);
  position: relative;
  z-index: 1;
}

.personal-info-card .modern-card-header h3 {
  color: var(--soft-white);
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.personal-info-card .modern-card-header i {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  color: var(--soft-white);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  padding: 6px;
  font-size: 1.1rem;
}

.personal-info-card .modern-card-body {
  position: relative;
  z-index: 1;
  padding: var(--space-xl) var(--space-lg);
}

.modern-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 35px rgba(var(--color-brown-rgb), 0.15),
              0 8px 20px rgba(0, 0, 0, 0.08);
}

.modern-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: var(--border-radius-lg);
  box-shadow: 0 5px 15px rgba(var(--color-brown-rgb), 0.2);
  opacity: 0;
  transition: opacity 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  pointer-events: none;
}

.modern-card:hover::after {
  opacity: 1;
}

/* Mavi kartlar */
.modern-card-navy {
  border: 1px solid rgba(var(--color-navy-rgb), 0.1);
}

/* INLINE STİLLERİ KALDIRMAK İÇİN EKLENEN SINIFLAR */

/* Login Modal Stilleri */
.login-modal-bg {
  background-color: rgba(0, 0, 0, 0.5);
}

.login-modal-content {
  background-color: white !important;
  opacity: 1 !important;
}

.login-modal-input {
  background-color: white !important;
  opacity: 1 !important;
}

.modal-footer-light {
  background-color: var(--soft-light) !important;
}

/* Profil Resmi Stilleri */
.profile-image-circle {
  width: 150px;
  height: 150px;
  object-fit: cover;
}

.profile-placeholder-circle {
  width: 150px;
  height: 150px;
  background-color: #f0f0f0;
  margin: 0 auto;
}

.profile-placeholder-icon {
  font-size: 4rem;
  color: #aaa;
}

/* Uyarı İkonu Stilleri */
.warning-icon-large {
  font-size: 3rem;
  color: #ddd;
}

/* Atölye Kartları İkon Stilleri */
.workshop-icon {
  font-size: 2rem;
}

/* WhatsApp İkon Stilleri */
.whatsapp-icon-bg {
  background-color: rgba(var(--color-sky-rgb), 0.8);
}

/* Google Maps İframe Stilleri */
.map-iframe {
  border: 0;
  width: 100%;
  height: 450px;
}
  box-shadow: 0 10px 30px rgba(var(--color-navy-rgb), 0.1),
              0 5px 15px rgba(0, 0, 0, 0.05);
}

.modern-card-navy:hover {
  box-shadow: 0 15px 35px rgba(var(--color-navy-rgb), 0.15),
              0 8px 20px rgba(0, 0, 0, 0.08);
}

.modern-card-navy::after {
  box-shadow: 0 5px 15px rgba(var(--color-navy-rgb), 0.2);
}

.modern-card-sky {
  border: 1px solid rgba(var(--color-sky-rgb), 0.1);
  box-shadow: 0 10px 30px rgba(var(--color-sky-rgb), 0.1),
              0 5px 15px rgba(0, 0, 0, 0.05);
}

.modern-card-sky:hover {
  box-shadow: 0 15px 35px rgba(var(--color-sky-rgb), 0.15),
              0 8px 20px rgba(0, 0, 0, 0.08);
}

.modern-card-sky::after {
  box-shadow: 0 5px 15px rgba(var(--color-sky-rgb), 0.2);
}

.modern-card-gradient {
  background: linear-gradient(135deg, var(--color-navy) 0%, var(--color-sky) 100%);
  border: none;
  color: white;
  box-shadow: 0 10px 30px rgba(var(--color-navy-rgb), 0.2),
              0 5px 15px rgba(0, 0, 0, 0.1);
}

.modern-card-gradient:hover {
  box-shadow: 0 15px 35px rgba(var(--color-navy-rgb), 0.25),
              0 8px 20px rgba(0, 0, 0, 0.15);
  transform: translateY(-8px);
}

/* Kart Başlık ve İçerik Alanları - Gelişmiş Görsel Hiyerarşi */
.modern-card-header,
.sidebar-header {
  padding: var(--space-lg);
  border-bottom: 1px solid rgba(var(--color-brown-rgb), 0.1);
  position: relative;
  overflow: hidden;
  background-color: rgba(var(--color-brown-rgb), 0.05);
}

.modern-card-header h3,
.sidebar-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: var(--font-weight-semibold);
  color: var(--color-brown);
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.modern-card-header i,
.sidebar-header i {
  color: var(--color-brown);
  font-size: 1.2rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background-color: rgba(var(--color-brown-rgb), 0.1);
  border-radius: 50%;
  padding: 6px;
}

.modern-card-body {
  padding: var(--space-lg);
}

/* Kart Başlık Altı Çizgisi - Animasyonlu */
.modern-card-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(to right, var(--color-brown), var(--color-navy));
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.modern-card:hover .modern-card-header::after {
  transform: scaleX(1);
}

/* Kart İçerik Vurgulamaları */
.modern-card-highlight {
  background-color: rgba(var(--color-sand-rgb), 0.1);
  border-left: 3px solid var(--color-sand);
  padding: var(--space-md);
  margin: var(--space-md) 0;
  border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
}

.modern-card-info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: var(--space-xs) 0;
  border-bottom: 1px dashed rgba(var(--color-brown-rgb), 0.1);
}

.modern-card-info-item:last-child {
  border-bottom: none;
}

.modern-card-info-label {
  font-weight: var(--font-weight-medium);
  color: var(--color-navy);
  min-width: 120px;
}

.modern-card-info-value {
  color: var(--text-soft);
}

/* Sonbahar teması için kart başlıkları */
.autumn-card-header {
  background: linear-gradient(to right, var(--color-brown), rgba(var(--color-brown-rgb), 0.7));
  color: var(--soft-white);
  border-bottom: none;
  padding: 1.5rem;
  position: relative;
  z-index: 1;
}

.autumn-card-header h3 {
  color: var(--soft-white);
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.autumn-card-header i {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  color: var(--soft-white);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  padding: 6px;
  font-size: 1.1rem;
}

.autumn-card-header-alt {
  background: linear-gradient(to right, var(--color-navy), rgba(var(--color-navy-rgb), 0.7));
  color: var(--soft-white);
  border-bottom: none;
  padding: 1.5rem;
  position: relative;
  z-index: 1;
}

.autumn-card-header-alt h3 {
  color: var(--soft-white);
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.autumn-card-header-alt i {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  color: var(--soft-white);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  padding: 6px;
  font-size: 1.1rem;
}

.autumn-card-header-gradient {
  background: linear-gradient(135deg, var(--color-navy) 0%, var(--color-sky) 100%);
  color: var(--soft-white);
  border-bottom: none;
  padding: 1.5rem;
  position: relative;
  z-index: 1;
}

.autumn-card-header-gradient h3 {
  color: var(--soft-white);
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.autumn-card-header-gradient i {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  color: var(--soft-white);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  padding: 6px;
  font-size: 1.1rem;
}

/* Yönetim Paneli Stilleri */
.admin-panel-card {
  border: none;
  box-shadow: 0 15px 35px rgba(var(--color-brown-rgb), 0.15),
              0 8px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
  background-color: rgba(var(--color-sand-rgb), 0.1);
  border: 1px solid rgba(var(--color-brown-rgb), 0.1);
}

.admin-panel-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(var(--color-brown-rgb), 0.2),
              0 12px 25px rgba(0, 0, 0, 0.12);
}

.admin-panel-header {
  background: linear-gradient(135deg, var(--color-brown) 0%, var(--color-sand) 100%);
  color: var(--white);
  padding: 1.25rem 1.5rem;
  border-bottom: none;
  position: relative;
  z-index: 1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.admin-panel-header h3 {
  color: var(--white);
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
}

.admin-panel-header h3 i {
  margin-right: 0.5rem;
  font-size: 1.2rem;
}

.admin-menu {
  display: flex;
  flex-direction: column;
}

.admin-menu-item {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  text-decoration: none;
  color: var(--text-color);
  border-bottom: 1px solid rgba(var(--color-navy-rgb), 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.admin-menu-item:last-child {
  border-bottom: none;
}

.admin-menu-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 5px;
  height: 100%;
  background-color: var(--color-brown);
  transform: translateX(-5px);
  transition: transform 0.3s ease;
}

.admin-menu-item:hover {
  background-color: rgba(var(--color-sand-rgb), 0.15);
  color: var(--color-brown);
}

.admin-menu-item:hover::before {
  transform: translateX(0);
}

.admin-menu-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--color-brown);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  color: var(--white);
  font-size: 1.2rem;
  flex-shrink: 0;
  transition: all 0.3s ease;
  box-shadow: 0 4px 8px rgba(var(--color-brown-rgb), 0.3);
}

.admin-menu-item:hover .admin-menu-icon {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 6px 12px rgba(var(--color-brown-rgb), 0.4);
}

.admin-menu-content {
  flex-grow: 1;
}

.admin-menu-content h5 {
  margin: 0 0 0.25rem;
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-brown);
  transition: all 0.3s ease;
}

.admin-menu-item:hover .admin-menu-content h5 {
  color: var(--color-brown);
  transform: translateX(5px);
}

.admin-menu-content p {
  margin: 0;
  font-size: 0.8rem;
  color: var(--text-muted);
  transition: all 0.3s ease;
}

.admin-menu-item:hover .admin-menu-content p {
  transform: translateX(5px);
}

.admin-menu-arrow {
  color: var(--color-brown);
  font-size: 1.2rem;
  opacity: 0.5;
  transition: all 0.3s ease;
}

.admin-menu-item:hover .admin-menu-arrow {
  opacity: 1;
  transform: translateX(5px);
  color: var(--color-brown);
}

/* Özel ikon renkleri */
.edit-icon {
  background-color: var(--color-navy);
  box-shadow: 0 4px 8px rgba(var(--color-navy-rgb), 0.3);
}

.admin-menu-item:hover .edit-icon {
  box-shadow: 0 6px 12px rgba(var(--color-navy-rgb), 0.4);
}

.photo-icon {
  background-color: var(--color-moss);
  box-shadow: 0 4px 8px rgba(var(--color-moss-rgb), 0.3);
}

.admin-menu-item:hover .photo-icon {
  box-shadow: 0 6px 12px rgba(var(--color-moss-rgb), 0.4);
}

.delete-icon {
  background-color: var(--color-brown);
  box-shadow: 0 4px 8px rgba(var(--color-brown-rgb), 0.3);
}

.admin-menu-item:hover .delete-icon {
  box-shadow: 0 6px 12px rgba(var(--color-brown-rgb), 0.4);
}

.back-icon {
  background-color: var(--color-navy);
  box-shadow: 0 4px 8px rgba(var(--color-navy-rgb), 0.3);
}

.admin-menu-item:hover .back-icon {
  box-shadow: 0 6px 12px rgba(var(--color-navy-rgb), 0.4);
}

.delete-item {
  color: var(--color-brown);
}

.delete-item:hover {
  background-color: rgba(var(--color-brown-rgb), 0.05);
}

.featured-card-header {
  background: linear-gradient(to right, var(--color-brown), rgba(var(--color-brown-rgb), 0.7));
  color: var(--soft-white);
  border-bottom: none;
  padding: 1.5rem;
  position: relative;
  z-index: 1;
}

.featured-card-header h3 {
  color: var(--soft-white);
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.featured-card-header i {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  color: var(--soft-white);
}

.modern-card-body {
  padding: 1.5rem;
  height: auto !important;
}

.sidebar-body {
  padding: 0 !important;
  height: auto !important;
}

/* Kategori ve etiket kartları için özelleştirilmiş stiller */

/* Etiket ve badge stilleri */
.modern-tag-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 0;
  padding: 5px;
}

.modern-tag,
.modern-badge {
  margin-bottom: 5px !important;
}

/* Sonbahar teması için etiketler */
.autumn-tag {
  background-color: rgba(var(--color-moss-rgb), 0.15);
  color: var(--color-moss);
  border: 1px solid rgba(var(--color-moss-rgb), 0.3);
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.autumn-tag:hover {
  background-color: var(--color-moss);
  color: var(--soft-white);
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(var(--color-moss-rgb), 0.3);
}

/* Sonbahar teması için butonlar */
.autumn-like-btn {
  background-color: rgba(var(--color-brown-rgb), 0.1);
  color: var(--color-brown);
  border: 1px solid rgba(var(--color-brown-rgb), 0.2);
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.autumn-like-btn:hover,
.autumn-like-btn.active {
  background-color: var(--color-brown);
  color: var(--soft-white);
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(var(--color-brown-rgb), 0.3);
}

.autumn-nav-btn {
  background-color: rgba(var(--color-navy-rgb), 0.1);
  color: var(--color-navy);
  border: 1px solid rgba(var(--color-navy-rgb), 0.2);
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.autumn-nav-btn:hover {
  background-color: var(--color-navy);
  color: var(--soft-white);
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(var(--color-navy-rgb), 0.3);
}

/* Sonbahar teması için bilgi kartları */
.autumn-info-card {
  background: linear-gradient(135deg,
              rgba(var(--color-sand-rgb), 0.3) 0%,
              rgba(var(--color-brown-rgb), 0.1) 100%);
  border: none;
  box-shadow: 0 15px 35px rgba(var(--color-brown-rgb), 0.15),
              0 8px 20px rgba(0, 0, 0, 0.08);
}

.autumn-icon {
  background-color: rgba(var(--color-brown-rgb), 0.15);
  color: var(--color-brown);
  transition: all 0.3s ease;
}

.info-item:hover .autumn-icon {
  background-color: var(--color-brown);
  color: var(--white);
  transform: scale(1.1);
}

/* Beğeni ikonu için özel stiller */
.like-icon {
  cursor: pointer !important;
  transition: all 0.3s ease;
  position: relative;
  z-index: 10;
}

.like-icon:hover {
  background-color: rgba(var(--color-brown-rgb), 0.3) !important;
  transform: scale(1.2) !important;
}

.like-icon.active {
  background-color: var(--color-brown) !important;
  color: var(--white) !important;
}

.like-icon.active:hover {
  background-color: var(--color-brown) !important;
  opacity: 0.9;
}

/* Beğeni ikonu üzerine gelindiğinde diğer ikonların hover efektini devre dışı bırak */
.info-item:hover .like-icon:not(:hover) {
  background-color: rgba(var(--color-brown-rgb), 0.15);
  color: var(--color-brown);
  transform: none;
}

/* Beğeni ikonu için özel animasyon */
@keyframes heartBeat {
  0% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.3);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.3);
  }
  70% {
    transform: scale(1);
  }
}

.like-icon:active {
  animation: heartBeat 1s;
}

/* Sonbahar teması için benzer çalışmalar */
.autumn-sidebar-item {
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.autumn-sidebar-item:hover {
  border-left: 3px solid var(--color-brown);
  background-color: rgba(var(--color-brown-rgb), 0.05);
}

.autumn-thumbnail {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.autumn-sidebar-item:hover .autumn-thumbnail {
  border: 2px solid var(--color-brown);
  transform: scale(1.05);
}

.autumn-title {
  color: var(--color-brown);
  transition: all 0.3s ease;
}

.autumn-sidebar-item:hover .autumn-title {
  color: var(--color-moss);
}

/* Sonbahar teması için aksiyon butonları */
.autumn-action-btn {
  transition: all 0.3s ease;
}

.autumn-action-btn:hover {
  transform: translateY(-5px);
}

.autumn-icon-circle {
  background-color: rgba(255, 255, 255, 0.2);
  color: var(--soft-white);
  transition: all 0.3s ease;
}

.autumn-action-btn:hover .autumn-icon-circle {
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* Sonbahar teması için ilerleme çubuğu */
.autumn-progress {
  height: 10px;
  background-color: rgba(var(--color-navy-rgb), 0.1);
  border-radius: 10px;
  overflow: hidden;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.autumn-progress .progress-bar {
  background: linear-gradient(to right, var(--color-brown), var(--color-sand));
  box-shadow: 0 0 10px rgba(var(--color-brown-rgb), 0.5);
  transition: width 1.5s ease;
}

/* BİLGİ ÖĞELER */
.info-item {
  display: flex;
  align-items: start;
  margin-bottom: 1.25rem;
}

.info-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: rgba(var(--color-moss-rgb), 0.15);
  color: var(--color-moss);
  border-radius: 50%;
  margin-right: 1rem;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.info-item:hover .info-icon {
  background-color: var(--color-moss);
  color: var(--white);
  transform: scale(1.1);
}

.info-content {
  flex-grow: 1;
}

.info-title {
  display: block;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 0.25rem;
}

.info-value {
  display: block;
  color: var(--text-color);
}

/* SOSYAL MEDYA */
/* Not: Profil sayfası için özel sosyal medya stilleri aşağıda tanımlanmıştır */
.social-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--color-brown);
  color: var(--soft-white);
  text-decoration: none;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 4px 10px rgba(var(--color-brown-rgb), 0.2),
              0 2px 5px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  z-index: 1;
  margin: 0 0.5rem;
}

.social-link:hover {
  background-color: var(--color-moss);
  transform: translateY(-5px) scale(1.1);
  text-decoration: none;
  color: var(--soft-white);
  box-shadow: 0 8px 20px rgba(var(--color-moss-rgb), 0.3),
              0 4px 10px rgba(0, 0, 0, 0.15);
}

/* Yosun yeşili sosyal medya ikonları */
.social-link.moss-icon {
  background-color: var(--color-moss);
  color: var(--soft-white);
  box-shadow: 0 4px 10px rgba(var(--color-moss-rgb), 0.2),
              0 2px 5px rgba(0, 0, 0, 0.1);
}

.social-link.moss-icon:hover {
  background-color: var(--color-navy);
  box-shadow: 0 8px 20px rgba(var(--color-navy-rgb), 0.3),
              0 4px 10px rgba(0, 0, 0, 0.15);
}

.social-link::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 70%);
  opacity: 0;
  transform: scale(2);
  transition: opacity 0.4s ease, transform 0.6s ease;
  pointer-events: none;
}

.social-link:hover::after {
  opacity: 0.2;
  transform: scale(1);
}

.modern-btn-circle {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--color-brown);
  color: var(--soft-white);
  text-decoration: none;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 4px 10px rgba(var(--color-brown-rgb), 0.3),
              0 2px 5px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.modern-btn-circle:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 8px 20px rgba(var(--color-brown-rgb), 0.4),
              0 4px 10px rgba(0, 0, 0, 0.15);
  background: var(--color-moss);
  color: var(--soft-white);
  text-decoration: none;
}

.modern-btn-circle::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 70%);
  opacity: 0;
  transform: scale(2);
  transition: opacity 0.4s ease, transform 0.6s ease;
  pointer-events: none;
}

.modern-btn-circle:hover::after {
  opacity: 0.2;
  transform: scale(1);
}

/* FORM KONTROLLARI */
.modern-form .form-control {
  border: 1px solid rgba(var(--color-moss-rgb), 0.3);
  padding: 0.75rem 1rem;
  border-radius: 10px;
  transition: all 0.3s ease;
  background-color: var(--soft-light);
  color: var(--text-soft);
}

.modern-form .form-control:focus {
  border-color: var(--color-moss);
  box-shadow: 0 0 0 0.15rem rgba(var(--color-moss-rgb), 0.1);
  background-color: var(--soft-white);
}

.modern-form .form-label {
  color: var(--color-moss);
  font-weight: 600;
  margin-bottom: 0.5rem;
  opacity: 0.9;
}

/* BAŞLIK VE METİNLER */
.modern-title {
  color: var(--color-navy);
  font-weight: 700;
  margin-bottom: 1.5rem;
  position: relative;
  display: inline-block;
  padding-bottom: 10px;
  letter-spacing: -0.02em;
  text-shadow: 0 1px 1px rgba(var(--color-navy-rgb), 0.05);
}

/* Aksiyon Butonları - Minimalist Görsel Hiyerarşi */
.modern-btn {
  display: inline-block;
  background: transparent;
  color: var(--color-brown);
  border: 2px solid var(--color-brown);
  padding: 0.75rem 2rem;
  border-radius: 30px;
  font-weight: var(--font-weight-semibold);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 4px 12px rgba(var(--color-brown-rgb), 0.1),
              0 2px 6px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
  z-index: 1;
  text-decoration: none;
  letter-spacing: 0.5px;
  text-align: center;
}

.modern-btn:hover {
  background: rgba(var(--color-brown-rgb), 0.1);
  color: var(--color-brown);
  border-color: var(--color-brown);
  box-shadow: 0 8px 25px rgba(var(--color-brown-rgb), 0.2),
              0 4px 10px rgba(0, 0, 0, 0.1);
  transform: translateY(-5px);
  text-decoration: none;
}

.modern-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 70%);
  opacity: 0;
  transform: scale(2);
  transition: opacity 0.4s ease, transform 0.6s ease;
  pointer-events: none;
}

.modern-btn:hover::after {
  opacity: 0.15;
  transform: scale(1);
}

/* Birincil Aksiyon Butonu - En Yüksek Vurgu */
.modern-btn-primary {
  background: var(--color-brown);
  color: var(--soft-white);
  border: 2px solid var(--color-brown);
  box-shadow: 0 4px 12px rgba(var(--color-brown-rgb), 0.3),
              0 2px 6px rgba(0, 0, 0, 0.1);
  font-weight: var(--font-weight-bold);
}

.modern-btn-primary:hover {
  background: var(--soft-white);
  color: var(--color-brown);
  border-color: var(--color-brown);
  box-shadow: 0 8px 25px rgba(var(--color-brown-rgb), 0.4),
              0 4px 10px rgba(0, 0, 0, 0.15);
}

/* Başarı Butonu - İkincil Vurgu */
.modern-btn-success {
  background: var(--color-moss);
  color: var(--soft-white);
  border: 2px solid var(--color-moss);
  box-shadow: 0 4px 12px rgba(var(--color-moss-rgb), 0.3),
              0 2px 6px rgba(0, 0, 0, 0.1);
}

.modern-btn-success:hover {
  background: var(--soft-white);
  color: var(--color-moss);
  border-color: var(--color-moss);
  box-shadow: 0 8px 25px rgba(var(--color-moss-rgb), 0.4),
              0 4px 10px rgba(0, 0, 0, 0.15);
}

/* Vurgulu Buton - Dikkat Çekici */
.modern-btn-accent {
  background: linear-gradient(135deg, var(--color-brown) 0%, var(--color-sand) 100%);
  color: var(--soft-white);
  border: none;
  box-shadow: 0 4px 15px rgba(var(--color-brown-rgb), 0.4),
              0 2px 8px rgba(0, 0, 0, 0.1);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.modern-btn-accent:hover {
  background: linear-gradient(135deg, var(--color-sand) 0%, var(--color-brown) 100%);
  color: var(--soft-white);
  border: none;
  transform: translateY(-5px) scale(1.03);
  box-shadow: 0 8px 25px rgba(var(--color-brown-rgb), 0.5),
              0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Buton Boyutları */
.modern-btn-lg {
  padding: 1rem 2.5rem;
  font-size: 1.1rem;
  letter-spacing: 0.8px;
}

.modern-btn-sm {
  padding: 0.5rem 1.25rem;
  font-size: 0.9rem;
}

.modern-btn-xs {
  padding: 0.35rem 1rem;
  font-size: 0.8rem;
  border-width: 1px;
}

/* RESİM KONTEYNER */
.image-container {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(var(--primary-color-rgb), 0.1);
  margin-bottom: 1rem; /* Resim ile içerik arasına boşluk */
}

.modern-image {
  width: 100%;
  height: auto;
  transition: all 0.5s ease;
}

.image-container:hover .modern-image {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 1rem;
  background: linear-gradient(to top, rgba(var(--color-moss-rgb), 0.9), transparent);
  color: var(--white);
}

/* HERO SECTION */
.hero-section {
  position: relative;
  height: 100vh;
  min-height: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  padding: 0;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(rgba(var(--color-navy-rgb), 0.7), rgba(var(--color-moss-rgb), 0.7));
  z-index: 1;
}

.hero-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 0;
}

.hero-title {
  font-size: 4rem;
  font-weight: 700;
  color: var(--soft-white);
  margin-bottom: 1rem;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  position: relative;
  z-index: 2;
}

.hero-subtitle {
  font-size: 1.5rem;
  color: var(--soft-white);
  margin-bottom: 2rem;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  position: relative;
  z-index: 2;
}

.hero-btn {
  padding: 0.75rem 2.5rem;
  font-size: 1.1rem;
  background: var(--color-moss);
  color: var(--soft-white);
  border: 2px solid var(--color-moss);
  box-shadow: 0 4px 15px rgba(var(--color-moss-rgb), 0.3);
  position: relative;
  z-index: 2;
}

.hero-btn:hover {
  background: var(--color-navy);
  border-color: var(--color-navy);
  box-shadow: 0 6px 20px rgba(var(--color-navy-rgb), 0.4);
}

/* SITE HEADER (NAVİGASYON) */
.modern-header.sticky-top {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(var(--color-brown-rgb), 0.1);
  border-bottom: 1px solid rgba(var(--color-brown-rgb), 0.08);
  padding: 15px 0;
  transition: all 0.4s ease;
  z-index: 100;
}

.modern-header.scrolled {
  background-color: rgba(255, 255, 255, 0.98);
  box-shadow: 0 6px 25px rgba(var(--color-brown-rgb), 0.15);
}

.modern-logo {
  color: var(--color-brown);
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  font-family: var(--heading-font);
  letter-spacing: -0.5px;
  text-shadow: 2px 2px 4px rgba(var(--color-brown-rgb), 0.2);
  position: relative;
  display: inline-block;
}

.modern-logo::after {
  content: '';
  position: absolute;
  bottom: -3px;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(to right,
              rgba(var(--color-brown-rgb), 0.7),
              rgba(var(--color-brown-rgb), 0.3) 70%,
              rgba(var(--color-brown-rgb), 0));
  border-radius: 2px;
}

.modern-nav ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.modern-nav ul li {
  margin: 0 15px;
}

.modern-nav ul li a {
  color: var(--text-soft);
  font-weight: 500;
  text-decoration: none;
  padding: 10px 0;
  position: relative;
  transition: all 0.3s ease;
}

.modern-nav ul li a:hover,
.modern-nav ul li a.active {
  color: var(--color-brown);
  text-decoration: none;
}

.modern-nav ul li a::after {
  content: '';
  position: absolute;
  bottom: 5px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--color-brown);
  transition: all 0.3s ease;
}

.modern-nav ul li a:hover::after,
.modern-nav ul li a.active::after {
  width: 100%;
}

.modern-auth-buttons {
  display: flex;
  gap: 10px;
}

/* Navbar içindeki auth butonları */
.nav-auth-item {
  margin-left: 10px !important;
}

.nav-auth-link {
  display: flex !important;
  align-items: center;
  padding: 8px 15px !important;
  border-radius: 30px !important;
  background-color: transparent;
  color: var(--color-brown) !important;
  border: 1px solid rgba(var(--color-brown-rgb), 0.3);
  transition: all 0.3s ease;
  text-decoration: none;
}

.nav-auth-link i {
  font-size: 1.1rem;
  margin-right: 8px;
}

.nav-auth-link:hover {
  background-color: var(--color-moss) !important;
  color: var(--soft-white) !important;
  transform: translateY(-2px);
  box-shadow: 0 3px 8px rgba(var(--color-moss-rgb), 0.3);
}

.nav-auth-link.nav-auth-outline {
  background-color: transparent;
  color: var(--color-brown) !important;
  border: 1px solid rgba(var(--color-brown-rgb), 0.5);
}

.nav-auth-link.nav-auth-outline:hover {
  background-color: var(--color-brown) !important;
  color: var(--soft-white) !important;
}

/* Giriş butonu için minimalist stil */
.nav-auth-link.nav-auth-highlight {
  background-color: transparent;
  color: var(--color-brown) !important;
  border: 1px solid var(--color-brown);
  box-shadow: 0 2px 8px rgba(var(--color-brown-rgb), 0.1);
}

.nav-auth-link.nav-auth-highlight:hover {
  background-color: rgba(var(--color-brown-rgb), 0.1) !important;
  border-color: var(--color-brown);
  color: var(--color-brown) !important;
  box-shadow: 0 4px 15px rgba(var(--color-brown-rgb), 0.2);
}

/* Kayıt ol butonu için minimalist yeşil stil */
.nav-auth-link.nav-auth-moss {
  background-color: transparent;
  color: var(--color-moss) !important;
  border: 1px solid var(--color-moss);
  box-shadow: 0 2px 8px rgba(var(--color-moss-rgb), 0.1);
}

.nav-auth-link.nav-auth-moss:hover {
  background-color: rgba(var(--color-moss-rgb), 0.1) !important;
  color: var(--color-moss) !important;
  border-color: var(--color-moss);
  box-shadow: 0 4px 15px rgba(var(--color-moss-rgb), 0.2);
}

/* Tooltip Stilleri */
.tooltip {
  position: absolute;
  z-index: 1070;
  display: block;
  margin: 0;
  font-family: var(--body-font);
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  white-space: normal;
  line-break: auto;
  font-size: 0.875rem;
  word-wrap: break-word;
  opacity: 0;
}

.tooltip.show {
  opacity: 0.9;
}

.tooltip .tooltip-inner {
  max-width: 200px;
  padding: 0.25rem 0.5rem;
  color: var(--soft-white);
  text-align: center;
  background-color: var(--color-brown);
  border-radius: 0.25rem;
}

.bs-tooltip-top .tooltip-arrow::before,
.bs-tooltip-auto[data-popper-placement^=top] .tooltip-arrow::before {
  border-top-color: var(--color-brown);
}

.bs-tooltip-bottom .tooltip-arrow::before,
.bs-tooltip-auto[data-popper-placement^=bottom] .tooltip-arrow::before {
  border-bottom-color: var(--color-brown);
}

/* Normal Butonlar */
.modern-btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
}

.modern-btn-outline {
  background: transparent;
  color: var(--color-brown);
  border: 2px solid var(--color-brown);
  box-shadow: 0 4px 12px rgba(var(--color-brown-rgb), 0.1),
              0 2px 6px rgba(0, 0, 0, 0.03);
}

.modern-btn-outline:hover {
  background: rgba(var(--color-brown-rgb), 0.1);
  color: var(--color-brown);
  box-shadow: 0 8px 25px rgba(var(--color-brown-rgb), 0.2),
              0 4px 10px rgba(0, 0, 0, 0.1);
  transform: translateY(-3px);
}

/* Mobil görünümdeki çıkış butonu */
.mobile-auth-buttons .modern-btn-outline {
  background: transparent;
  color: var(--color-brown);
  border: 2px solid var(--color-brown);
  box-shadow: 0 4px 12px rgba(var(--color-brown-rgb), 0.1),
              0 2px 6px rgba(0, 0, 0, 0.03);
}

.mobile-auth-buttons .modern-btn-outline:hover {
  background: rgba(var(--color-brown-rgb), 0.1);
  color: var(--color-brown);
  box-shadow: 0 8px 25px rgba(var(--color-brown-rgb), 0.2),
              0 4px 10px rgba(0, 0, 0, 0.1);
}

/* Responsive butonlar */
.modern-btn i {
  margin-right: 8px;
}

.modern-btn.icon-only i {
  margin-right: 0;
}

/* Mobil görünümde butonlar */
.mobile-auth-buttons .modern-btn {
  width: 100%;
  margin-bottom: 12px;
  justify-content: center;
  display: flex;
  align-items: center;
  padding: 12px 20px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  text-decoration: none;
}

/* Mobil görünümde buton efektleri */
.mobile-auth-buttons .modern-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

/* Mobil görünümde buton ikonları */
.mobile-auth-buttons .modern-btn i {
  font-size: 1.1rem;
  margin-right: 10px;
}

/* Giriş butonu için minimalist stil */
.modern-btn-brown {
  background-color: transparent !important;
  color: var(--color-brown) !important;
  border: 2px solid var(--color-brown);
  box-shadow: 0 4px 12px rgba(var(--color-brown-rgb), 0.1),
              0 2px 6px rgba(0, 0, 0, 0.03);
}

.modern-btn-brown:hover {
  background-color: rgba(var(--color-brown-rgb), 0.1) !important;
  color: var(--color-brown) !important;
  border-color: var(--color-brown);
  box-shadow: 0 8px 25px rgba(var(--color-brown-rgb), 0.2),
              0 4px 10px rgba(0, 0, 0, 0.1);
  transform: translateY(-3px);
}

/* Mobil görünümdeki giriş butonu */
.mobile-auth-buttons .modern-btn-brown {
  background-color: transparent !important;
  color: var(--color-brown) !important;
  border: 2px solid var(--color-brown);
  box-shadow: 0 4px 12px rgba(var(--color-brown-rgb), 0.1),
              0 2px 6px rgba(0, 0, 0, 0.03);
}

.mobile-auth-buttons .modern-btn-brown:hover {
  background-color: rgba(var(--color-brown-rgb), 0.1) !important;
  color: var(--color-brown) !important;
  border-color: var(--color-brown);
  box-shadow: 0 8px 25px rgba(var(--color-brown-rgb), 0.2),
              0 4px 10px rgba(0, 0, 0, 0.1);
}

/* Tablet ve mobil cihazlarda navbar düzenlemeleri */
@media (max-width: 1199px) {
  .mobile-nav-toggle {
    display: block;
    font-size: 24px;
  }

  #navmenu {
    position: fixed;
    top: 0;
    right: -100%;
    width: 100%;
    height: 100vh;
    z-index: 9997;
    background-color: rgba(0, 0, 0, 0.7);
    overflow-y: auto;
    transition: 0.3s;
  }

  body.mobile-nav-active #navmenu {
    right: 0;
  }

  #navmenu ul {
    position: absolute;
    top: 60px;
    right: 20px;
    left: 20px;
    bottom: 20px;
    max-height: calc(100vh - 80px);
    overflow-y: auto;
    background-color: var(--soft-white);
    padding: 20px !important;
    border-radius: 10px;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
  }

  #navmenu a {
    padding: 12px 15px !important;
    font-size: 16px !important;
    border-radius: 5px;
    color: var(--color-brown) !important;
  }

  #navmenu a:hover {
    background-color: rgba(var(--color-brown-rgb), 0.1);
  }

  .mobile-nav-toggle.bi-x {
    color: var(--soft-white);
  }
}

/* Orta boyutlu ekranlarda navbar butonları */
@media (min-width: 1200px) and (max-width: 1400px) {
  .nav-auth-link span {
    display: none;
  }

  .nav-auth-link {
    width: 40px;
    height: 40px;
    padding: 0 !important;
    display: flex !important;
    align-items: center;
    justify-content: center;
    border-radius: 50% !important;
  }

  .nav-auth-link i {
    margin-right: 0;
  }
}

/* Küçük ekranlarda logo boyutu ve ikon butonları */
@media (max-width: 576px) {
  .modern-logo {
    font-size: 1.5rem !important;
  }

  .auth-icon-btn {
    width: 32px;
    height: 32px;
  }

  .auth-icon-btn i {
    font-size: 1rem;
  }

  .modern-auth-icons {
    gap: 5px;
    margin-right: 10px;
  }
}

/* Mavi butonlar */
.modern-btn-navy {
  background: var(--soft-white);
  color: var(--color-navy);
  border: 2px solid var(--color-navy);
  box-shadow: 0 4px 12px rgba(var(--color-navy-rgb), 0.3),
              0 2px 6px rgba(0, 0, 0, 0.1);
}

.modern-btn-navy:hover {
  background: var(--color-navy);
  color: var(--soft-white);
  border-color: var(--color-navy);
  box-shadow: 0 8px 25px rgba(var(--color-navy-rgb), 0.4),
              0 4px 10px rgba(0, 0, 0, 0.15);
}

.modern-btn-sky {
  background: var(--soft-white);
  color: var(--color-sky);
  border: 2px solid var(--color-sky);
  box-shadow: 0 4px 12px rgba(var(--color-sky-rgb), 0.3),
              0 2px 6px rgba(0, 0, 0, 0.1);
}

.modern-btn-sky:hover {
  background: var(--color-sky);
  color: var(--soft-white);
  border-color: var(--color-sky);
  box-shadow: 0 8px 25px rgba(var(--color-sky-rgb), 0.4),
              0 4px 10px rgba(0, 0, 0, 0.15);
}

/* Orman Yeşili Butonlar - Minimalist */
.modern-btn-moss {
  background: transparent;
  color: var(--color-moss);
  border: 2px solid var(--color-moss);
  box-shadow: 0 4px 12px rgba(var(--color-moss-rgb), 0.1),
              0 2px 6px rgba(0, 0, 0, 0.03);
}

.modern-btn-moss:hover {
  background: rgba(var(--color-moss-rgb), 0.1);
  color: var(--color-moss);
  border-color: var(--color-moss);
  box-shadow: 0 8px 25px rgba(var(--color-moss-rgb), 0.2),
              0 4px 10px rgba(0, 0, 0, 0.1);
  transform: translateY(-3px);
}

/* Mobil görünümdeki kayıt ol butonu */
.mobile-auth-buttons .modern-btn-moss {
  background: transparent;
  color: var(--color-moss);
  border: 2px solid var(--color-moss);
  box-shadow: 0 4px 12px rgba(var(--color-moss-rgb), 0.1),
              0 2px 6px rgba(0, 0, 0, 0.03);
}

.mobile-auth-buttons .modern-btn-moss:hover {
  background: rgba(var(--color-moss-rgb), 0.1);
  color: var(--color-moss);
  border-color: var(--color-moss);
  box-shadow: 0 8px 25px rgba(var(--color-moss-rgb), 0.2),
              0 4px 10px rgba(0, 0, 0, 0.1);
}

/* FOOTER */
.modern-footer {
  background-color: var(--soft-light);
  padding: 60px 0 30px;
  position: relative;
  box-shadow: 0 -10px 30px rgba(var(--color-brown-rgb), 0.1);
  border-top: 1px solid rgba(var(--color-brown-rgb), 0.1);
}

.modern-footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("../img/pattern.svg");
  background-repeat: repeat;
  background-size: 300px;
  opacity: 0.05;
  z-index: 0;
  pointer-events: none;
}

.modern-footer-title {
  color: var(--color-brown);
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  position: relative;
  display: inline-block;
}

.modern-footer-title::after {
  content: '';
  position: absolute;
  bottom: -0.4rem;
  left: 0;
  width: 40px;
  height: 2px;
  background: linear-gradient(to right,
              rgba(var(--color-brown-rgb), 1),
              rgba(var(--color-brown-rgb), 0.3));
  border-radius: 2px;
}

.modern-footer-text {
  color: var(--color-navy);
  margin-bottom: 1.5rem;
  line-height: 1.7;
}

.modern-footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.modern-footer-links li {
  margin-bottom: 12px;
  position: relative;
}

.modern-footer-links li a {
  color: var(--color-navy);
  text-decoration: none;
  transition: all 0.3s ease;
  padding-left: 0;
  display: inline-block;
}

.modern-footer-links li a:hover {
  color: var(--color-brown);
  padding-left: 5px;
}

.modern-footer-links li a::before {
  content: '';
  position: absolute;
  left: -15px;
  top: 50%;
  transform: translateY(-50%) scale(0);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--color-brown);
  opacity: 0;
  transition: all 0.3s ease;
}

.modern-footer-links li a:hover::before {
  opacity: 1;
  transform: translateY(-50%) scale(1);
}

.modern-social-links {
  display: flex;
  gap: 10px;
}

.modern-footer-bottom {
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid rgba(var(--color-brown-rgb), 0.2);
  position: relative;
  z-index: 1;
}

.modern-link {
  color: var(--color-brown);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  border-bottom: 1px dashed var(--color-brown);
  padding-bottom: 2px;
}

.modern-link:hover {
  color: var(--color-moss);
  border-color: var(--color-moss);
  text-decoration: none;
}

/* SOSYAL MEDYA (FOOTER) */
.modern-footer .social-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: rgba(var(--color-navy-rgb), 0.2);
  color: var(--color-navy);
  border-radius: 50%;
  transition: all 0.3s ease;
  margin: 0 0.5rem;
  text-decoration: none;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.modern-footer .social-link:hover {
  background-color: var(--color-navy);
  transform: translateY(-3px);
  color: var(--soft-white);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

/* SCROLL TOP BUTON */
.scroll-top {
  position: fixed;
  visibility: hidden;
  opacity: 0;
  right: 15px;
  bottom: -15px;
  z-index: 99999;
  background-color: var(--color-navy);
  width: 44px;
  height: 44px;
  border-radius: 50px;
  transition: all 0.4s;
}

.scroll-top i {
  font-size: 24px;
  color: var(--soft-white);
  line-height: 0;
}

.scroll-top:hover {
  background-color: color-mix(in srgb, var(--color-navy), transparent 20%);
  color: var(--soft-white);
}

/* MODAL */
.modern-modal {
  border-radius: 15px;
  overflow: hidden;
  background-color: var(--soft-white);
  box-shadow: 0 10px 30px rgba(var(--color-moss-rgb), 0.2);
  border: none;
  opacity: 1 !important;
}

.modal-backdrop {
  background-color: rgba(var(--color-brown-rgb), 0.5);
  backdrop-filter: blur(5px);
}

.modal-content {
  background-color: var(--white) !important;
  opacity: 1 !important;
  border: none;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modern-modal-header {
  background-color: var(--color-moss);
  color: var(--soft-white);
  padding: 1.5rem;
  border-bottom: none;
}

.modern-modal-title {
  color: var(--soft-white);
  font-weight: 600;
  margin: 0;
}

.modern-modal-body {
  padding: 2rem;
  background-color: var(--white) !important;
}

.modal-body {
  background-color: var(--white) !important;
}

.modern-modal-footer {
  background-color: var(--soft-light) !important;
  padding: 1rem;
  border-top: none;
}

.modal-footer {
  background-color: var(--soft-light) !important;
  border-top: none;
}

.input-group-text {
  background-color: var(--color-moss);
  color: var(--soft-white);
  border: 1px solid var(--color-moss);
}

/* Modal içindeki form alanları */
.modal .form-control {
  background-color: var(--white) !important;
  border: 1px solid rgba(var(--color-moss-rgb), 0.3);
  color: var(--text-color);
  opacity: 1 !important;
}

.modal .form-control:focus {
  border-color: var(--color-moss);
  box-shadow: 0 0 0 0.25rem rgba(var(--color-moss-rgb), 0.25);
  background-color: var(--white) !important;
}

.modal .form-label {
  color: var(--text-color);
  font-weight: 500;
}

/* Modal genel stilleri */
.modal {
  background-color: transparent;
}

.modal-dialog {
  opacity: 1 !important;
}

/* ÇALIŞMALAR SAYFASI */
.transition-all {
  transition: all 0.5s ease;
}

.object-fit-cover {
  object-fit: cover;
}

/* Slider için özel stiller */
.main-slider-container {
    position: relative;
    overflow: hidden;
    border-radius: 16px;
    box-shadow: 0 5px 15px rgba(var(--primary-color-rgb), 0.1);
}

.main-slider-container .swiper-slide {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 500px;
}

.main-slider-container .swiper-slide img {
    width: 100%;
    height: auto;
    object-fit: cover;
    border-radius: 16px;
}

.main-slider-container .swiper-button-next,
.main-slider-container .swiper-button-prev {
    color: var(--color-brown);
}

.main-slider-container .swiper-pagination-bullet {
    background: var(--color-brown);
}

/* Öne Çıkan Çalışmalar Slider Stilleri */
.featured-works-slider {
    margin-bottom: 3rem;
    position: relative;
    overflow: hidden;
    padding: 2rem 0;
    background: linear-gradient(135deg,
                rgba(var(--color-sand-rgb), 0.1) 0%,
                rgba(var(--color-moss-rgb), 0.05) 100%);
    border-radius: 20px;
    box-shadow: inset 0 0 20px rgba(var(--color-brown-rgb), 0.05);
}

.slider-title {
    color: var(--color-moss);
    font-size: 2rem;
    margin-bottom: 2rem;
    position: relative;
    display: inline-block;
    transition: all 0.3s ease;
    padding-bottom: 0.75rem;
    font-weight: 700;
    letter-spacing: -0.5px;
}

.slider-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 80px;
    height: 3px;
    background: linear-gradient(to right, var(--color-brown), var(--color-moss));
    border-radius: 3px;
    transition: width 0.3s ease;
}

.slider-title i {
    color: var(--color-brown);
    margin-right: 0.5rem;
    transition: transform 0.3s ease;
}

.slider-title:hover {
    transform: translateY(-3px);
    text-shadow: 0 3px 6px rgba(var(--color-moss-rgb), 0.2);
}

.slider-title:hover::after {
    width: 100%;
}

.slider-title:hover i {
    transform: rotate(10deg) scale(1.2);
}

.featured-slider {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 15px 35px rgba(var(--color-brown-rgb), 0.15),
                0 8px 20px rgba(0, 0, 0, 0.08);
    padding-bottom: 50px;
}

/* Animasyonlu slide */
.featured-slide.animated {
    animation-duration: 0.5s;
    animation-fill-mode: both;
}

.swiper-slide-active .featured-slide.animated {
    animation-name: slideInUp;
}

.swiper-slide-next .featured-slide.animated {
    animation-name: slideInRight;
    animation-delay: 0.1s;
}

.swiper-slide-prev .featured-slide.animated {
    animation-name: slideInLeft;
    animation-delay: 0.1s;
}

@keyframes slideInUp {
    from {
        transform: translate3d(0, 30px, 0);
        opacity: 0;
    }
    to {
        transform: translate3d(0, 0, 0);
        opacity: 1;
    }
}

@keyframes slideInRight {
    from {
        transform: translate3d(30px, 0, 0);
        opacity: 0;
    }
    to {
        transform: translate3d(0, 0, 0);
        opacity: 1;
    }
}

@keyframes slideInLeft {
    from {
        transform: translate3d(-30px, 0, 0);
        opacity: 0;
    }
    to {
        transform: translate3d(0, 0, 0);
        opacity: 1;
    }
}

.featured-slide {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: var(--soft-light);
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(var(--color-brown-rgb), 0.15),
                0 5px 15px rgba(0, 0, 0, 0.08);
    transform: translateY(0);
}

.featured-slide:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(var(--color-brown-rgb), 0.2),
                0 10px 25px rgba(0, 0, 0, 0.1);
}

.featured-image {
    position: relative;
    height: 280px;
    overflow: hidden;
    border-bottom: 3px solid var(--color-moss);
}

.slide-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
    filter: brightness(0.95);
}

.featured-slide:hover .slide-image {
    transform: scale(1.05);
    filter: brightness(1.05);
}

.featured-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom,
                rgba(var(--color-brown-rgb), 0.1) 0%,
                rgba(var(--color-brown-rgb), 0.4) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.4s ease;
    backdrop-filter: blur(0px);
}

.featured-slide:hover .featured-overlay {
    opacity: 1;
    backdrop-filter: blur(2px);
}

.featured-btn {
    background-color: var(--color-brown);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 30px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(var(--color-brown-rgb), 0.4);
    letter-spacing: 0.5px;
    transform: translateY(20px);
    opacity: 0;
}

.featured-slide:hover .featured-btn {
    transform: translateY(0);
    opacity: 1;
}

.featured-btn:hover {
    background-color: var(--color-moss);
    color: white;
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 20px rgba(var(--color-moss-rgb), 0.5);
}

.featured-content {
    padding: 1.5rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    position: relative;
    background: linear-gradient(to bottom,
                var(--soft-light) 0%,
                rgba(var(--soft-white-rgb), 0.8) 100%);
}

.featured-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 0;
    background: var(--color-moss);
    transition: height 0.4s ease;
}

.featured-slide:hover .featured-content::before {
    height: 100%;
}

.featured-title {
    font-size: 1.25rem;
    margin-bottom: 0.75rem;
    color: var(--color-brown);
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    padding-bottom: 0.5rem;
}

.featured-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 2px;
    background: var(--color-brown);
    transition: width 0.3s ease, background 0.3s ease;
}

.featured-slide:hover .featured-title {
    color: var(--color-moss);
    transform: translateX(8px);
}

.featured-slide:hover .featured-title::after {
    width: 60px;
    background: var(--color-moss);
}

.featured-author {
    margin-bottom: 1rem;
    color: var(--text-soft);
    transition: transform 0.3s ease;
}

.featured-slide:hover .featured-author {
    transform: translateX(8px);
}

.featured-author a {
    color: var(--text-soft);
    transition: color 0.3s ease;
}

.featured-author a:hover {
    color: var(--color-brown);
}

.featured-meta {
    display: flex;
    justify-content: space-between;
    color: var(--text-muted);
    font-size: 0.9rem;
    margin-top: auto;
    padding-top: 1rem;
    border-top: 1px dashed rgba(var(--color-brown-rgb), 0.2);
    transition: transform 0.3s ease;
}

.featured-slide:hover .featured-meta {
    transform: translateX(8px);
}

.featured-meta span {
    display: flex;
    align-items: center;
}

.featured-meta i {
    margin-right: 5px;
    color: var(--color-moss);
    transition: transform 0.3s ease;
}

.featured-meta span:hover i {
    transform: scale(1.2);
}

/* Swiper Navigasyon Butonları - Öne Çıkan Çalışmalar */
.featured-slider .swiper-button-next,
.featured-slider .swiper-button-prev {
    color: var(--white);
    background-color: rgba(var(--color-brown-rgb), 0.7);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    opacity: 0;
    transform: translateY(20px);
}

.featured-slider:hover .swiper-button-next,
.featured-slider:hover .swiper-button-prev {
    opacity: 1;
    transform: translateY(0);
}

.featured-slider .swiper-button-next {
    right: 20px;
}

.featured-slider .swiper-button-prev {
    left: 20px;
}

.featured-slider .swiper-button-next:after,
.featured-slider .swiper-button-prev:after {
    font-size: 20px;
    font-weight: bold;
}

.featured-slider .swiper-button-next:hover,
.featured-slider .swiper-button-prev:hover {
    background-color: var(--color-moss);
    transform: scale(1.15);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
}

/* Swiper Pagination - Öne Çıkan Çalışmalar */
.featured-slider .swiper-pagination {
    bottom: 15px;
}

.featured-slider .swiper-pagination-bullet {
    width: 12px;
    height: 12px;
    background-color: var(--color-brown);
    opacity: 0.7;
    transition: all 0.3s ease;
    margin: 0 5px;
}

.featured-slider .swiper-pagination-bullet-active {
    background-color: var(--color-moss);
    opacity: 1;
    width: 30px;
    height: 12px;
    border-radius: 6px;
}

.thumbnails-container {
    display: flex;
    justify-content: center;
    align-items: center;
    overflow-x: auto;
    padding: 10px 0;
}

.thumbnails-container .swiper-slide {
    width: 100px;
    height: 100px;
    margin: 0 5px;
    cursor: pointer;
}

.thumbnails-container .swiper-slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
    transition: transform 0.3s ease;
}

.thumbnails-container .swiper-slide:hover img {
    transform: scale(1.1);
}

.swiper-button-next,
.swiper-button-prev {
  color: var(--color-brown) !important;
  background: rgba(var(--soft-white-rgb), 0.7);
  width: 40px !important;
  height: 40px !important;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.swiper-button-next:after,
.swiper-button-prev:after {
  font-size: 1.2rem !important;
  font-weight: bold;
}

.swiper-pagination-bullet-active {
  background: var(--color-brown) !important;
}

/* Sayfalama Stilleri - Bootstrap Pagination */
.page-item.active .page-link {
  background-color: var(--color-brown);
  border-color: var(--color-brown);
}

.page-link {
  color: var(--color-brown);
}

.page-link:hover {
  color: var(--color-navy);
}

/* PROFİL SAYFASI STİLLERİ */
.modern-profile-header {
  display: flex;
  align-items: center;
  gap: 2rem;
  padding: 1.5rem;
  background-color: rgba(var(--color-moss-rgb), 0.1);
  border-radius: 16px;
  margin-bottom: 1.5rem;
  border: 1px solid rgba(var(--color-moss-rgb), 0.2);
}

.modern-profile-pic {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  border: 4px solid var(--soft-white);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.modern-profile-pic img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.modern-profile-pic .modern-btn-circle {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--color-moss);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.modern-profile-pic .modern-btn-circle:hover {
  background-color: var(--color-navy);
}

.modern-profile-info {
  flex-grow: 1;
}

.modern-profile-info h2 {
  margin-bottom: 0.5rem;
  color: var(--color-moss);
}

.modern-profile-info p {
  margin-bottom: 0.5rem;
  color: var(--text-color);
}

/* Profil Sekmeler */
.modern-tabs {
  display: flex;
  gap: 0.5rem;
  border-bottom: 1px solid rgba(var(--color-moss-rgb), 0.5);
  margin-bottom: 1.5rem;
  overflow-x: auto;
  padding-bottom: 0.5rem;
}

.modern-tab {
  padding: 0.75rem 1.25rem;
  border-radius: 8px 8px 0 0;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  color: var(--text-color);
  white-space: nowrap;
}

.modern-tab:hover {
  background-color: rgba(var(--color-moss-rgb), 0.2);
}

.modern-tab.active {
  background-color: var(--color-moss);
  color: white;
}

.modern-tab-content {
  margin-bottom: 2rem;
}

.modern-tab-pane {
  display: none;
}

.modern-tab-pane.active {
  display: block;
}

/* Profil Alanları */
.modern-profile-field {
  margin-bottom: 1.5rem;
}

.modern-profile-field label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--color-moss);
}

/* ZAMANCİZGİSİ */
.timeline {
  position: relative;
  padding-left: 2rem;
}

.timeline::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 2px;
  height: 100%;
  background-color: var(--accent-color);
}

.timeline-item {
  position: relative;
  padding-bottom: 1.5rem;
}

.timeline-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -2rem;
  width: 12px;
  height: 12px;
  background-color: var(--primary-color);
  border: 3px solid var(--accent-color);
  border-radius: 50%;
}

/* Kişisel Bilgiler Kartı - Profil Resmi */
.personal-info-card .profile-image {
  border: 4px solid var(--soft-white);
  box-shadow: 0 5px 15px rgba(var(--color-brown-rgb), 0.2);
  transition: all 0.4s ease;
  position: relative;
}

.personal-info-card .profile-image::after {
  content: '';
  position: absolute;
  top: -8px;
  left: -8px;
  right: -8px;
  bottom: -8px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--color-brown), var(--color-sand));
  z-index: -1;
  opacity: 0.3;
}

.personal-info-card:hover .profile-image {
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(var(--color-brown-rgb), 0.3);
}

.personal-info-card .profile-placeholder {
  background: linear-gradient(135deg, rgba(var(--color-sand-rgb), 0.5), rgba(var(--color-brown-rgb), 0.3)) !important;
  border: 4px solid var(--soft-white);
  box-shadow: 0 5px 15px rgba(var(--color-brown-rgb), 0.1);
  transition: all 0.4s ease;
}

.personal-info-card .profile-placeholder i {
  color: var(--color-brown) !important;
  opacity: 0.7;
  text-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.personal-info-card:hover .profile-placeholder {
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(var(--color-brown-rgb), 0.2);
}

/* Kişisel Bilgiler Kartı - İçerik */
.personal-info-card #hakkimda-baslik {
  color: var(--color-brown);
  font-size: 1.5rem;
  margin-bottom: 0.75rem;
  position: relative;
  display: inline-block;
  text-shadow: 0 1px 2px rgba(var(--color-brown-rgb), 0.1);
}

.personal-info-card #hakkimda-baslik::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 50%;
  height: 2px;
  background: linear-gradient(to right, var(--color-brown), transparent);
}

.personal-info-card #hakkimda-aciklama {
  color: var(--text-soft);
  line-height: 1.6;
}

/* Kişisel Bilgiler Kartı - Eğitim Bilgileri */
.personal-info-card .timeline-container {
  margin-top: 2rem;
  padding: 1.25rem;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(var(--color-brown-rgb), 0.05);
}

/* Sanat Yolculuğum Kartı - Özel Stiller */
.sanat-yolculugu-content .border-primary {
  border-color: var(--color-navy) !important;
  background-color: rgba(var(--color-sky-rgb), 0.1);
  border-radius: 0 8px 8px 0;
  transition: all 0.3s ease;
  box-shadow: 2px 2px 10px rgba(var(--color-navy-rgb), 0.05);
}

.sanat-yolculugu-content .border-primary:hover {
  background-color: rgba(var(--color-sky-rgb), 0.2);
  box-shadow: 3px 3px 15px rgba(var(--color-navy-rgb), 0.1);
  transform: translateX(5px);
}

.sanat-yolculugu-content .border-primary h5 {
  color: var(--color-navy);
  font-weight: 600;
  margin-bottom: 0.75rem;
}

.sanat-yolculugu-content .border-primary p {
  color: var(--text-soft);
  line-height: 1.6;
}

/* Sanat Yolculuğum - Bölüm Başlıkları */
.sanat-yolculugu-content .skills-section h5 {
  color: var(--color-moss);
  font-weight: 600;
  display: inline-block;
  position: relative;
  padding-bottom: 8px;
}

.sanat-yolculugu-content .workshop-section h5 {
  color: var(--color-sky);
  font-weight: 600;
  display: inline-block;
  position: relative;
  padding-bottom: 8px;
}

.sanat-yolculugu-content .skills-section h5::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 2px;
  background: linear-gradient(to right, var(--color-moss), transparent);
  border-radius: 2px;
}

.sanat-yolculugu-content .workshop-section h5::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 2px;
  background: linear-gradient(to right, var(--color-sky), transparent);
  border-radius: 2px;
}

.sanat-yolculugu-content .skills-section h5 i {
  color: var(--color-moss);
}

.sanat-yolculugu-content .workshop-section h5 i {
  color: var(--color-sky);
}

/* Sanat Yolculuğum - Teknikler Listesi */
.sanat-yolculugu-content .skill-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  transition: all 0.3s ease;
  padding: 0.5rem;
  border-radius: 6px;
}

.sanat-yolculugu-content .skill-item:hover {
  background-color: rgba(var(--color-moss-rgb), 0.1);
  transform: translateX(5px);
}

.sanat-yolculugu-content .skill-item i {
  color: var(--color-moss) !important;
  margin-right: 0.5rem;
}

/* Sanat Yolculuğum - Atölye Kartları */
.sanat-yolculugu-content .card.bg-light {
  background-color: rgba(var(--color-sky-rgb), 0.1) !important;
  border: none;
  box-shadow: 0 5px 15px rgba(var(--color-sky-rgb), 0.05);
  transition: all 0.3s ease;
}

.sanat-yolculugu-content .card.bg-light:hover {
  background-color: rgba(var(--color-sky-rgb), 0.2) !important;
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(var(--color-sky-rgb), 0.1);
}

.sanat-yolculugu-content .card.bg-light i {
  color: var(--color-sky);
  transition: all 0.3s ease;
}

.sanat-yolculugu-content .card.bg-light:hover i {
  transform: scale(1.1);
}

.sanat-yolculugu-content .card.bg-light h6 {
  color: var(--color-navy);
  font-weight: 600;
  margin: 0.5rem 0;
}

/* Kişisel Bilgiler Kartı - Düzenleme Butonu */
.personal-info-card .content-edit-btn-bottom {
  background: linear-gradient(135deg, var(--color-brown), rgba(var(--color-brown-rgb), 0.8));
  color: var(--soft-white);
  box-shadow: 0 5px 15px rgba(var(--color-brown-rgb), 0.2);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.personal-info-card .content-edit-btn-bottom:hover {
  transform: translateY(-3px) scale(1.1);
  box-shadow: 0 8px 20px rgba(var(--color-brown-rgb), 0.3);
  background: linear-gradient(135deg, var(--color-brown), var(--color-sand));
}

.personal-info-card .timeline-container h5 {
  color: var(--color-brown);
  margin-bottom: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.personal-info-card .timeline-container h5 i {
  margin-right: 0.5rem;
  color: var(--color-brown);
}

.personal-info-card .timeline {
  padding-left: 1.5rem;
}

.personal-info-card .timeline::before {
  background: linear-gradient(to bottom, var(--color-brown), rgba(var(--color-brown-rgb), 0.3));
}

.personal-info-card .timeline-item::before {
  background-color: var(--soft-white);
  border: 3px solid var(--color-brown);
  box-shadow: 0 0 0 3px rgba(var(--color-brown-rgb), 0.1);
  left: -1.5rem;
}

.personal-info-card .timeline-date {
  font-weight: 600;
  color: var(--color-brown);
  margin-bottom: 0.25rem;
}

.personal-info-card .timeline-content h6 {
  color: var(--text-color);
  margin-bottom: 0.25rem;
}

.personal-info-card .timeline-content p {
  color: var(--text-soft);
  margin-bottom: 0;
}

/* ALINTI KUTUSU */
.quote-box {
  font-style: italic;
  padding: 2rem;
  background-color: rgba(var(--accent-color-rgb), 0.05);
  border-radius: 12px;
  position: relative;
}

/* MODERN LİSTE VE KATEGORİLER */
.modern-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.modern-list li {
  margin-bottom: 0;
  border-bottom: 1px solid rgba(var(--color-moss-rgb), 0.2);
}

.modern-list li:last-child {
  border-bottom: none;
}

.modern-list-item {
  display: flex;
  padding: 12px 20px;
  text-decoration: none;
  color: var(--text-color);
  transition: all 0.3s ease;
}

.modern-list-item:hover {
  background-color: rgba(var(--color-moss-rgb), 0.1);
  color: var(--color-moss);
}

.modern-list-item.active {
  background-color: var(--color-moss);
  color: white;
}

.modern-badge {
  display: inline-block;
  padding: 3px 10px;
  background-color: rgba(var(--color-moss-rgb), 0.2);
  color: var(--color-moss);
  border-radius: 20px;
  font-size: 0.8rem;
}

.modern-list-item.active .modern-badge {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
}

.modern-badge-corner {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: var(--color-moss);
  color: white;
.modern-video-player::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px; /* Kalınlık */
  background: linear-gradient(to right, var(--color-brown), var(--color-navy)); /* Gradyan renkler */
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.modern-card:hover .modern-video-player::after {
  transform: scaleX(1);
}
  padding: 4px 10px;
  border-radius: 20px;
  font-size: 0.75rem;
  z-index: 1;
}

.modern-tag-cloud {
  display: flex;
  flex-wrap: wrap;
.video-thumbnail img:hover {
  transform: scale(1.05);
}
  gap: 8px;
}

.modern-tag {
  display: inline-block;
  padding: 5px 12px;
  background-color: rgba(var(--color-moss-rgb), 0.15);
  color: var(--color-moss);
  border-radius: 20px;
  font-size: 0.85rem;
  text-decoration: none;
  transition: all 0.3s ease;
}

.modern-tag:hover {
  background-color: rgba(var(--color-moss-rgb), 0.3);
  color: var(--color-moss);
}

.modern-tag.active {
  background-color: var(--color-moss);
  color: white;
}

/* VİDEO OYNATICI */
.modern-video-player {
  position: relative;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  height: 0;
  overflow: hidden;
  border-radius: 12px 12px 0 0;
}

/* VİDEO KARTLARI */
.video-card {
  display: flex;
  flex-direction: column;
  height: 100%;
  border: 1px solid rgba(var(--color-moss-rgb), 0.2);
  border-radius: 10px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.video-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(var(--color-moss-rgb), 0.1);
  border-color: var(--color-moss);
}

.video-thumbnail {
  position: relative;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  overflow: hidden;
}

.video-thumbnail img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.video-card:hover .video-thumbnail img {
  transform: scale(1.05);
}

.video-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.8rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 2.5rem; /* Yaklaşık 2 satır */
  color: var(--color-moss);
}

/* BEĞENİ BUTONU */
.modern-like-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: var(--soft-light);
  border: 1px solid rgba(var(--color-moss-rgb), 0.3);
  border-radius: 50px;
  color: var(--text-color);
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
}

.modern-like-btn:hover {
  background-color: rgba(var(--color-moss-rgb), 0.1);
  border-color: rgba(var(--color-moss-rgb), 0.5);
}

.modern-like-btn.active {
  background-color: var(--color-moss);
  border-color: var(--color-moss);
  color: var(--color-brown);
}

.modern-like-btn i {
  font-size: 1.1rem;
}

/* VİDEO OYNATICI */
.modern-video-player iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
}

.modern-play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 3rem;
  opacity: 0.8;
  transition: opacity 0.3s ease, transform 0.3s ease;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  z-index: 2;
}

.modern-card:hover .modern-play-button {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1.1);
}

/* SAYFALAMA - Modern Özel Stil */
.modern-pagination {
  display: flex;
  justify-content: center;
  gap: 5px;
  padding: 0;
  list-style: none;
  margin-top: 2rem;
}

.modern-page-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--soft-light);
  color: var(--text-color);
  text-decoration: none;
  transition: all 0.3s ease;
  border: 1px solid rgba(var(--color-moss-rgb), 0.3);
}

.modern-page-link:hover {
  background-color: rgba(var(--color-moss-rgb), 0.1);
  color: var(--color-moss);
}

.modern-page-link.active {
  background-color: var(--color-moss);
  color: white;
  border-color: var(--color-moss);
}

/* Responsive düzenlemeler */
@media (max-width: 576px) {
  .modern-pagination .modern-page-link {
    width: 35px;
    height: 35px;
  }
}

/* VİDEO DETAY SAYFASI */
.modern-video-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1rem;
}

.modern-video-content {
  padding: 0;
  overflow: hidden;
}

.modern-video-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.modern-info-list {
  display: flex;
  flex-direction: column;
  padding: 0.5rem;
  background-color: var(--soft-white);
  border-radius: 8px;
  box-shadow: inset 0 0 10px rgba(var(--color-brown-rgb), 0.05);
}

.modern-info-item {
  display: flex;
  justify-content: space-between;
  padding: 0.75rem 1.25rem;
  border-bottom: 1px solid rgba(var(--light-accent-rgb), 0.2);
  align-items: center;
}

.modern-info-item strong {
  color: var(--color-brown);
  font-weight: 600;
  flex: 0 0 40%;
}

.modern-info-item span {
  text-align: right;
  flex: 0 0 55%;
}

.modern-info-item:last-child {
  border-bottom: none;
}

.modern-sidebar-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 0;
  list-style: none;
}

.modern-sidebar-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  text-decoration: none;
  color: var(--text-color);
}

.modern-sidebar-item:hover {
  background-color: rgba(var(--accent-color-rgb), 0.1);
}

.modern-sidebar-thumbnail {
  width: 80px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  flex-shrink: 0;
}

.modern-sidebar-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.modern-sidebar-info {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.quote-box::before {
  content: '\201C';
  position: absolute;
  top: 0;
  left: 10px;
  font-size: 4rem;
  color: rgba(var(--accent-color-rgb), 0.2);
  line-height: 1;
}

/* YETENEK BARI */
.skill-item {
  margin-bottom: 1.5rem;
}

.skill-name {
  font-weight: 600;
  color: var(--text-color);
}

.skill-percentage {
  font-weight: 600;
  color: var(--color-brown);
}

.skill-bar {
  height: 8px;
  background-color: rgba(var(--color-brown-rgb), 0.1);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.skill-progress {
  height: 100%;
  background: linear-gradient(to right, var(--color-brown), var(--color-sand));
  border-radius: 4px;
  width: 0;
  transition: width 1.5s cubic-bezier(0.1, 0.5, 0.2, 1);
  position: relative;
}

/* Sayaç */
.counter-box {
  font-size: 3rem;
  font-weight: 700;
  color: var(--color-brown);
  line-height: 1;
  display: inline-block;
  position: relative;
}

.counter-box::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 3px;
  background: linear-gradient(to right,
              rgba(var(--color-brown-rgb), 1),
              rgba(var(--color-brown-rgb), 0.3));
  border-radius: 3px;
}

.counter-number {
  display: inline-block;
}

.counter-suffix {
  font-size: 2.5rem;
  color: var(--color-brown);
  opacity: 0.8;
}

/* Zaman Çizelgesi */
.timeline {
  position: relative;
  padding-left: 30px;
}

.timeline::before {
  content: '';
  position: absolute;
  top: 0;
  left: 7px;
  height: 100%;
  width: 2px;
  background: linear-gradient(to bottom,
              var(--color-brown),
              rgba(var(--color-brown-rgb), 0.3));
}

.timeline-item {
  position: relative;
  padding-bottom: 25px;
}

.timeline-item:last-child {
  padding-bottom: 0;
}

.timeline-dot {
  position: absolute;
  left: -30px;
  top: 0;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: var(--color-brown);
  border: 3px solid var(--soft-white);
  box-shadow: 0 0 0 3px rgba(var(--color-brown-rgb), 0.2);
  z-index: 1;
}

.timeline-content {
  padding-left: 10px;
  position: relative;
}

.timeline-content h4 {
  color: var(--color-brown);
  margin-bottom: 5px;
  font-weight: 600;
}

/* BÖLÜM AYIRICI */
.section-divider {
  width: 100%;
  height: 60px;
  position: relative;
  margin: 3rem 0;
}

.section-divider::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(to right, transparent, var(--color-moss), transparent);
}

.section-divider-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 50px;
  height: 50px;
  background-color: var(--soft-white);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  color: var(--color-moss);
  font-size: 1.5rem;
}

/* HARİTA KONTEYNER */
.map-container {
  border-radius: 12px;
  overflow: hidden;
}

/* BAŞLIK STİLLERİ */
.gradient-heading {
  background: linear-gradient(to right, var(--color-navy), var(--color-moss));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent !important; /* Eski tarayıcılar için destek */
  display: inline-block;
  position: relative;
  /* Firefox için ek destek */
  text-shadow: none;
}

/* YARDIMCI SINIFLAR - Görsel Hiyerarşi ve Vurgulama */

/* Metin Renkleri */
.text-inherit { color: inherit; }
.text-inherit:hover { color: var(--color-brown); }
.text-brown { color: var(--color-brown) !important; }
.text-navy { color: var(--color-navy) !important; }
.text-moss { color: var(--color-moss) !important; }
.text-sky { color: var(--color-sky) !important; }
.text-sand { color: var(--color-sand) !important; }

/* Metin Vurgulamaları */
.text-bold { font-weight: var(--font-weight-bold); }
.text-semibold { font-weight: var(--font-weight-semibold); }
.text-medium { font-weight: var(--font-weight-medium); }
.text-regular { font-weight: var(--font-weight-regular); }
.text-light { font-weight: var(--font-weight-light); }

/* Metin Boyutları */
.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-md { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.75rem; }
.text-4xl { font-size: 2rem; }

/* Arka Plan Renkleri */
.bg-brown-light { background-color: rgba(var(--color-brown-rgb), 0.1); }
.bg-navy-light { background-color: rgba(var(--color-navy-rgb), 0.1); }
.bg-moss-light { background-color: rgba(var(--color-moss-rgb), 0.1); }
.bg-sky-light { background-color: rgba(var(--color-sky-rgb), 0.1); }
.bg-sand-light { background-color: rgba(var(--color-sand-rgb), 0.1); }

/* Kenarlık Stilleri */
.border-left-brown { border-left: 3px solid var(--color-brown); }
.border-left-navy { border-left: 3px solid var(--color-navy); }
.border-left-moss { border-left: 3px solid var(--color-moss); }
.border-left-sky { border-left: 3px solid var(--color-sky); }
.border-left-sand { border-left: 3px solid var(--color-sand); }

/* Vurgulama Kutuları */
.highlight-box {
  padding: var(--space-md);
  border-radius: var(--border-radius-md);
  margin: var(--space-md) 0;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.highlight-box-brown {
  background-color: rgba(var(--color-brown-rgb), 0.05);
  border: 1px solid rgba(var(--color-brown-rgb), 0.1);
}

.highlight-box-brown::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background-color: var(--color-brown);
}

.highlight-box-navy {
  background-color: rgba(var(--color-navy-rgb), 0.05);
  border: 1px solid rgba(var(--color-navy-rgb), 0.1);
}

.highlight-box-navy::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background-color: var(--color-navy);
}

.highlight-box-moss {
  background-color: rgba(var(--color-moss-rgb), 0.05);
  border: 1px solid rgba(var(--color-moss-rgb), 0.1);
}

.highlight-box-moss::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background-color: var(--color-moss);
}

/* İkon Vurgulamaları */
.icon-circle {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(var(--color-brown-rgb), 0.1);
  color: var(--color-brown);
  margin-right: var(--space-sm);
}

.icon-circle-sm {
  width: 30px;
  height: 30px;
  font-size: 0.9rem;
}

.icon-circle-lg {
  width: 50px;
  height: 50px;
  font-size: 1.25rem;
}

/* Animasyonlu Vurgular */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(var(--color-brown-rgb), 0.15);
}

.hover-scale {
  transition: transform 0.3s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

/* Çağrı-Aksiyon (Call-to-Action) Vurgulamaları */
.cta-box {
  padding: var(--space-lg);
  background: linear-gradient(135deg, rgba(var(--color-brown-rgb), 0.1) 0%, rgba(var(--color-sand-rgb), 0.2) 100%);
  border-radius: var(--border-radius-lg);
  box-shadow: 0 8px 25px rgba(var(--color-brown-rgb), 0.1);
  text-align: center;
  margin: var(--space-xl) 0;
}

.cta-title {
  font-size: 1.5rem;
  font-weight: var(--font-weight-semibold);
  color: var(--color-brown);
  margin-bottom: var(--space-md);
}

.cta-text {
  font-size: 1.1rem;
  color: var(--text-soft);
  margin-bottom: var(--space-lg);
}

/* RESPONSİVE DÜZENLEMELER */
/* Büyük Tabletler (992px'e kadar) */
@media (max-width: 992px) {
  :root {
    --container-padding-x: var(--space-md);
    --section-spacing: var(--space-xl);
  }

  .row {
    --bs-gutter-x: var(--space-md);
  }

  .modern-card {
    margin-bottom: var(--space-lg);
  }

  h1 {
    font-size: 2.25rem;
  }

  h2 {
    font-size: 1.75rem;
  }

  .hero-title {
    font-size: 3rem;
  }

  .hero-subtitle {
    font-size: 1.5rem;
  }
}

/* Tabletler (768px'e kadar) */
@media (max-width: 768px) {
  :root {
    --container-padding-x: var(--space-sm);
    --section-spacing: var(--space-lg);
    --card-spacing: var(--space-sm);
  }

  .row {
    --bs-gutter-x: var(--space-sm);
  }

  .modern-card {
    margin-bottom: var(--space-md);
  }

  .timeline {
    padding-left: var(--space-lg);
  }

  .timeline-item::before {
    left: calc(-1 * var(--space-lg));
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
  }

  .modern-profile-header {
    flex-direction: column;
    text-align: center;
  }

  .modern-profile-pic {
    margin: 0 auto var(--space-md);
  }

  .modern-tabs {
    justify-content: center;
  }

  h1 {
    font-size: 2rem;
  }

  h2 {
    font-size: 1.5rem;
  }

  h3 {
    font-size: 1.25rem;
  }

  .modern-header {
    margin-bottom: var(--space-lg);
    padding-bottom: var(--space-lg);
  }

  /* Ürün ve çalışma detay sayfaları için düzenlemeler */
  .modern-card-header,
  .modern-card-body,
  .autumn-card-header,
  .autumn-card-header-alt,
  .autumn-card-header-gradient {
    padding: var(--space-md);
  }

  .modern-info-item {
    padding: var(--space-xs) var(--space-md);
  }
}

/* Mobil Cihazlar (576px'e kadar) */
@media (max-width: 576px) {
  :root {
    --container-padding-x: var(--space-xs);
    --section-spacing: var(--space-md);
    --card-spacing: var(--space-xs);
    --element-spacing: var(--space-xs);
  }

  .row {
    --bs-gutter-x: var(--space-xs);
  }

  .modern-section {
    padding: var(--section-spacing) 0;
  }

  .modern-header {
    padding-bottom: var(--space-md);
    margin-bottom: var(--space-md);
  }

  .modern-card-header,
  .modern-card-body,
  .autumn-card-header,
  .autumn-card-header-alt,
  .autumn-card-header-gradient {
    padding: var(--space-sm);
  }

  .modern-info-item {
    padding: var(--space-xxs) var(--space-sm);
  }

  .hero-section {
    min-height: 450px;
  }

  .hero-title {
    font-size: 1.75rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .hero-btn {
    padding: var(--space-xs) var(--space-lg);
    font-size: 1rem;
  }

  .info-item {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .info-icon {
    margin-right: 0;
    margin-bottom: var(--space-xs);
  }

  h1 {
    font-size: 1.75rem;
  }

  h2 {
    font-size: 1.5rem;
  }

  h3 {
    font-size: 1.25rem;
  }

  h4 {
    font-size: 1.1rem;
  }

  p {
    font-size: 0.95rem;
  }

  /* Ürün ve çalışma detay sayfaları için düzenlemeler */
  .modern-card {
    margin-bottom: var(--space-sm);
    border-radius: var(--border-radius-md);
  }

  .modern-card-header h3,
  .autumn-card-header h3,
  .autumn-card-header-alt h3,
  .autumn-card-header-gradient h3 {
    font-size: 0.95rem;
  }

  .modern-info-list {
    font-size: 0.9rem;
  }
}

/*
 * Anasayfa Stilleri - anasayfa-styles.css'den entegre edildi
 * Son güncelleme: 07.05.2025
 */

/* Inline Editing Stilleri */
.editable-content {
    position: relative;
    transition: all 0.3s ease;
}

.editable-content:hover {
    background-color: rgba(var(--accent-color-rgb), 0.05);
}

.editable-content .edit-icon {
    position: absolute;
    top: 5px;
    right: 5px;
    opacity: 0;
    transition: all 0.3s ease;
    cursor: pointer;
    color: var(--accent-color);
    background-color: rgba(var(--white-rgb), 0.8);
    border-radius: 50%;
    padding: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.editable-content:hover .edit-icon {
    opacity: 1;
}

/* Düzenleme Modu */
.edit-mode {
    border: 2px dashed var(--accent-color);
    padding: 10px;
    border-radius: 5px;
    background-color: rgba(var(--accent-color-rgb), 0.05);
}

/* Düzenleme Kontrolleri */
.edit-controls {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.edit-save-btn, .edit-cancel-btn {
    padding: 5px 15px;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.edit-save-btn {
    background-color: var(--accent-color);
    color: var(--white);
    border: none;
}

.edit-save-btn:hover {
    background-color: var(--heading-color);
}

.edit-cancel-btn {
    background-color: var(--off-white);
    color: var(--text-color);
    border: 1px solid var(--light-gray);
}

.edit-cancel-btn:hover {
    background-color: var(--light-gray);
}

/* ÇALIŞMA DETAY SAYFASI STİLLERİ - SWIPER ENTEGRASYONU */

/* Ana slider konteyner */
.main-slider-container {
    position: relative;
    width: 100%;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(var(--color-brown-rgb), 0.08);
    transition: all 0.3s ease;
    background-color: var(--soft-white);
    margin-bottom: var(--space-lg);
}

.main-slider-container:hover {
    box-shadow: 0 8px 25px rgba(var(--color-brown-rgb), 0.12);
}

/* Ana görsel için stil */
.main-image-container {
    position: relative;
    width: 100%;
    height: 450px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: var(--soft-white);
    overflow: hidden;
    transition: all 0.3s ease;
    padding: var(--space-md);
}

.main-image-container img {
    max-height: 420px;
    max-width: 100%;
    object-fit: contain;
    transition: transform 0.5s ease;
    box-shadow: 0 8px 25px rgba(var(--color-brown-rgb), 0.1);
}

.main-image-container a:hover img {
    transform: scale(1.03);
}

/* Swiper özelleştirmeleri */
.swiper {
    width: 100%;
    height: 100%;
}

.mainSwiper {
    margin-bottom: var(--space-xs);
}

.thumbsSwiper {
    padding: var(--space-xs) 0;
}

.thumbsSwiper .swiper-slide {
    width: 70px;
    opacity: 0.6;
    transition: all 0.3s ease;
}

.thumbsSwiper .swiper-slide-thumb-active {
    opacity: 1;
}

/* Swiper navigasyon butonları */
.swiper-button-next,
.swiper-button-prev {
    color: var(--white);
    background-color: rgba(var(--color-brown-rgb), 0.7);
    width: 44px;
    height: 44px;
    border-radius: var(--border-radius-circle);
    transition: all 0.3s ease;
    opacity: 0;
}

.swiper-button-next:after,
.swiper-button-prev:after {
    font-size: 18px;
    font-weight: bold;
}

.main-slider-container:hover .swiper-button-next,
.main-slider-container:hover .swiper-button-prev {
    opacity: 0.8;
}

.swiper-button-next:hover,
.swiper-button-prev:hover {
    background-color: rgba(var(--color-brown-rgb), 0.9);
    transform: scale(1.1);
    opacity: 1 !important;
}

/* Swiper pagination */
.swiper-pagination-bullet {
    width: 10px;
    height: 10px;
    background-color: var(--white);
    opacity: 0.7;
    transition: all 0.3s ease;
}

.swiper-pagination-bullet-active {
    background-color: var(--color-brown);
    opacity: 1;
    width: 12px;
    height: 12px;
}

/* Tam ekran butonu */
.fullscreen-button {
    position: absolute;
    top: var(--space-md);
    right: var(--space-md);
    width: 40px;
    height: 40px;
    background-color: rgba(var(--color-brown-rgb), 0.7);
    color: var(--white);
    border-radius: var(--border-radius-circle);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    cursor: pointer;
    z-index: 10;
    transition: all 0.3s ease;
    opacity: 0;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
}

.main-slider-container:hover .fullscreen-button {
    opacity: 0.8;
}

.fullscreen-button:hover {
    background-color: rgba(var(--color-brown-rgb), 0.9);
    transform: scale(1.1);
    opacity: 1 !important;
}

/* Küçük resimler bölümü */
.thumbnails-container {
    background-color: var(--soft-white);
    border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
    border-top: 1px solid rgba(var(--color-brown-rgb), 0.1);
    padding: var(--space-sm) var(--space-md);
}

.thumbnails-counter {
    font-size: 0.9rem;
    color: var(--text-soft);
    font-weight: var(--font-weight-medium);
}

.thumbnails-counter .current-index {
    color: var(--color-brown);
    font-weight: var(--font-weight-bold);
}

/* Küçük resimler için stil */
.thumbnail-item {
    cursor: pointer;
    border: 2px solid transparent;
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    transition: all 0.3s ease;
    height: 60px;
    width: 60px;
    position: relative;
    box-shadow: 0 2px 8px rgba(var(--color-brown-rgb), 0.1);
    margin: 0 auto;
}

.swiper-slide-thumb-active .thumbnail-item {
    border-color: var(--color-brown);
}

.thumbnail-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.thumbnail-item:hover img {
    transform: scale(1.1);
}

/* Animasyonlar */
@keyframes fadeIn {
    from { opacity: 0; transform: scale(0.95); }
    to { opacity: 1; transform: scale(1); }
}

@keyframes fadeOut {
    from { opacity: 1; transform: scale(1); }
    to { opacity: 0; transform: scale(0.95); }
}

/* Responsive düzenlemeler - Slider için özel */
@media (max-width: 992px) {
    .main-image-container {
        height: 400px;
    }

    .main-image-container img {
        max-height: 370px;
    }
}

@media (max-width: 768px) {
    .main-image-container {
        height: 350px;
        padding: var(--space-sm);
    }

    .main-image-container img {
        max-height: 320px;
    }

    .thumbnail-item {
        height: 55px;
        width: 55px;
    }

    .swiper-button-next,
    .swiper-button-prev {
        width: 38px;
        height: 38px;
    }

    .swiper-button-next:after,
    .swiper-button-prev:after {
        font-size: 16px;
    }

    .fullscreen-button {
        width: 36px;
        height: 36px;
        font-size: 18px;
        top: var(--space-sm);
        right: var(--space-sm);
    }

    .thumbnails-container {
        padding: var(--space-xs) var(--space-sm);
    }
}

@media (max-width: 576px) {
    .main-image-container {
        height: 280px;
        padding: var(--space-xs);
    }

    .main-image-container img {
        max-height: 260px;
    }

    .thumbnail-item {
        height: 45px;
        width: 45px;
    }

    .swiper-button-next,
    .swiper-button-prev {
        width: 34px;
        height: 34px;
    }

    .swiper-button-next:after,
    .swiper-button-prev:after {
        font-size: 14px;
    }

    .fullscreen-button {
        width: 32px;
        height: 32px;
        font-size: 16px;
        top: var(--space-xs);
        right: var(--space-xs);
    }

    .thumbnails-container {
        padding: var(--space-xxs) var(--space-xs);
    }

    .thumbnails-counter {
        font-size: 0.8rem;
    }
}

/* Mavi Tema Bölümü */
.blue-section {
  background: linear-gradient(135deg, var(--color-navy) 0%, var(--color-sky) 100%);
  color: white;
  padding: 5rem 0;
  position: relative;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(var(--color-navy-rgb), 0.2),
              0 5px 15px rgba(0, 0, 0, 0.1);
}

.blue-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("../img/pattern.svg");
  background-repeat: repeat;
  background-size: 200px;
  opacity: 0.05;
  z-index: 0;
}

.blue-section .container {
  position: relative;
  z-index: 1;
}

.feature-box {
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  height: 100%;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.feature-box:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  background-color: rgba(255, 255, 255, 0.15);
}

.feature-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: white;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.feature-box h4 {
  color: white;
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
}

.feature-box p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  margin-bottom: 0;
}

.blue-btn {
  background-color: white;
  color: var(--color-navy);
  border: none;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.blue-btn:hover {
  background-color: rgba(255, 255, 255, 0.9);
  color: var(--color-navy);
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.blue-card {
  background-color: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1),
              0 5px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.blue-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15),
              0 8px 20px rgba(0, 0, 0, 0.1);
}

.blue-card-header {
  background: linear-gradient(to right, var(--color-navy), var(--color-sky));
  color: white;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.blue-card-header h3 {
  color: white;
  margin: 0;
  font-size: 1.5rem;
}

.blue-card-body {
  padding: 1.5rem;
}

.blue-progress-container {
  margin-top: 1.5rem;
}

.blue-progress-label {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.blue-progress {
  height: 8px;
  background-color: rgba(var(--color-navy-rgb), 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.blue-progress-bar {
  height: 100%;
  background: linear-gradient(to right, var(--color-navy), var(--color-sky));
  border-radius: 4px;
  transition: width 1.5s cubic-bezier(0.1, 0.5, 0.2, 1);
}

/* İÇERİK DÜZENLEME BUTONLARI */
.content-edit-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--color-brown);
  color: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: 0.7;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  z-index: 10;
  border: none;
}

.content-edit-btn-bottom {
  position: absolute;
  bottom: 10px;
  right: 10px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--color-brown);
  color: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: 0.7;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  z-index: 10;
  border: none;
}

.content-edit-btn:hover, .content-edit-btn-bottom:hover {
  background-color: var(--color-moss);
  opacity: 1;
  transform: scale(1.1);
}

.profile-edit-btn {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--color-brown);
  color: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: 0.7;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  z-index: 10;
  border: 2px solid var(--white);
}

.profile-edit-btn:hover {
  background-color: var(--color-moss);
  opacity: 1;
  transform: scale(1.1);
}

/* PROFİL SAYFASI STİLLERİ */

/* Tema Renk Sınıfları */
.text-brown { color: var(--color-brown) !important; }
.text-navy { color: var(--color-navy) !important; }
.text-moss { color: var(--color-moss) !important; }

/* Buton Stilleri */
.btn-outline-brown {
    color: var(--color-brown);
    border-color: var(--color-brown);
}
.btn-outline-brown:hover {
    color: white;
    background-color: var(--color-brown);
}
.btn-outline-navy {
    color: var(--color-navy);
    border-color: var(--color-navy);
}
.btn-outline-navy:hover {
    color: white;
    background-color: var(--color-navy);
}
.btn-outline-moss {
    color: var(--color-moss);
    border-color: var(--color-moss);
}
.btn-outline-moss:hover {
    color: white;
    background-color: var(--color-moss);
}

/* Profil Sekmeler Stilleri */
.profile-tabs {
    border-bottom: 2px solid rgba(var(--color-brown-rgb), 0.15);
    margin-bottom: 2rem;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    background-color: rgba(255, 255, 255, 0.7);
}

.profile-tab {
    padding: 1.25rem 1rem;
    color: #555;
    font-weight: 600;
    font-size: 1.05rem;
    position: relative;
    transition: all 0.3s ease;
    text-align: center;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 3px solid transparent;
    margin-bottom: -2px;
}

.profile-tab i {
    margin-right: 8px;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.profile-tab:hover {
    color: var(--color-brown);
    text-decoration: none;
    background-color: rgba(var(--color-brown-rgb), 0.05);
}

.profile-tab:hover i {
    transform: translateY(-2px);
}

.profile-tab.active {
    color: var(--color-brown);
    font-weight: 700;
    border-bottom: 3px solid var(--color-brown);
}

.profile-tab.active i {
    color: var(--color-brown);
}

/* Profil Kart Stilleri */
.profile-card {
    background: linear-gradient(135deg,
        rgba(var(--color-sand-rgb), 0.2) 0%,
        rgba(var(--color-brown-rgb), 0.05) 100%);
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(var(--color-brown-rgb), 0.15);
    margin-bottom: 2rem;
    overflow: hidden;
    border: 1px solid rgba(var(--color-brown-rgb), 0.1);
}

.profile-header {
    padding: 1.5rem;
    display: flex;
    align-items: center;
    position: relative;
    background: linear-gradient(to right,
        rgba(var(--color-navy-rgb), 0.05),
        rgba(var(--color-brown-rgb), 0.05));
}

.profile-avatar-container {
    position: relative;
    margin-right: 0.5rem;
    flex-shrink: 0;
}

.profile-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid white;
    box-shadow: 0 5px 15px rgba(var(--color-brown-rgb), 0.2);
    transition: all 0.3s ease;
}

.profile-avatar:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(var(--color-brown-rgb), 0.3);
}

.profile-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding-left: 0;
}

.profile-info h4 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--color-navy);
    letter-spacing: -0.02em;
    line-height: 1.2;
}

.profile-info p {
    margin: 0.25rem 0 0;
    font-size: 1.1rem;
    color: var(--color-brown);
    font-weight: 500;
    line-height: 1.3;
}

/* Profil Düzenleme Butonu */
.profile-edit-btn {
    position: absolute;
    top: 2rem;
    right: 2rem;
}

.profile-edit-btn .btn {
    padding: 0.5rem 1.25rem;
    font-weight: 600;
    border-width: 2px;
    box-shadow: 0 4px 10px rgba(var(--color-brown-rgb), 0.15);
    transition: all 0.3s ease;
}

.profile-edit-btn .btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(var(--color-brown-rgb), 0.25);
}

/* Responsive Düzenlemeler */
@media (max-width: 768px) {
    .profile-header {
        flex-direction: column;
        text-align: center;
        padding: 1.5rem;
    }

    .profile-avatar-container {
        margin-right: 0;
        margin-bottom: 1.5rem;
    }

    .profile-info {
        align-items: center;
        margin-bottom: 3rem;
    }

    .profile-edit-btn {
        position: relative;
        top: auto;
        right: auto;
        margin-top: 1rem;
        display: flex;
        justify-content: center;
    }

    .profile-tabs {
        padding: 0;
        flex-wrap: wrap;
    }

    .profile-tab {
        padding: 1rem 0.5rem;
        font-size: 0.9rem;
    }

    .profile-tab i {
        margin-right: 5px;
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .profile-tabs {
        flex-wrap: nowrap;
        overflow-x: auto;
        justify-content: flex-start;
        padding-bottom: 5px;
    }

    .profile-tab {
        flex: 0 0 auto;
        padding: 0.75rem 1rem;
        white-space: nowrap;
    }
}

/* HAKKIMDA SAYFASI STİLLERİ */

/* Bilgi Satırları */
.info-row {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(var(--color-brown-rgb), 0.1);
}
.info-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
}
.info-label {
    display: block;
    font-size: 1.05rem;
    font-weight: 700;
    color: var(--color-navy);
    margin-bottom: 8px;
}
.info-data {
    font-size: 1.05rem;
    line-height: 1.7;
    color: #333;
}

/* Modern Kart Başlık Stilleri */
.modern-card .modern-card-header h3,
.modern-card-header h3 {
    display: flex;
    align-items: center;
    font-size: 1rem;
    margin: 0;
    font-weight: 600;
    letter-spacing: -0.02em;
}
.modern-card .modern-card-header h3 i,
.modern-card-header h3 i {
    margin-right: 10px;
    font-size: 1.2rem;
}

/* İletişim Bilgileri Stilleri */
.contact-item {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    background-color: rgba(var(--color-sand-rgb), 0.2);
    border-radius: 10px;
    transition: all 0.3s ease;
}
.contact-item:hover {
    background-color: rgba(var(--color-sand-rgb), 0.3);
    transform: translateX(5px);
}
.contact-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0;
    background-color: white;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
}
.contact-icon i {
    font-size: 1.1rem;
}
.contact-icon .bi-envelope-fill {
    color: var(--color-navy);
}
.contact-icon .bi-telephone-fill {
    color: var(--color-moss);
}
.contact-icon .bi-geo-alt-fill {
    color: var(--color-brown);
}
.contact-text {
    flex: 1;
}
.contact-label {
    display: block;
    font-size: 0.9rem;
    font-weight: 600;
    color: #555;
    margin-bottom: 2px;
}
.contact-value {
    font-size: 1.05rem;
    font-weight: 500;
    color: #222;
}
.empty-contact-message,
.empty-social-message {
    padding: 15px;
    background-color: rgba(var(--color-navy-rgb), 0.05);
    border-radius: 10px;
    border-left: 3px solid rgba(var(--color-navy-rgb), 0.3);
}

/* Sosyal Medya Butonları */
.social-links-container {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-top: 10px;
}
.profile-social-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 55px;
    height: 55px;
    background-color: white;
    border-radius: 50%;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    margin-right: 15px;
    border: 1px solid rgba(0, 0, 0, 0.05);
}
.profile-social-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 50%);
    z-index: 1;
}
.profile-social-link i {
    position: relative;
    z-index: 2;
    font-size: 1.5rem;
}
.profile-social-link:hover {
    transform: translateY(-8px) scale(1.1);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Sosyal Medya Platformları İçin Özel Renkler */
.profile-social-link .bi-instagram,
.social-link .bi-instagram {
    background: -webkit-linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);
    background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
    text-shadow: none;
}
.profile-social-link .bi-facebook,
.social-link .bi-facebook {
    color: #3b5998;
}
.profile-social-link .bi-twitter,
.social-link .bi-twitter {
    color: #1da1f2;
}
.profile-social-link .bi-linkedin,
.social-link .bi-linkedin {
    color: #0077b5;
}
.profile-social-link .bi-youtube,
.social-link .bi-youtube {
    color: #ff0000;
}
.profile-social-link:hover .bi-instagram,
.social-link:hover .bi-instagram {
    opacity: 0.9;
}
.profile-social-link:hover .bi-facebook,
.profile-social-link:hover .bi-twitter,
.profile-social-link:hover .bi-linkedin,
.profile-social-link:hover .bi-youtube,
.social-link:hover .bi-facebook,
.social-link:hover .bi-twitter,
.social-link:hover .bi-linkedin,
.social-link:hover .bi-youtube {
    opacity: 0.9;
}

/* Animasyonlu Bölüm Stilleri */
.section-divider {
    position: relative;
    height: 60px;
    text-align: center;
    margin: 2rem 0;
}
.section-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(to right,
        rgba(var(--color-brown-rgb), 0),
        rgba(var(--color-brown-rgb), 0.5),
        rgba(var(--color-brown-rgb), 0));
}
.section-divider-icon {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background-color: var(--soft-white);
    border-radius: 50%;
    color: var(--color-brown);
    font-size: 1.5rem;
    box-shadow: 0 4px 15px rgba(var(--color-brown-rgb), 0.1);
    z-index: 1;
}

/* Özellik Kartları */
.feature-card {
    background-color: white;
    border-radius: 16px;
    padding: 2rem;
    height: 100%;
    box-shadow: 0 10px 30px rgba(var(--color-brown-rgb), 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(var(--color-brown-rgb), 0.05);
    display: flex;
    flex-direction: column;
}
.feature-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background-color: rgba(var(--color-moss-rgb), 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    color: var(--color-moss);
    font-size: 2rem;
    transition: all 0.3s ease;
}

@media (max-width: 576px) {
    .contact-item {
        flex-direction: column;
        align-items: flex-start;
        padding: 12px;
    }
    .contact-icon {
        margin-bottom: 8px;
    }
    .social-links-container {
        justify-content: center;
    }
}

/* ORTAK KART STİLLERİ */
.profile-item-card {
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border: none;
    border-radius: 16px;
    overflow: hidden;
    margin-bottom: 20px;
}

.profile-item-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

/* ÇALIŞMALARIM SAYFASI STİLLERİ */
.work-card {
    /* Ortak kart stillerini miras al */
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border: none;
    border-radius: 16px;
    overflow: hidden;
    margin-bottom: 20px;
}

.work-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.work-card .card-header {
    background-color: var(--color-brown);
    color: white;
    font-weight: 600;
}

/* ARKADAŞLAR SAYFASI STİLLERİ */
.friend-card {
    /* Ortak kart stillerini miras al */
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border: none;
    border-radius: 16px;
    overflow: hidden;
    margin-bottom: 20px;
}

.friend-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.friend-img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 15px;
}

.friend-name {
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 5px;
}

.friend-info {
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* PROFİL DÜZENLEME SAYFASI STİLLERİ */

.profile-edit-container {
    max-width: 800px;
    margin: 0 auto;
}

.profile-edit-section {
    margin-bottom: 2rem;
    background-color: var(--soft-white);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.profile-edit-header {
    background-color: var(--color-brown);
    color: white;
    padding: 1rem 1.5rem;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
}

.profile-edit-body {
    padding: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--text-soft);
}

.form-control {
    border-radius: 8px;
    border: 1px solid rgba(var(--color-brown-rgb), 0.1);
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--color-brown);
    box-shadow: 0 0 0 0.2rem rgba(166, 111, 63, 0.25);
}

.form-check-input:checked {
    background-color: var(--color-brown);
    border-color: var(--color-brown);
}

.social-media-input {
    position: relative;
}

.social-media-input i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.2rem;
    color: var(--text-muted);
}

.social-media-input input {
    padding-left: 40px;
}

.btn-save {
    background-color: var(--color-brown);
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-save:hover {
    background-color: rgba(var(--color-brown-rgb), 0.85);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(var(--color-brown-rgb), 0.2);
}

.btn-cancel {
    background-color: var(--soft-white);
    color: var(--text-soft);
    border: 1px solid rgba(var(--color-brown-rgb), 0.1);
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-cancel:hover {
    background-color: var(--off-white);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(var(--color-brown-rgb), 0.1);
}

/* İLETİŞİM SAYFASI STİLLERİ */

/* İletişim Sayfası Özel Stilleri */
.iletisim-section .blue-section {
  margin-top: 3rem;
}

.iletisim-section .blue-card {
  overflow: visible;
}

.info-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(var(--color-sky-rgb), 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: white;
  box-shadow: 0 4px 10px rgba(var(--color-sky-rgb), 0.2);
}

.contact-info-box {
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.social-media-links {
  display: flex;
  gap: 10px;
}

.social-link.bg-moss {
  color: white !important;
  background-color: var(--color-sky) !important;
  box-shadow: 0 4px 10px rgba(var(--color-sky-rgb), 0.2);
  transition: all 0.3s ease;
}

.social-link.bg-moss:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(var(--color-sky-rgb), 0.3);
  background-color: rgba(var(--color-sky-rgb), 0.8) !important;
  color: white !important;
}

.whatsapp-link {
  text-decoration: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
}

.whatsapp-link:hover {
  transform: translateX(5px);
  color: #25D366 !important;
}

.whatsapp-link:hover i {
  transform: translateX(5px);
}

/* Mesaj kartı stilleri */
.blue-card {
  background-color: rgba(255, 255, 255, 0.9);
}

.blue-card-body {
  background-color: rgba(var(--soft-light-rgb), 0.7);
}

.blue-card .form-control {
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(var(--color-navy-rgb), 0.2);
}

.blue-card .form-control:focus {
  background-color: rgba(255, 255, 255, 0.95);
  border-color: rgba(var(--color-navy-rgb), 0.5);
  box-shadow: 0 0 0 0.25rem rgba(var(--color-navy-rgb), 0.15);
}

/* İletişim Sayfası Admin Stilleri */
.edit-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  transition: all 0.3s ease;
  margin-left: 5px;
}

.edit-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  transform: scale(1.1);
}

.social-media-item {
  position: relative;
  display: inline-block;
  margin: 0 0.5rem;
}

.social-edit-btn {
  position: absolute;
  top: -10px;
  right: -10px;
  width: 20px;
  height: 20px;
  font-size: 0.7rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.social-media-item:hover .social-edit-btn {
  opacity: 1;
}

.admin-only {
  display: inline-flex;
  opacity: 0.7;
}

.admin-only:hover {
  opacity: 1;
}

/*
 * CSS Optimizasyonu Tamamlandı
 * Son güncelleme: 12.05.2025
 */

