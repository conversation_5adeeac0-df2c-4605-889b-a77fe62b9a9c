/* 
* MODERN THEME CSS
* <PERSON><PERSON>, <PERSON><PERSON><PERSON>ı<PERSON>ı web sitesinin modern ve minimalist tasar<PERSON><PERSON> di<PERSON> tanımlar.
* Tüm sayfalar için ortak kullanılacak CSS stillerini içerir.
*/

/* GENEL SAYFA STİLLERİ */
.modern-section {
  background-color: var(--soft-white);
  position: relative;
  overflow: hidden;
}

.modern-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("../img/pattern.svg");
  background-repeat: repeat;
  background-size: 300px;
  opacity: 0.05;
  z-index: 0;
}

.modern-container {
  position: relative;
  z-index: 1;
}

.modern-header {
  text-align: center;
  padding-bottom: 2rem;
}

/* KART BİLEŞENİ */
.modern-card {
  background-color: var(--soft-light);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(var(--primary-color-rgb), 0.03);
  transition: all 0.3s ease;
  height: 100%;
  margin-bottom: 0;
  border: 1px solid rgba(var(--light-accent-rgb), 0.5);
}

/* İçeriğe göre otomatik boyutlanan kartlar için */
.modern-card.auto-height {
  height: auto !important;
  min-height: 0 !important;
}

.modern-card.auto-height .modern-card-body {
  height: auto !important;
  padding-bottom: 0 !important;
}

/* Kategori ve etiket kartları için özelleştirilmiş stiller */
.kategori-listesi {
  max-height: 250px;
  overflow-y: auto;
  scrollbar-width: thin;
  border-radius: 8px;
}

.kategori-listesi::-webkit-scrollbar {
  width: 8px;
}

.kategori-listesi::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.kategori-listesi::-webkit-scrollbar-thumb {
  background: var(--color-teal);
  border-radius: 10px;
}

/* Kartlar için içeriğe göre boyutlandırma */
.modern-card-header {
  padding-bottom: 10px;
}

/* Etiket bulutunun boşluklarını azalt */
.modern-tag-cloud {
  margin-bottom: 0;
  padding: 5px;
}

/* Etiketlerin kendi aralarındaki boşluğu azalt */
.modern-tag {
  margin-bottom: 5px;
}

.modern-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(var(--primary-color-rgb), 0.08);
}

.modern-card-header {
  padding: 1.5rem;
  border-bottom: 1px solid rgba(var(--accent-color-rgb), 0.1);
  position: relative;
  overflow: hidden;
}

.modern-card-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(to right, var(--color-teal), var(--color-orange));
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.modern-card:hover .modern-card-header::after {
  transform: scaleX(1);
}

.modern-card-body {
  padding: 1.5rem;
}

/* BİLGİ ÖĞELER */
.info-item {
  display: flex;
  align-items: start;
  margin-bottom: 1.25rem;
}

.info-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: rgba(var(--accent-color-rgb), 0.1);
  color: var(--primary-color);
  border-radius: 50%;
  margin-right: 1rem;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.info-item:hover .info-icon {
  background-color: var(--primary-color);
  color: var(--white);
  transform: scale(1.1);
}

.info-content {
  flex-grow: 1;
}

.info-title {
  display: block;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 0.25rem;
}

.info-value {
  display: block;
  color: var(--text-color);
}

/* SOSYAL MEDYA */
.social-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: var(--color-teal);
  color: var(--soft-white);
  border-radius: 50%;
  transition: all 0.3s ease;
  margin: 0 0.5rem;
  text-decoration: none;
  box-shadow: 0 3px 10px rgba(var(--color-teal-rgb), 0.2);
}

.social-link:hover {
  background-color: var(--color-rust);
  transform: translateY(-3px);
  color: var(--soft-white);
  box-shadow: 0 5px 15px rgba(var(--color-rust-rgb), 0.25);
}

/* FORM KONTROLLARI */
.modern-form .form-control {
  border: 1px solid rgba(var(--color-gray-rgb), 0.3);
  padding: 0.75rem 1rem;
  border-radius: 10px;
  transition: all 0.3s ease;
  background-color: var(--soft-light);
  color: var(--text-soft);
}

.modern-form .form-control:focus {
  border-color: var(--color-teal);
  box-shadow: 0 0 0 0.15rem rgba(var(--color-teal-rgb), 0.1);
  background-color: var(--soft-white);
}

.modern-form .form-label {
  color: var(--color-teal);
  font-weight: 600;
  margin-bottom: 0.5rem;
  opacity: 0.9;
}

/* BAŞLIK VE METİNLER */
.modern-title {
  color: var(--color-teal);
  font-weight: 700;
  margin-bottom: 1.5rem;
  position: relative;
  display: inline-block;
  padding-bottom: 10px;
  letter-spacing: -0.02em;
  text-shadow: 0 1px 1px rgba(var(--color-teal-rgb), 0.05);
}

.modern-btn {
  background: var(--soft-white);
  color: var(--color-teal);
  border: 2px solid var(--color-teal);
  padding: 0.75rem 2rem;
  border-radius: 30px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(var(--color-teal-rgb), 0.2);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.modern-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 18px rgba(var(--color-rust-rgb), 0.3);
  background: var(--color-rust);
  color: var(--soft-white);
  border-color: var(--color-rust);
}

/* RESİM KONTEYNER */
.image-container {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(var(--primary-color-rgb), 0.1);
}

.modern-image {
  width: 100%;
  height: auto;
  transition: all 0.5s ease;
}

.image-container:hover .modern-image {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 1rem;
  background: linear-gradient(to top, rgba(var(--secondary-color-rgb), 0.9), transparent);
  color: var(--white);
}

/* HERO SECTION */
.hero-section {
  position: relative;
  height: 100vh;
  min-height: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  padding: 0;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(rgba(var(--color-brown-rgb), 0.7), rgba(var(--color-teal-rgb), 0.7));
  z-index: 1;
}

.hero-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 0;
}

.hero-title {
  font-size: 4rem;
  font-weight: 700;
  color: var(--soft-white);
  margin-bottom: 1rem;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  position: relative;
  z-index: 2;
}

.hero-subtitle {
  font-size: 1.5rem;
  color: var(--soft-white);
  margin-bottom: 2rem;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  position: relative;
  z-index: 2;
}

.hero-btn {
  padding: 0.75rem 2.5rem;
  font-size: 1.1rem;
  background: var(--color-orange);
  color: var(--soft-white);
  border: 2px solid var(--color-orange);
  box-shadow: 0 4px 15px rgba(var(--color-orange-rgb), 0.3);
  position: relative;
  z-index: 2;
}

.hero-btn:hover {
  background: var(--color-rust);
  border-color: var(--color-rust);
  box-shadow: 0 6px 20px rgba(var(--color-rust-rgb), 0.4);
}

/* HEADER */
.modern-header {
  background-color: var(--soft-white);
  box-shadow: 0 2px 15px rgba(var(--color-teal-rgb), 0.1);
  padding: 15px 0;
  transition: all 0.3s ease;
  z-index: 100;
}

.modern-logo {
  color: var(--color-teal);
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  font-family: var(--heading-font);
  letter-spacing: -0.5px;
}

.modern-nav ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.modern-nav ul li {
  margin: 0 15px;
}

.modern-nav ul li a {
  color: var(--text-soft);
  font-weight: 500;
  text-decoration: none;
  padding: 10px 0;
  position: relative;
  transition: all 0.3s ease;
}

.modern-nav ul li a:hover,
.modern-nav ul li a.active {
  color: var(--color-teal);
}

.modern-nav ul li a::after {
  content: '';
  position: absolute;
  bottom: 5px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--color-teal);
  transition: all 0.3s ease;
}

.modern-nav ul li a:hover::after,
.modern-nav ul li a.active::after {
  width: 100%;
}

.modern-auth-buttons {
  display: flex;
  gap: 10px;
}

.modern-btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
}

.modern-btn-outline {
  background: transparent;
  color: var(--color-teal);
}

.modern-btn-outline:hover {
  background: var(--color-teal);
  color: var(--soft-white);
}

/* FOOTER */
.modern-footer {
  background-color: var(--soft-light);
  padding: 60px 0 30px;
  position: relative;
}

.modern-footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("../img/pattern.svg");
  background-repeat: repeat;
  background-size: 300px;
  opacity: 0.05;
  z-index: 0;
}

.modern-footer-title {
  color: var(--color-teal);
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  position: relative;
}

.modern-footer-text {
  color: var(--text-soft);
  margin-bottom: 1.5rem;
}

.modern-footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.modern-footer-links li {
  margin-bottom: 10px;
}

.modern-footer-links li a {
  color: var(--text-soft);
  text-decoration: none;
  transition: all 0.3s ease;
}

.modern-footer-links li a:hover {
  color: var(--color-teal);
  padding-left: 5px;
}

.modern-social-links {
  display: flex;
  gap: 10px;
}

.modern-footer-bottom {
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid rgba(var(--color-gray-rgb), 0.3);
  position: relative;
  z-index: 1;
}

.modern-link {
  color: var(--color-teal);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.modern-link:hover {
  color: var(--color-rust);
}

/* MODAL */
.modern-modal {
  border-radius: 15px;
  overflow: hidden;
  background-color: var(--soft-white);
  box-shadow: 0 10px 30px rgba(var(--color-teal-rgb), 0.2);
  border: none;
}

.modern-modal-header {
  background-color: var(--color-teal);
  color: var(--soft-white);
  padding: 1.5rem;
  border-bottom: none;
}

.modern-modal-title {
  color: var(--soft-white);
  font-weight: 600;
  margin: 0;
}

.modern-modal-body {
  padding: 2rem;
}

.modern-modal-footer {
  background-color: var(--soft-light);
  padding: 1rem;
  border-top: none;
}

.input-group-text {
  background-color: var(--color-teal);
  color: var(--soft-white);
  border: 1px solid var(--color-teal);
}

/* ÇALIŞMALAR SAYFASI */
.transition-all {
  transition: all 0.5s ease;
}

.object-fit-cover {
  object-fit: cover;
}

/* Swiper Özelleştirmeleri */
.swiper-button-next,
.swiper-button-prev {
  color: var(--color-teal) !important;
  background: rgba(var(--soft-white-rgb), 0.7);
  width: 40px !important;
  height: 40px !important;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.swiper-button-next:after,
.swiper-button-prev:after {
  font-size: 1.2rem !important;
  font-weight: bold;
}

.swiper-pagination-bullet-active {
  background: var(--color-teal) !important;
}

/* Sayfalama Stilleri */
.page-item.active .page-link {
  background-color: var(--color-teal);
  border-color: var(--color-teal);
}

.page-link {
  color: var(--color-teal);
}

.page-link:hover {
  color: var(--color-rust);
}

/* PROFİL SAYFASI STİLLERİ */
.modern-profile-header {
  display: flex;
  align-items: center;
  gap: 2rem;
  padding: 1.5rem;
  background-color: rgba(var(--light-accent-rgb), 0.1);
  border-radius: 16px;
  margin-bottom: 1.5rem;
}

.modern-profile-pic {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  border: 4px solid var(--soft-white);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.modern-profile-pic img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.modern-profile-pic .modern-btn-circle {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--color-teal);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.modern-profile-pic .modern-btn-circle:hover {
  background-color: var(--color-rust);
}

.modern-profile-info {
  flex-grow: 1;
}

.modern-profile-info h2 {
  margin-bottom: 0.5rem;
  color: var(--color-teal);
}

.modern-profile-info p {
  margin-bottom: 0.5rem;
  color: var(--text-color);
}

/* Profil Sekmeler */
.modern-tabs {
  display: flex;
  gap: 0.5rem;
  border-bottom: 1px solid rgba(var(--light-accent-rgb), 0.5);
  margin-bottom: 1.5rem;
  overflow-x: auto;
  padding-bottom: 0.5rem;
}

.modern-tab {
  padding: 0.75rem 1.25rem;
  border-radius: 8px 8px 0 0;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  color: var(--text-color);
  white-space: nowrap;
}

.modern-tab:hover {
  background-color: rgba(var(--light-accent-rgb), 0.2);
}

.modern-tab.active {
  background-color: var(--color-teal);
  color: white;
}

.modern-tab-content {
  margin-bottom: 2rem;
}

.modern-tab-pane {
  display: none;
}

.modern-tab-pane.active {
  display: block;
}

/* Profil Alanları */
.modern-profile-field {
  margin-bottom: 1.5rem;
}

.modern-profile-field label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--color-teal);
}

/* ZAMANCİZGİSİ */
.timeline {
  position: relative;
  padding-left: 2rem;
}

.timeline::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 2px;
  height: 100%;
  background-color: var(--accent-color);
}

.timeline-item {
  position: relative;
  padding-bottom: 1.5rem;
}

.timeline-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -2rem;
  width: 12px;
  height: 12px;
  background-color: var(--primary-color);
  border: 3px solid var(--accent-color);
  border-radius: 50%;
}

/* ALINTI KUTUSU */
.quote-box {
  font-style: italic;
  padding: 2rem;
  background-color: rgba(var(--accent-color-rgb), 0.05);
  border-radius: 12px;
  position: relative;
}

/* MODERN LİSTE VE KATEGORİLER */
.modern-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.modern-list li {
  margin-bottom: 0;
  border-bottom: 1px solid rgba(var(--light-accent-rgb), 0.2);
}

.modern-list li:last-child {
  border-bottom: none;
}

.modern-list-item {
  display: flex;
  padding: 12px 20px;
  text-decoration: none;
  color: var(--text-color);
  transition: all 0.3s ease;
}

.modern-list-item:hover {
  background-color: rgba(var(--accent-color-rgb), 0.1);
  color: var(--primary-color);
}

.modern-list-item.active {
  background-color: var(--color-teal);
  color: white;
}

.modern-badge {
  display: inline-block;
  padding: 3px 10px;
  background-color: rgba(var(--accent-color-rgb), 0.2);
  color: var(--text-color);
  border-radius: 20px;
  font-size: 0.8rem;
}

.modern-list-item.active .modern-badge {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
}

.modern-badge-corner {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: var(--color-teal);
  color: white;
  padding: 4px 10px;
  border-radius: 20px;
  font-size: 0.75rem;
  z-index: 1;
}

.modern-tag-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.modern-tag {
  display: inline-block;
  padding: 5px 12px;
  background-color: rgba(var(--light-accent-rgb), 0.3);
  color: var(--text-color);
  border-radius: 20px;
  font-size: 0.85rem;
  text-decoration: none;
  transition: all 0.3s ease;
}

.modern-tag:hover {
  background-color: rgba(var(--accent-color-rgb), 0.3);
  color: var(--primary-color);
}

.modern-tag.active {
  background-color: var(--color-teal);
  color: white;
}

/* VİDEO OYNATICI */
.modern-video-player {
  position: relative;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  height: 0;
  overflow: hidden;
  border-radius: 12px 12px 0 0;
}

/* VİDEO KARTLARI */
.video-card {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.video-thumbnail {
  position: relative;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  overflow: hidden;
}

.video-thumbnail img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.video-card:hover .video-thumbnail img {
  transform: scale(1.05);
}

.video-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.8rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 2.5rem; /* Yaklaşık 2 satır */
}

/* BEĞENİ BUTONU */
.modern-like-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: var(--soft-light);
  border: 1px solid rgba(var(--accent-color-rgb), 0.3);
  border-radius: 50px;
  color: var(--text-color);
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
}

.modern-like-btn:hover {
  background-color: rgba(var(--accent-color-rgb), 0.1);
  border-color: rgba(var(--accent-color-rgb), 0.5);
}

.modern-like-btn.active {
  background-color: var(--color-teal);
  border-color: var(--color-teal);
  color: white;
}

.modern-like-btn i {
  font-size: 1.1rem;
}

/* ETİKET BULUTU */
.modern-tag-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  max-height: 120px;
  overflow-y: auto;
  padding-right: 5px;
}

.modern-video-player iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
}

.modern-play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 3rem;
  opacity: 0.8;
  transition: opacity 0.3s ease, transform 0.3s ease;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  z-index: 2;
}

.modern-card:hover .modern-play-button {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1.1);
}

/* SAYFALAMA */
.modern-pagination {
  display: flex;
  justify-content: center;
  gap: 5px;
  padding: 0;
  list-style: none;
  margin-top: 2rem;
}

.modern-page-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--soft-light);
  color: var(--text-color);
  text-decoration: none;
  transition: all 0.3s ease;
  border: 1px solid rgba(var(--light-accent-rgb), 0.3);
}

.modern-page-link:hover {
  background-color: rgba(var(--accent-color-rgb), 0.1);
  color: var(--primary-color);
}

.modern-page-link.active {
  background-color: var(--color-teal);
  color: white;
  border-color: var(--color-teal);
}

/* VİDEO DETAY SAYFASI */
.modern-video-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1rem;
}

.modern-video-content {
  padding: 0;
  overflow: hidden;
}

.modern-video-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.modern-info-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.modern-info-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(var(--light-accent-rgb), 0.2);
}

.modern-info-item:last-child {
  border-bottom: none;
}

.modern-sidebar-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 0;
  list-style: none;
}

.modern-sidebar-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  text-decoration: none;
  color: var(--text-color);
}

.modern-sidebar-item:hover {
  background-color: rgba(var(--accent-color-rgb), 0.1);
}

.modern-sidebar-thumbnail {
  width: 80px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  flex-shrink: 0;
}

.modern-sidebar-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.modern-sidebar-info {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.quote-box::before {
  content: '\201C';
  position: absolute;
  top: 0;
  left: 10px;
  font-size: 4rem;
  color: rgba(var(--accent-color-rgb), 0.2);
  line-height: 1;
}

/* YETENEK BARI */
.skill-item {
  margin-bottom: 1rem;
}

.skill-name {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.skill-bar {
  height: 5px;
  background-color: rgba(var(--accent-color-rgb), 0.2);
  border-radius: 5px;
  overflow: hidden;
}

.skill-progress {
  height: 100%;
  background: linear-gradient(to right, var(--primary-color), var(--accent-color));
  border-radius: 5px;
}

/* BÖLÜM AYIRICI */
.section-divider {
  width: 100%;
  height: 60px;
  position: relative;
  margin: 3rem 0;
}

.section-divider::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(to right, transparent, var(--accent-color), transparent);
}

.section-divider-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 50px;
  height: 50px;
  background-color: var(--off-white);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  color: var(--primary-color);
  font-size: 1.5rem;
}

/* HARİTA KONTEYNER */
.map-container {
  border-radius: 12px;
  overflow: hidden;
}

/* BAŞLIK STİLLERİ */
.gradient-heading {
  background: linear-gradient(to right, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent; /* Eski tarayıcılar için destek */
  display: inline-block;
  position: relative;
}

/* YARDIMCI SINIFLAR */
.text-inherit {
  color: inherit;
}

.text-inherit:hover {
  color: var(--color-teal);
}

/* RESPONSİVE DÜZENLEMELER */
@media (max-width: 768px) {
  .modern-card {
    margin-bottom: 1.5rem;
  }
  
  .timeline {
    padding-left: 1.5rem;
  }
  
  .timeline-item::before {
    left: -1.5rem;
  }
  
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.2rem;
  }
  
  .modern-profile-header {
    flex-direction: column;
    text-align: center;
  }
  
  .modern-profile-pic {
    margin: 0 auto 1rem;
  }
  
  .modern-tabs {
    justify-content: center;
  }
}

@media (max-width: 576px) {
  .modern-section {
    padding: 2rem 0;
  }
  
  .modern-header {
    padding-bottom: 1rem;
  }
  
  .modern-card-header, 
  .modern-card-body {
    padding: 1rem;
  }
  
  .hero-section {
    min-height: 450px;
  }
  
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-btn {
    padding: 0.5rem 1.5rem;
    font-size: 1rem;
  }
  
  .info-item {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .info-icon {
    margin-right: 0;
    margin-bottom: 0.5rem;
  }
  
  .modern-pagination .modern-page-link {
    width: 35px;
    height: 35px;
  }
}
