/* <PERSON><PERSON><PERSON> */
.new-header {
    background-color: var(--soft-white);
    padding: 15px 0;
    box-shadow: 0px 0 18px rgba(0, 0, 0, 0.1);
}

.new-header .logo {
    line-height: 1;
}

.new-header .logo h1 {
    font-size: 32px;
    margin: 0;
    font-weight: 300;
    text-transform: uppercase;
    color: var(--heading-color);
}

.new-navmenu ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.new-navmenu ul li {
    display: inline-block;
    margin-right: 15px;
}

.new-navmenu ul li a {
    color: var(--nav-color);
    text-decoration: none;
}

.new-auth-buttons {
    display: flex;
    align-items: center;
}

.new-auth-buttons .new-btn {
    display: inline-block;
    margin-left: 10px;
    padding: 8px 15px;
    border-radius: 5px;
    background-color: var(--accent-color);
    color: var(--soft-white);
    text-decoration: none;
}

.new-auth-buttons .new-btn-outline {
    background-color: transparent;
    border: 1px solid var(--accent-color);
    color: var(--accent-color);
}
/* 
 * Küp Cadısı - Birleştirilmiş Stil Dosyası
 * Bu dosya, tüm sayfalar için ortak stilleri içerir.
 * Son güncelleme: 07.05.2025
 */

/* Not: Ana renk değişkenleri main.css dosyasında tanımlanmıştır */
:root {
    /* RGB versiyonları - JavaScript ve RGBA kullanımı için */
    --primary-color-rgb: 166, 111, 63; /* Kahverengi */
    --secondary-color-rgb: 66, 112, 140; /* Koyu mavi */
    --accent-color-rgb: 101, 115, 81; /* Yosun yeşili */
    --light-accent-rgb: 128, 167, 191; /* Açık mavi */
    
    /* Ek renkler */
    --dark-bg: #2D1A00;
    --dark-bg-rgb: 45, 26, 0;
    --text-color: var(--default-color); /* main.css ile uyumlu */
    --text-color-rgb: 64, 36, 1;
    --light-text: var(--heading-color); /* main.css ile uyumlu */
    --light-text-rgb: 115, 68, 41;
    --white: #ffffff;
    --white-rgb: 255, 255, 255;
    --off-white: #F8F5F0;
    --off-white-rgb: 248, 245, 240;
    --light-gray: #E9E4D9;
    --light-gray-rgb: 233, 228, 217;
    
    /* Durum renkleri */
    --success-color: #2E7D32;
    --success-color-rgb: 46, 125, 50;
    --error-color: #C62828;
    --error-color-rgb: 198, 40, 40;
}

/* Modal Stilleri */
.modal-backdrop {
    background-color: rgba(var(--secondary-color-rgb), 0.2);
}

/* Modal Animasyonları */
@keyframes modalFadeIn {
    from { opacity: 0; transform: translateY(-30px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes modalContentFadeIn {
    0% { opacity: 0; transform: translateY(20px); }
    100% { opacity: 1; transform: translateY(0); }
}

.custom-modal.fade .modal-dialog {
    transform: translate(0, -30px);
    transition: transform 0.3s ease-out;
}

.custom-modal.show .modal-dialog {
    transform: none;
}

.custom-modal.show .modal-content {
    animation: modalContentFadeIn 0.5s ease-out forwards;
}

.custom-modal.show .login-form-field:nth-child(1) {
    animation: modalContentFadeIn 0.5s ease-out 0.1s both;
}

.custom-modal.show .login-form-field:nth-child(2) {
    animation: modalContentFadeIn 0.5s ease-out 0.2s both;
}

.custom-modal.show .login-button-container {
    animation: modalContentFadeIn 0.5s ease-out 0.3s both;
}

/* Login Modal Stilleri */
.custom-modal .modal-dialog {
    max-width: 420px;
}

.custom-modal .modal-content {
    border: none;
    border-radius: 20px;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.97), rgba(248, 249, 250, 0.95));
    box-shadow: 0 15px 35px rgba(var(--secondary-color-rgb), 0.15);
    overflow: hidden;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.login-header {
    border-bottom: 1px solid rgba(var(--secondary-color-rgb), 0.08);
    padding: 1.75rem;
    background: linear-gradient(to right, rgba(var(--accent-color-rgb), 0.05), rgba(var(--primary-color-rgb), 0.08));
    position: relative;
}

.login-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0;
    text-shadow: 1px 1px 1px rgba(0,0,0,0.05);
}

.login-body {
    padding: 2rem;
    background-color: transparent;
}

.login-footer {
    border-top: 1px solid rgba(var(--secondary-color-rgb), 0.08);
    padding: 1.5rem;
    background: linear-gradient(to right, rgba(var(--accent-color-rgb), 0.05), rgba(var(--primary-color-rgb), 0.08));
    text-align: center;
}

.login-form-field {
    background: linear-gradient(to right, rgba(248, 249, 250, 0.7), rgba(248, 249, 250, 0.9));
    padding: 1.25rem;
    border-radius: 15px;
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
    box-shadow: 0 3px 10px rgba(0,0,0,0.03);
    border-left: 3px solid transparent;
}

.login-form-field:hover {
    background-color: rgba(233, 236, 239, 0.9);
    transform: translateX(5px);
    border-left: 3px solid var(--primary-color);
}

.login-form-field:focus-within {
    border-left: 3px solid var(--primary-color);
    background-color: rgba(233, 236, 239, 0.9);
    transform: translateX(5px);
}

.login-input {
    border: none;
    background: transparent;
    width: 100%;
    padding: 0.5rem 0;
    color: var(--text-color);
    font-size: 1rem;
}

.login-input:focus {
    outline: none;
    box-shadow: none;
}

.login-input::placeholder {
    color: rgba(var(--text-color-rgb), 0.5);
    font-weight: 400;
}

.login-icon {
    color: var(--primary-color);
    font-size: 1.2rem;
    margin-right: 1rem;
}

.login-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    width: 100%;
    margin-top: 1rem;
    box-shadow: 0 5px 15px rgba(var(--primary-color-rgb), 0.2);
}

.login-btn:hover {
    background: var(--secondary-color);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(var(--primary-color-rgb), 0.3);
}

.login-link {
    color: var(--primary-color);
    font-weight: 500;
    text-decoration: none;
    position: relative;
    transition: all 0.3s ease;
}

.login-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -2px;
    left: 0;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

.login-link:hover {
    color: var(--secondary-color);
}

.login-link:hover::after {
    width: 100%;
}

.custom-close {
    position: absolute;
    right: 1.5rem;
    top: 1.5rem;
    background: rgba(var(--light-gray-rgb), 0.5);
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    border: none;
    transition: all 0.3s ease;
}

.custom-close:hover {
    background: rgba(var(--accent-color-rgb), 0.5);
    color: var(--secondary-color);
    transform: rotate(90deg);
}

/* Video Detay Sayfası Stilleri */
.video-detail-section {
    background: linear-gradient(135deg, var(--off-white) 0%, var(--light-accent) 100%);
    padding: 4rem 0;
}

.video-player {
    position: relative;
    width: 100%;
    padding-bottom: 56.25%; /* 16:9 */
    height: 0;
    overflow: hidden;
}

.video-player iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 15px;
}

.video-content {
    padding: 1.5rem;
    padding-bottom: 1rem;
}

.video-title {
    color: var(--primary-color);
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.4;
}

.category-badge {
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
    color: var(--white);
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 500;
}

.video-date {
    color: var(--light-text);
    font-size: 0.9rem;
}

.video-description {
    color: var(--text-color);
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 1.5rem;
}

.video-actions {
    border-top: 1px solid var(--light-gray);
    padding-top: 1.5rem;
}

.video-views {
    color: var(--light-text);
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.like-button {
    background: none;
    border: none;
    color: var(--light-text);
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 0;
    transition: all 0.3s ease;
}

.like-button:hover, .like-button.active {
    color: var(--accent-color);
    transform: scale(1.1);
}

.similar-video-card {
    text-decoration: none;
    color: inherit;
    display: block;
}

.video-card {
    border-radius: 20px;
    overflow: hidden;
    background: var(--white);
    box-shadow: 0 10px 25px rgba(0,0,0,0.05);
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.video-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 35px rgba(var(--secondary-color-rgb), 0.15);
}

.video-card:hover .video-play {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
}

.video-thumbnail {
    position: relative;
    height: 180px;
    overflow: hidden;
}

.video-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.video-card:hover .video-thumbnail img {
    transform: scale(1.1);
}

.video-play {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.8);
    width: 60px;
    height: 60px;
    background: rgba(var(--white-rgb), 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.8;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.video-play i {
    color: var(--primary-color);
    font-size: 1.5rem;
}

.video-info {
    padding: 1.5rem;
}

.video-info h3 {
    color: var(--primary-color);
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    line-height: 1.4;
    transition: color 0.3s ease;
}

.video-card:hover .video-info h3 {
    color: var(--accent-color);
}

.video-meta {
    display: flex;
    justify-content: space-between;
    color: var(--light-text);
    font-size: 0.9rem;
}

.play-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(var(--primary-color-rgb), 0.7);
    color: var(--white);
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.popular-video-link:hover .play-icon {
    opacity: 1;
}

.popular-video-info {
    flex-grow: 1;
}

.popular-video-title {
    font-size: 0.95rem;
    color: var(--text-color);
    margin-bottom: 0.5rem;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.popular-video-meta {
    display: flex;
    flex-direction: column;
    font-size: 0.8rem;
    color: var(--light-text);
    gap: 5px;
}

.video-info-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.video-info-list li {
    display: flex;
    justify-content: space-between;
    padding: 0.8rem 0;
    border-bottom: 1px solid var(--light-gray);
    color: var(--text-color);
    font-size: 0.95rem;
}

.video-info-list li:last-child {
    border-bottom: none;
}

.video-info-list li span:first-child {
    font-weight: 600;
    color: var(--primary-color);
}

/* Animasyonlar */
.wow {
    animation: fadeIn 0.8s ease forwards;
    visibility: visible;
}

.fadeIn { animation-name: fadeIn; }
.fadeInUp { animation-name: fadeInUp; }
.fadeInRight { animation-name: fadeInRight; }

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeInUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeInRight {
    from { opacity: 0; transform: translateX(20px); }
    to { opacity: 1; transform: translateX(0); }
}

/* Ürünler Sayfası Stilleri */
.products-section {
    background: linear-gradient(135deg, var(--off-white) 0%, var(--light-accent) 100%);
    padding: 4rem 0;
}

/* Kategori Filtreleri */
.filter-container {
    margin-bottom: 2rem;
}

.category-filters {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
}

.filter-btn {
    padding: 0.7rem 1.5rem;
    background: var(--white);
    color: var(--text-color);
    border: none;
    border-radius: 50px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 3px 10px rgba(0,0,0,0.05);
}

.filter-btn:hover {
    background: var(--light-accent);
    transform: translateY(-3px);
}

.filter-btn.active {
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
    color: var(--white);
}

/* Ürün Kartları */
.product-card {
    border-radius: 20px;
    overflow: hidden;
    background: var(--white);
    box-shadow: 0 10px 25px rgba(0,0,0,0.05);
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    height: 100%;
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 35px rgba(var(--secondary-color-rgb), 0.15);
}

.product-image {
    position: relative;
    height: 280px;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.1);
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(var(--secondary-color-rgb), 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.product-card:hover .product-overlay {
    opacity: 1;
}

.product-actions {
    display: flex;
    gap: 15px;
}

.product-zoom, .product-link {
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--white);
    color: var(--primary-color);
    border-radius: 50%;
    font-size: 1.2rem;
    transform: translateY(20px);
    opacity: 0;
    transition: all 0.3s ease;
    text-decoration: none;
}

.product-card:hover .product-zoom,
.product-card:hover .product-link {
    transform: translateY(0);
    opacity: 1;
}

.product-zoom:hover, .product-link:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: scale(1.1);
}

.product-category {
    position: absolute;
    top: 15px;
    left: 15px;
    background: var(--primary-color);
    color: var(--white);
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.8rem;
    font-weight: 500;
    z-index: 1;
}

.product-info {
    padding: 1.5rem;
}

.product-title {
    color: var(--text-color);
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.8rem;
    transition: color 0.3s ease;
}

.product-card:hover .product-title {
    color: var(--primary-color);
}

.product-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.product-category-badge {
    background: var(--light-gray);
    color: var(--text-color);
    padding: 0.3rem 0.8rem;
    border-radius: 50px;
    font-size: 0.8rem;
}

.product-price {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 1.1rem;
}

.empty-state {
    text-align: center;
    padding: 3rem;
    background: var(--white);
    border-radius: 20px;
    box-shadow: 0 10px 25px rgba(0,0,0,0.05);
}

.empty-state i {
    font-size: 3rem;
    color: var(--light-text);
    margin-bottom: 1rem;
    display: block;
}

.empty-state p {
    color: var(--text-color);
    font-size: 1.1rem;
}

/* Ürün Detay Sayfası Stilleri */
.product-detail-section {
    background: linear-gradient(135deg, var(--off-white) 0%, var(--light-accent) 100%);
    padding: 4rem 0;
}

.product-slide-image {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    height: 400px;
}

.product-slide-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-zoom {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 40px;
    height: 40px;
    background: rgba(var(--white-rgb), 0.9);
    color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    text-decoration: none;
    opacity: 0.8;
}

.image-zoom:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: scale(1.1);
    opacity: 1;
}

.product-description-card, .sidebar-card {
    border-radius: 20px;
    overflow: hidden;
    background: var(--white);
    box-shadow: 0 10px 25px rgba(0,0,0,0.05);
    margin-bottom: 2rem;
}

.card-header, .sidebar-header {
    padding: 1.5rem;
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
    color: var(--white);
    border-radius: 20px 20px 0 0;
}

.card-body, .sidebar-body {
    padding: 1.5rem;
}

.product-description {
    color: var(--text-color);
    font-size: 1.1rem;
    line-height: 1.8;
}

.product-info-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.product-info-list li {
    display: flex;
    justify-content: space-between;
    padding: 0.8rem 0;
    border-bottom: 1px solid var(--light-gray);
    color: var(--text-color);
    font-size: 0.95rem;
}

.product-info-list li:last-child {
    border-bottom: none;
}

.info-title {
    font-weight: 600;
    color: var(--primary-color);
}

.info-value {
    color: var(--text-color);
}

.info-value.in-stock {
    color: #28a745;
    font-weight: 600;
}

.info-value.out-of-stock {
    color: #dc3545;
    font-weight: 600;
}

.info-value.price {
    color: var(--primary-color);
    font-weight: 700;
    font-size: 1.2rem;
}

.buy-button-wrapper {
    margin-top: 1.5rem;
    text-align: center;
}

.buy-button {
    width: 100%;
    padding: 1rem;
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
    color: var(--white);
    border: none;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.buy-button:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(var(--primary-color-rgb), 0.3);
}

.tag-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.tag-item {
    background: var(--light-gray);
    color: var(--text-color);
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.9rem;
    text-decoration: none;
    transition: all 0.3s ease;
}

.tag-item:hover {
    background: var(--accent-color);
    color: var(--white);
    transform: translateY(-3px);
}

.no-tags, .no-similar {
    color: var(--light-text);
    font-style: italic;
}

.similar-products {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.similar-product-item {
    display: flex;
    gap: 12px;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
    padding: 10px;
    border-radius: 10px;
}

.similar-product-item:hover {
    background: var(--light-gray);
    transform: translateX(5px);
}

.similar-product-image {
    width: 70px;
    height: 70px;
    border-radius: 10px;
    overflow: hidden;
    flex-shrink: 0;
}

.similar-product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.similar-product-item:hover .similar-product-image img {
    transform: scale(1.1);
}

.similar-product-info {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.similar-product-title {
    color: var(--text-color);
    font-size: 0.95rem;
    margin-bottom: 5px;
    transition: color 0.3s ease;
}

.similar-product-item:hover .similar-product-title {
    color: var(--primary-color);
}

.similar-product-price {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 0.9rem;
}

/* Swiper Özelleştirmeleri */
.swiper-button-next, .swiper-button-prev {
    color: var(--primary-color);
    background: rgba(var(--white-rgb), 0.8);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.swiper-button-next:hover, .swiper-button-prev:hover {
    background: var(--primary-color);
    color: var(--white);
}

.swiper-button-next:after, .swiper-button-prev:after {
    font-size: 1.2rem;
}

.swiper-pagination-bullet {
    background: var(--primary-color);
    opacity: 0.5;
}

.swiper-pagination-bullet-active {
    opacity: 1;
    background: var(--primary-color);
}

/* Üyelik Sayfaları Stilleri */
.auth-section {
    background: linear-gradient(135deg, var(--light-accent) 0%, var(--accent-color) 100%);
    min-height: calc(100vh - 200px);
    padding: 3rem 0;
    display: flex;
    align-items: center;
}

/* Profil Sayfası Stilleri */
.cover-photo {
    width: 100%;
    height: 300px;
    background-image: url('../img/cover-default.jpg');
    background-size: cover;
    background-position: center;
    position: relative;
    background-color: var(--light-accent);
}

.profile-container {
    width: 90%;
    max-width: 1000px;
    margin: -80px auto 40px;
    background: var(--white);
    border-radius: 8px;
    box-shadow: 0 0 15px rgba(0,0,0,0.1);
    padding-top: 100px;
    position: relative;
}

.profile-pic {
    width: 160px;
    height: 160px;
    border-radius: 50%;
    border: 5px solid var(--white);
    position: absolute;
    top: -80px;
    left: 30px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.profile-pic img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-pic-edit {
    position: absolute;
    bottom: 5px;
    right: 5px;
    opacity: 0.9;
    transition: opacity 0.3s;
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
}

.profile-pic-edit:hover {
    opacity: 1;
}

.profile-info {
    margin-left: 210px;
    padding: 10px 20px;
}

.profile-info h1 {
    margin: 0;
    font-size: 26px;
    color: var(--primary-color);
}

.profile-info p {
    color: #666;
    margin-top: 4px;
}

.tabs {
    margin-top: 20px;
    border-top: 1px solid #ddd;
    display: flex;
}

.tab {
    flex: 1;
    padding: 12px 16px;
    text-align: center;
    cursor: pointer;
    font-weight: bold;
    color: #555;
    transition: all 0.3s;
}

.tab:hover {
    background: var(--off-white);
    color: var(--primary-color);
}

.tab.active {
    border-bottom: 3px solid var(--primary-color);
    color: var(--primary-color);
}

.content {
    padding: 20px;
}

.content h2 {
    margin-top: 0;
    color: var(--primary-color);
    border-bottom: 1px solid var(--light-accent);
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.profile-field {
    background-color: var(--white);
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    margin-bottom: 15px;
    border: 1px solid var(--light-accent);
}

.profile-field:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border-color: #d0c8b8;
}

.form-label {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.field-value {
    color: var(--secondary-color);
}

.edit-btn, .save-btn, .cancel-btn {
    width: 36px;
    height: 36px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.edit-btn:hover {
    background-color: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.btn-kupcadisi {
    background-color: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.btn-kupcadisi:hover {
    background-color: var(--secondary-color);
    color: var(--white);
    border-color: var(--secondary-color);
}

.btn-outline-kupcadisi {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-kupcadisi:hover {
    background-color: var(--primary-color);
    color: var(--white);
}

.about-section {
    background: var(--off-white);
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.alert-warning {
    background-color: #f8f0e3;
    border-color: #e6d5b8;
    color: var(--primary-color);
}

.auth-card {
    border: none;
    border-radius: 20px;
    background-color: rgba(var(--white-rgb), 0.95);
    box-shadow: 0 15px 35px rgba(var(--secondary-color-rgb), 0.1);
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    overflow: hidden;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    transform: translateY(0);
    animation: fadeIn 0.6s ease forwards;
}

.auth-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(var(--secondary-color-rgb), 0.15);
}

.card-body {
    padding: 2.5rem !important;
}

.auth-title {
    font-size: 2.25rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.form-field {
    background: linear-gradient(to right, rgba(248, 249, 250, 0.7), rgba(248, 249, 250, 0.9));
    padding: 1.25rem;
    border-radius: 15px;
    transition: all 0.3s ease;
    margin-bottom: 1.5rem !important;
    box-shadow: 0 3px 10px rgba(0,0,0,0.03);
    border-left: 3px solid transparent;
    animation: slideIn 0.5s ease forwards;
}

.form-field:nth-child(1) { animation-delay: 0.1s; }
.form-field:nth-child(2) { animation-delay: 0.2s; }
.form-field:nth-child(3) { animation-delay: 0.3s; }

.col-lg-8.wow.fadeInUp {
    animation-delay: 0.3s;
}



.form-field:hover {
    background-color: rgba(233, 236, 239, 0.9);
    transform: translateX(5px);
    border-left: 3px solid var(--primary-color);
}

.form-field:focus-within {
    border-left: 3px solid var(--primary-color);
    background-color: rgba(233, 236, 239, 0.9);
    transform: translateX(5px);
}

.form-label {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 0.75rem;
    display: block;
}

.input-group {
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
    border-radius: 50px;
    overflow: hidden;
}

.input-group-text {
    background-color: var(--white);
    border-right: none;
    padding-left: 1.5rem;
    color: var(--primary-color);
    border-top-left-radius: 50px;
    border-bottom-left-radius: 50px;
    border: 2px solid transparent;
    border-right: none;
    transition: all 0.3s ease;
}

.form-control {
    border-left: none;
    padding: 0.75rem 1.5rem 0.75rem 0.75rem;
    font-size: 1rem;
    border: 2px solid transparent;
    border-left: none;
    border-top-right-radius: 50px;
    border-bottom-right-radius: 50px;
    transition: all 0.3s ease;
}

.form-control:focus {
    box-shadow: none;
    border-color: var(--primary-color);
}

.form-control:focus + .input-group-text {
    border-color: var(--primary-color);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border: none;
    padding: 1rem;
    font-weight: 600;
    border-radius: 50px;
    box-shadow: 0 5px 15px rgba(var(--secondary-color-rgb), 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
    transition: all 0.5s ease;
    z-index: -1;
}

.btn-primary:hover::before {
    left: 0;
}

.btn-primary:hover {
    box-shadow: 0 8px 25px rgba(var(--secondary-color-rgb), 0.3);
    transform: translateY(-3px);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 3px 10px rgba(var(--secondary-color-rgb), 0.2);
}

.text-primary {
    color: var(--primary-color) !important;
    position: relative;
    display: inline-block;
    font-weight: 600;
    transition: all 0.3s ease;
}

.text-primary::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -2px;
    left: 0;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

.text-primary:hover {
    color: var(--secondary-color) !important;
}

.text-primary:hover::after {
    width: 100%;
}

.alert-danger {
    background-color: rgba(248, 215, 218, 0.8);
    border-color: #f5c6cb;
    color: #721c24;
    border-radius: 10px;
    padding: 1rem;
    animation: shakeX 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
}

@keyframes shakeX {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

@keyframes slideIn {
    from { opacity: 0; transform: translateX(-20px); }
    to { opacity: 1; transform: translateX(0); }
}

/* Üyelik sayfaları medya sorguları */
@media (max-width: 768px) {
    .auth-title {
        font-size: 1.75rem;
    }
    
    .auth-card {
        margin: 0 1rem;
    }
    
    .card-body {
        padding: 1.5rem !important;
    }
    
    .form-field {
        padding: 1rem;
    }
}

/* Genel sayfa stilleri */
.page-section {
    background: linear-gradient(135deg, var(--off-white) 0%, var(--light-accent) 100%);
    padding: 4rem 0;
}

/* Başlık bölümü */
.section-header {
    margin-bottom: 3rem;
}

.section-title {
    color: var(--primary-color);
    font-weight: 700;
    position: relative;
}

.section-subtitle {
    color: var(--light-text);
    font-weight: 400;
}

.title-decoration {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-top: 20px;
}

.title-decoration span {
    height: 8px;
    border-radius: 4px;
}

/* Kartlar ve Bileşenler */
.card-container {
    margin-bottom: 30px;
}

.sidebar-card, .content-card {
    background-color: var(--white);
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    margin-bottom: 25px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.sidebar-card:hover, .content-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.sidebar-header {
    padding: 15px 20px;
    background: linear-gradient(to right, var(--accent-color), var(--light-accent)); 
    color: var(--secondary-color); 
    font-weight: 600; 
    text-shadow: 0 1px 1px rgba(255,255,255,0.5);
    padding: 1.5rem; 
    border-radius: 20px 20px 0 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.sidebar-header h5, .card-header h5 {
    margin: 0;
    color: var(--primary-color);
    font-weight: 600;
}

.sidebar-body, .card-body {
    padding: 20px;
}

/* Profil ve Kullanıcı Bilgileri */
.profile-image-container {
    width: 150px;
    height: 150px;
    margin: 0 auto 20px;
    border-radius: 50%;
    overflow: hidden;
    border: 5px solid var(--light-accent);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.profile-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Sosyal Medya İkonları */
.social-share {
    text-align: center;
}

.social-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: var(--white);
    margin: 0 5px;
    transition: all 0.3s ease;
}

.social-icon:hover {
    background-color: var(--secondary-color);
    transform: translateY(-3px);
}

/* Bilgi Listeleri */
.info-list, .contact-info-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.info-list li, .contact-info-list li {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--light-gray);
}

.info-list li:last-child, .contact-info-list li:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.info-title, .info-value {
    display: block;
}

.info-title {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.info-value {
    color: var(--text-color);
}

/* Zaman Çizelgesi */
.timeline-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.timeline-item {
    padding-bottom: 25px;
    position: relative;
    margin-bottom: 25px;
    border-bottom: 1px solid var(--light-gray);
}

.timeline-item:last-child {
    padding-bottom: 0;
    margin-bottom: 0;
    border-bottom: none;
}

.timeline-date {
    display: inline-block;
    padding: 5px 15px;
    background-color: var(--accent-color);
    color: var(--secondary-color);
    border-radius: 50px;
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 10px;
}

.timeline-title {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 5px;
}

/* Butonlar */
.action-button {
    display: inline-block;
    padding: 10px 25px;
    background-color: var(--primary-color);
    color: var(--white);
    border-radius: 50px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    text-align: center;
}

.action-button:hover {
    background-color: var(--secondary-color);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.contact-button {
    width: 100%;
    margin-top: 15px;
}

/* Form Elemanları */
.form-control {
    border: 1px solid var(--light-gray);
    padding: 12px 15px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.25rem rgba(214, 179, 145, 0.25);
}

.form-label {
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: 8px;
}

.form-group {
    margin-bottom: 20px;
}

/* Harita */
.map-container {
    border-radius: 0 0 10px 10px;
    overflow: hidden;
    height: 400px;
}

.map-container iframe {
    width: 100%;
    height: 100%;
    border: 0;
}

/* Çalışma Detay Sayfası */
.calisma-detail-section, .product-detail-section, .video-detail-section {
    background: linear-gradient(135deg, var(--off-white) 0%, var(--light-accent) 100%);
    padding: 4rem 0;
}

.calisma-image-container {
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 25px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.calisma-image {
    width: 100%;
    height: auto;
    transition: transform 0.3s ease;
}

.calisma-image:hover {
    transform: scale(1.03);
}

.calisma-info {
    margin-bottom: 30px;
}

.calisma-title {
    color: var(--primary-color);
    font-weight: 700;
    margin-bottom: 15px;
}

.calisma-description {
    color: var(--text-color);
    margin-bottom: 25px;
    line-height: 1.7;
}

.tag-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 20px;
}

.tag-item {
    background-color: var(--light-accent);
    color: var(--primary-color);
    padding: 5px 15px;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.tag-item:hover {
    background-color: var(--accent-color);
    color: var(--white);
}

/* Duyarlı Tasarım */
@media (max-width: 991px) {
    .sidebar-card, .content-card {
        margin-bottom: 20px;
    }
    
    .profile-image-container {
        width: 120px;
        height: 120px;
    }
}

@media (max-width: 767px) {
    .page-section {
        padding: 3rem 0;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .section-subtitle {
        font-size: 1rem;
    }
}
@media (max-width: 575px) {
    .page-section {
        padding: 2rem 0;
    }
    
    .section-title {
        font-size: 1.75rem;
    }
    
    .timeline-item {
        padding-left: 0;
    }
}

/* Ürünler Uygulaması Stilleri */
.product-image-container {
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 25px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.product-image {
    width: 100%;
    height: auto;
    transition: transform 0.3s ease;
}

.product-image:hover {
    transform: scale(1.03);
}

.product-info {
    margin-bottom: 30px;
}

.product-title {
    color: var(--primary-color);
    font-weight: 700;
    margin-bottom: 15px;
}

.product-description {
    color: var(--text-color);
    margin-bottom: 25px;
    line-height: 1.7;
}

.product-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.product-stock {
    font-size: 0.9rem;
    font-weight: 500;
    padding: 5px 10px;
    border-radius: 50px;
    display: inline-block;
    margin-bottom: 20px;
}

.stock-available {
    background-color: rgba(46, 125, 50, 0.1);
    color: #2E7D32;
}

.stock-limited {
    background-color: rgba(255, 152, 0, 0.1);
    color: #F57C00;
}

.stock-out {
    background-color: rgba(198, 40, 40, 0.1);
    color: #C62828;
}

.product-quantity {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.quantity-input {
    width: 60px;
    text-align: center;
    margin: 0 10px;
}

.quantity-btn {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--light-accent);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
}

.quantity-btn:hover {
    background-color: var(--accent-color);
    color: var(--white);
}

.add-to-cart-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 12px 25px;
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: 50px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.add-to-cart-btn:hover {
    background-color: var(--secondary-color);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Nasıl Yapılır Uygulaması Stilleri */
.video-container {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 oranı */
    height: 0;
    overflow: hidden;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 25px;
}

.video-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 0;
}

.video-thumbnail {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 15px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    cursor: pointer;
}

.video-thumbnail img {
    width: 100%;
    height: auto;
    transition: transform 0.3s ease;
}

.video-thumbnail:hover img {
    transform: scale(1.05);
}

.video-play {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 1.5rem;
    transition: all 0.3s ease;
}

.video-thumbnail:hover .video-play {
    background-color: var(--primary-color);
    color: var(--white);
}

.video-info {
    padding: 15px;
}

.video-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.video-meta {
    display: flex;
    align-items: center;
    gap: 15px;
    font-size: 0.85rem;
    color: var(--text-color);
    margin-bottom: 10px;
}

.video-meta i {
    color: var(--accent-color);
}

.video-description {
    font-size: 0.9rem;
    color: var(--text-color);
    line-height: 1.6;
    max-height: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 5;
    line-clamp: 5;
    -webkit-box-orient: vertical;
}

.video-actions {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-top: 5px;
    margin-bottom: 5px;
}

.video-action-btn {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9rem;
    color: var(--text-color);
    cursor: pointer;
    transition: all 0.3s ease;
}

.video-action-btn:hover {
    color: var(--primary-color);
}

.video-action-btn.active {
    color: var(--primary-color);
    font-weight: 500;
}

/* Üyelik Uygulaması Stilleri */
.auth-container {
    max-width: 500px;
    margin: 0 auto;
    padding: 30px;
    background-color: var(--white);
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.auth-header {
    text-align: center;
    margin-bottom: 30px;
}

.auth-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.auth-subtitle {
    font-size: 1rem;
    color: var(--text-color);
}

.auth-form .form-group {
    margin-bottom: 20px;
}

.auth-form .form-label {
    font-weight: 500;
    color: var(--primary-color);
    margin-bottom: 8px;
}

.auth-form .form-control {
    padding: 12px 15px;
    border-radius: 8px;
    border: 1px solid var(--light-gray);
    transition: all 0.3s ease;
}

.auth-form .form-control:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.25rem rgba(214, 179, 145, 0.25);
}

.auth-form .btn-submit {
    width: 100%;
    padding: 12px;
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: 50px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.auth-form .btn-submit:hover {
    background-color: var(--secondary-color);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.auth-links {
    text-align: center;
    margin-top: 20px;
}

.auth-links a {
    color: var(--primary-color);
    text-decoration: none;
    transition: all 0.3s ease;
}

.auth-links a:hover {
    color: var(--secondary-color);
    text-decoration: underline;
}

.profile-container {
    max-width: 1000px;
    margin: -50px auto 50px;
    position: relative;
    z-index: 10;
    background-color: var(--white);
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.cover-photo {
    height: 200px;
    background-color: var(--accent-color);
    background-image: linear-gradient(135deg, var(--accent-color) 0%, var(--primary-color) 100%);
}

.profile-pic {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    border: 5px solid var(--white);
    overflow: hidden;
    position: absolute;
    top: 125px;
    left: 50px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.profile-pic img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-header {
    padding: 30px 50px 20px 220px;
    border-bottom: 1px solid var(--light-gray);
}

.profile-name {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.profile-username {
    font-size: 1rem;
    color: var(--text-color);
    margin-bottom: 15px;
}

.profile-stats {
    display: flex;
    gap: 20px;
}

.stat-item {
    text-align: center;
}

.stat-value {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary-color);
}

.stat-label {
    font-size: 0.85rem;
    color: var(--text-color);
}

.profile-actions {
    position: absolute;
    top: 30px;
    right: 50px;
}

.profile-nav {
    padding: 0 50px;
    border-bottom: 1px solid var(--light-gray);
}

.nav-tabs {
    display: flex;
    gap: 30px;
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-tabs .nav-item {
    padding: 15px 0;
    font-weight: 500;
    color: var(--text-color);
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
}

.nav-tabs .nav-item:hover {
    color: var(--primary-color);
}

.nav-tabs .nav-item.active {
    color: var(--primary-color);
}

.nav-tabs .nav-item.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--primary-color);
    border-radius: 3px 3px 0 0;
}

.profile-content {
    padding: 30px 50px;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.profile-field {
    margin-bottom: 20px;
}

.profile-field label {
    font-weight: 500;
    color: var(--primary-color);
    margin-bottom: 8px;
}

.field-value {
    color: var(--text-color);
}

.edit-btn {
    background-color: transparent;
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    transition: all 0.3s ease;
}

.edit-btn:hover {
    color: var(--secondary-color);
}

.save-btn, .cancel-btn {
    padding: 5px 15px;
    border-radius: 50px;
    border: none;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.save-btn {
    background-color: var(--primary-color);
    color: var(--white);
}

.save-btn:hover {
    background-color: var(--secondary-color);
}

.cancel-btn {
    background-color: var(--light-gray);
    color: var(--text-color);
    margin-left: 10px;
}

.cancel-btn:hover {
    background-color: #d0c9bd;
}

/* Yorumlar Uygulaması Stilleri */
.yorumlar-container, .yorumlar-containeri {
    margin-top: 30px;
    max-width: 900px;
    margin: 0 auto;
}

.yorumlar-card {
    background-color: var(--white);
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.yorumlar-header {
    padding: 15px 20px;
    background-color: var(--light-accent);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    border-radius: 20px 20px 0 0;
}

.yorumlar-body {
    padding: 20px;
    max-height: 600px;
    overflow-y: auto;
}

.yorum-form {
    margin-bottom: 30px;
}

.yorum-form h5, .yorum-baslik {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--light-gray);
}

.yorum-form textarea {
    border: 1px solid var(--light-gray);
    border-radius: 8px;
    padding: 12px 15px;
    resize: vertical;
    min-height: 100px;
    transition: all 0.3s ease;
}

.yorum-form textarea:focus {
    border-color: var(--accent-color);
}

.yorum-sayisi-badge {
    background-color: var(--off-white); 
    color: var(--secondary-color);
}

.emoji-buttons {
    display: flex; 
    flex-wrap: wrap; 
    gap: 5px;
}

.toast-container {
    z-index: 9999;
}

.yorum-kart {
    background-color: var(--off-white); 
    border-radius: 12px; 
    overflow: hidden;
}

.duzenle-kaydet-btn {
    background-color: var(--accent-color); 
    color: var(--white);
}

.duzenle-iptal-btn {
    background-color: var(--primary-color); 
    color: var(--white);
    box-shadow: 0 0 0 0.25rem rgba(214, 179, 145, 0.25);
}

.yorum-form .btn-submit {
    padding: 8px 20px;
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: 50px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.yorum-form .btn-submit:hover {
    background-color: var(--secondary-color);
    transform: translateY(-2px);
}

.yorum-listesi {
    list-style: none;
    padding: 0;
    margin: 0;
}

.yorum-item {
    padding: 15px;
    border-bottom: 1px solid var(--light-gray);
    transition: all 0.3s ease;
}

.yorum-item:last-child {
    border-bottom: none;
}

.yorum-item:hover {
    background-color: rgba(224, 216, 200, 0.1);
}

.yorum-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
}

.yorum-user {
    display: flex;
    align-items: center;
    gap: 10px;
}

.yorum-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
}

.yorum-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.yorum-user-info {
    display: flex;
    flex-direction: column;
}

.yorum-username {
    font-weight: 600;
    color: var(--primary-color);
}

.yorum-date {
    font-size: 0.85rem;
    color: var(--text-color);
}

.yorum-actions {
    display: flex;
    gap: 10px;
}

.yorum-action-btn {
    background-color: transparent;
    border: none;
    color: var(--text-color);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.yorum-action-btn:hover {
    color: var(--primary-color);
}

.yorum-content {
    color: var(--text-color);
    line-height: 1.6;
    margin-bottom: 10px;
}

.yorum-footer {
    display: flex;
    align-items: center;
    gap: 15px;
}

.yorum-reaction {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9rem;
    color: var(--text-color);
    cursor: pointer;
    transition: all 0.3s ease;
}

.yorum-reaction:hover {
    color: var(--primary-color);
}

.yorum-reaction.active {
    color: var(--primary-color);
    font-weight: 500;
}

.yorum-replies {
    margin-top: 15px;
    padding-left: 20px;
    border-left: 2px solid var(--light-gray);
}

.yanit-form {
    margin-top: 15px;
    padding: 15px;
    background-color: rgba(224, 216, 200, 0.1);
    border-radius: 8px;
}

.yanit-bilgisi {
    font-size: 0.9rem;
    color: var(--primary-color);
    margin-bottom: 10px;
    padding: 8px 12px;
    background-color: rgba(214, 179, 145, 0.1);
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.yanit-iptal-btn {
    background-color: transparent;
    border: none;
    color: var(--text-color);
    cursor: pointer;
    transition: all 0.3s ease;
}

.yanit-iptal-btn:hover {
    color: var(--primary-color);
}

.emoji-picker {
    display: flex;
    gap: 5px;
    margin-bottom: 10px;
}

.emoji-btn {
    background-color: transparent;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.emoji-btn:hover {
    transform: scale(1.2);
}

.karakter-sayaci {
    font-size: 0.85rem;
    color: var(--text-color);
    text-align: right;
    margin-top: 5px;
}

.toast-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 9999;
}

.toast {
    padding: 12px 20px;
    background-color: var(--primary-color);
    color: var(--white);
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 10px;
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.3s forwards;
}

@keyframes fadeInUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

