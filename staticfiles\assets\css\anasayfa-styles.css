/**
 * <PERSON><PERSON><PERSON> - <PERSON> Uygulaması Stilleri
 * Son güncelleme: 07.05.2025
 */

/* Gradient Başlık Stili */
.gradient-heading {
    background: linear-gradient(to right, var(--heading-color), var(--accent-color));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent !important;
    display: inline-block;
}

/* Inline Editing Stilleri */
.editable-content {
    position: relative;
    transition: all 0.3s ease;
}

.editable-content:hover {
    background-color: rgba(var(--accent-color-rgb), 0.05);
}

.editable-content .edit-icon {
    position: absolute;
    top: 5px;
    right: 5px;
    opacity: 0;
    transition: all 0.3s ease;
    cursor: pointer;
    color: var(--accent-color);
    background-color: rgba(var(--white-rgb), 0.8);
    border-radius: 50%;
    padding: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.editable-content:hover .edit-icon {
    opacity: 1;
}

/* Düzen<PERSON>e <PERSON> */
.edit-mode {
    border: 2px dashed var(--accent-color);
    padding: 10px;
    border-radius: 5px;
    background-color: rgba(var(--accent-color-rgb), 0.05);
}

/* Düzenleme Kontrolleri */
.edit-controls {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.edit-save-btn, .edit-cancel-btn {
    padding: 5px 15px;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.edit-save-btn {
    background-color: var(--accent-color);
    color: var(--white);
    border: none;
}

.edit-save-btn:hover {
    background-color: var(--heading-color);
}

.edit-cancel-btn {
    background-color: var(--off-white);
    color: var(--text-color);
    border: 1px solid var(--light-gray);
}

.edit-cancel-btn:hover {
    background-color: var(--light-gray);
}
