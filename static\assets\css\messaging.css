/* messaging/static/assets/css/messaging.css */

/* <PERSON><PERSON> k<PERSON>ri */
.messaging-container {
    margin-top: 20px;
}

/* Konuşma listesi */
.conversation-list .list-group-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

.conversation-list .list-group-item:hover {
    background-color: #f8f9fa;
}

.conversation-list .list-group-item a {
    flex-grow: 1;
    text-decoration: none;
    color: #333;
    padding: 10px 15px;
}

.conversation-list .list-group-item a:hover {
    text-decoration: none;
}

/* Mesajlaşma ekranı */
#chat-log {
    border: 1px solid #ccc;
    padding: 15px;
    height: 450px; /* Yüksekliği artırıldı */
    overflow-y: auto; /* Otomatik scroll */
    margin-bottom: 15px;
    background-color: #f9f9f9; /* Hafif arka plan rengi */
    border-radius: 8px;
}

.message {
    margin-bottom: 12px; /* <PERSON><PERSON><PERSON> a<PERSON>ına bo<PERSON> */
    padding: 10px 15px;
    border-radius: 15px; /* <PERSON><PERSON><PERSON> köşeler */
    max-width: 80%; /* Mesaj genişliği */
    word-wrap: break-word; /* Uzun kelimeleri böl */
    position: relative; /* Düzenle/Sil linkleri için */
}

.message strong {
    margin-right: 8px;
    font-weight: 600;
}

.message .text-muted {
    font-size: 0.75em;
    position: absolute;
    bottom: 5px;
    right: 10px;
}

.message .text-muted a {
    color: #777;
    text-decoration: none;
    margin-left: 5px;
}

.message .text-muted a:hover {
    text-decoration: underline;
}

/* Gönderen ve Alıcı mesajları için farklı stiller */
.message:nth-child(odd) { /* Örnek: Tek sıradaki mesajlar (gönderen) */
    background-color: #e9ecef; /* Açık gri */
    margin-left: auto; /* Sağa hizala */
    border-bottom-right-radius: 2px; /* Köşeyi düz yap */
}

.message:nth-child(even) { /* Örnek: Çift sıradaki mesajlar (alıcı) */
    background-color: #007bff; /* Mavi */
    color: white;
    margin-right: auto; /* Sola hizala */
    border-bottom-left-radius: 2px; /* Köşeyi düz yap */
}

.message:nth-child(even) strong {
    color: #cfe2ff; /* Açık mavi */
}

.message:nth-child(even) .text-muted {
    color: #e9ecef; /* Açık gri */
}


/* Duyuru listesi */
.announcement-list .list-group-item {
    margin-bottom: 10px;
    border: 1px solid #ddd;
    padding: 15px;
    border-radius: 5px;
    background-color: #fff;
}

.announcement-list .list-group-item strong {
    margin-right: 5px;
    color: #0056b3; /* Koyu mavi */
}

.announcement-list .list-group-item small {
    display: inline-block; /* Yan yana durması için */
    color: #777;
    font-size: 0.8em;
    margin-left: 10px;
}

.announcement-list .list-group-item form {
    margin-top: 0; /* Formun üst boşluğunu kaldır */
    display: inline-block; /* Yan yana durması için */
}

.announcement-list .list-group-item .text-success {
    color: #28a745 !important; /* Yeşil */
    font-weight: bold;
}


/* Yeni konuşma başlatma formu */
.new-conversation-form .form-group {
    margin-bottom: 20px;
}

.new-conversation-form label {
    font-weight: bold;
    margin-bottom: 8px;
}

.new-conversation-form select.form-control {
    height: auto; /* Çoklu seçim için yükseklik ayarı */
}

.new-conversation-form .form-text {
    font-size: 0.85em;
    color: #6c757d;
}

.new-conversation-form button[type="submit"] {
    margin-top: 10px;
}