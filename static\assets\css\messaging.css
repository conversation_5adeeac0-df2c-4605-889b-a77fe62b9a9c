/* Modern Messaging Styles */

/* <PERSON><PERSON>şma Listesi */
.conversation-item {
    border-bottom: 1px solid rgba(var(--color-brown-rgb), 0.1);
    transition: all 0.3s ease;
}

.conversation-item:last-child {
    border-bottom: none;
}

.conversation-item:hover {
    background-color: rgba(var(--color-sand-rgb), 0.1);
}

.conversation-link {
    display: block;
    padding: 1.25rem;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
}

.conversation-link:hover {
    color: inherit;
    text-decoration: none;
}

.conversation-avatar {
    font-size: 2.5rem;
    color: var(--color-brown);
    opacity: 0.7;
}

.conversation-title {
    color: var(--color-brown);
    font-weight: 600;
    font-size: 1.1rem;
}

.conversation-time {
    font-size: 0.85rem;
    color: var(--color-navy) !important;
}

.conversation-preview {
    font-size: 0.9rem;
    line-height: 1.4;
}

.conversation-arrow {
    color: var(--color-moss);
    font-size: 1.2rem;
    opacity: 0.6;
    transition: all 0.3s ease;
}

.conversation-item:hover .conversation-arrow {
    opacity: 1;
    transform: translateX(5px);
}

/* Empty State */
.empty-state {
    padding: 3rem 2rem;
}

.empty-state i {
    color: var(--color-sand) !important;
}

/* Yeni Konuşma Sayfası */
.user-select-item {
    padding: 1rem;
    border: 1px solid rgba(var(--color-brown-rgb), 0.1);
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: white;
}

.user-select-item:hover {
    border-color: var(--color-brown);
    background-color: rgba(var(--color-sand-rgb), 0.1);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(var(--color-brown-rgb), 0.1);
}

.user-select-item.selected {
    border-color: var(--color-brown);
    background-color: rgba(var(--color-brown-rgb), 0.1);
}

.user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--color-brown);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1.1rem;
}

.user-info h6 {
    color: var(--color-brown);
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.user-info small {
    color: var(--color-navy);
}

.selected-users {
    background-color: rgba(var(--color-moss-rgb), 0.1);
    border-radius: 10px;
    padding: 1rem;
    border: 1px solid rgba(var(--color-moss-rgb), 0.2);
}

.selected-users h6 {
    color: var(--color-moss);
    font-weight: 600;
}

/* Mesaj Detay Sayfası */
.message-container {
    max-height: 500px;
    overflow-y: auto;
    padding: 1rem;
    background-color: rgba(var(--color-sand-rgb), 0.05);
    border-radius: 10px;
    margin-bottom: 1rem;
}

.message-item {
    margin-bottom: 1rem;
    display: flex;
    align-items: flex-start;
}

.message-item.own-message {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--color-brown);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
    margin: 0 0.75rem;
}

.message-content {
    max-width: 70%;
    background-color: white;
    padding: 0.75rem 1rem;
    border-radius: 15px;
    box-shadow: 0 2px 8px rgba(var(--color-brown-rgb), 0.1);
    position: relative;
}

.message-item.own-message .message-content {
    background-color: var(--color-brown);
    color: white;
}

.message-text {
    margin-bottom: 0.25rem;
    line-height: 1.4;
}

.message-time {
    font-size: 0.75rem;
    opacity: 0.7;
}

.message-item.own-message .message-time {
    color: rgba(255, 255, 255, 0.8);
}

.message-actions a {
    text-decoration: none;
    opacity: 0.6;
    transition: opacity 0.3s ease;
}

.message-actions a:hover {
    opacity: 1;
}

/* Mesaj Gönderme Formu */
.message-form {
    background-color: white;
    border-radius: 15px;
    padding: 1rem;
    box-shadow: 0 4px 15px rgba(var(--color-brown-rgb), 0.1);
    border: 1px solid rgba(var(--color-brown-rgb), 0.1);
}

.message-input {
    border: 1px solid rgba(var(--color-brown-rgb), 0.2);
    border-radius: 25px;
    padding: 0.75rem 1.25rem;
    resize: none;
    transition: all 0.3s ease;
}

.message-input:focus {
    border-color: var(--color-brown);
    box-shadow: 0 0 0 0.25rem rgba(var(--color-brown-rgb), 0.25);
    outline: none;
}

.send-button {
    border-radius: 50%;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--color-brown);
    border: none;
    color: white;
    transition: all 0.3s ease;
}

.send-button:hover {
    background-color: var(--color-moss);
    transform: scale(1.05);
}

.send-button:disabled {
    background-color: var(--color-sand);
    cursor: not-allowed;
    transform: none;
}

/* Bildirim Badge */
.notification-badge {
    background-color: #dc3545;
    color: white;
    font-size: 0.7rem;
    font-weight: 600;
    padding: 0.2rem 0.4rem;
    border-radius: 50%;
    position: absolute;
    top: -5px;
    right: -5px;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* Navbar link için relative position */
.navbar-nav li a {
    position: relative;
}

/* Mesajlaşma ekranı */
#chat-log {
    border: 1px solid #ccc;
    padding: 15px;
    height: 450px; /* Yüksekliği artırıldı */
    overflow-y: auto; /* Otomatik scroll */
    margin-bottom: 15px;
    background-color: #f9f9f9; /* Hafif arka plan rengi */
    border-radius: 8px;
}

.message {
    margin-bottom: 12px; /* Mesajlar arasına boşluk */
    padding: 10px 15px;
    border-radius: 15px; /* Yuvarlak köşeler */
    max-width: 80%; /* Mesaj genişliği */
    word-wrap: break-word; /* Uzun kelimeleri böl */
    position: relative; /* Düzenle/Sil linkleri için */
}

.message strong {
    margin-right: 8px;
    font-weight: 600;
}

.message .text-muted {
    font-size: 0.75em;
    position: absolute;
    bottom: 5px;
    right: 10px;
}

.message .text-muted a {
    color: #777;
    text-decoration: none;
    margin-left: 5px;
}

.message .text-muted a:hover {
    text-decoration: underline;
}

/* Gönderen ve Alıcı mesajları için farklı stiller */
.message:nth-child(odd) { /* Örnek: Tek sıradaki mesajlar (gönderen) */
    background-color: #e9ecef; /* Açık gri */
    margin-left: auto; /* Sağa hizala */
    border-bottom-right-radius: 2px; /* Köşeyi düz yap */
}

.message:nth-child(even) { /* Örnek: Çift sıradaki mesajlar (alıcı) */
    background-color: #007bff; /* Mavi */
    color: white;
    margin-right: auto; /* Sola hizala */
    border-bottom-left-radius: 2px; /* Köşeyi düz yap */
}

.message:nth-child(even) strong {
    color: #cfe2ff; /* Açık mavi */
}

.message:nth-child(even) .text-muted {
    color: #e9ecef; /* Açık gri */
}


/* Duyuru listesi */
.announcement-list .list-group-item {
    margin-bottom: 10px;
    border: 1px solid #ddd;
    padding: 15px;
    border-radius: 5px;
    background-color: #fff;
}

.announcement-list .list-group-item strong {
    margin-right: 5px;
    color: #0056b3; /* Koyu mavi */
}

.announcement-list .list-group-item small {
    display: inline-block; /* Yan yana durması için */
    color: #777;
    font-size: 0.8em;
    margin-left: 10px;
}

.announcement-list .list-group-item form {
    margin-top: 0; /* Formun üst boşluğunu kaldır */
    display: inline-block; /* Yan yana durması için */
}

.announcement-list .list-group-item .text-success {
    color: #28a745 !important; /* Yeşil */
    font-weight: bold;
}


/* Yeni konuşma başlatma formu */
.new-conversation-form .form-group {
    margin-bottom: 20px;
}

.new-conversation-form label {
    font-weight: bold;
    margin-bottom: 8px;
}

.new-conversation-form select.form-control {
    height: auto; /* Çoklu seçim için yükseklik ayarı */
}

.new-conversation-form .form-text {
    font-size: 0.85em;
    color: #6c757d;
}

.new-conversation-form button[type="submit"] {
    margin-top: 10px;
}