/*
* FORM STYLES CSS
* Çalışma ve Video form sayfalar<PERSON> için ortak stiller
*/

/* Modern renk paleti */
:root {
    --primary-color: #734429;
    --secondary-color: #402401;
    --accent-color: #D9B391;
    --light-accent: #E0D8C8;
    --dark-bg: #2D1A00;
    --text-color: #402401;
    --light-text: #734429;
    --white: #ffffff;
    --off-white: #F8F5F0;
    --light-gray: #E9E4D9;
    --success-color: #2E7D32;
    --error-color: #C62828;
}

/* Genel stil */
.calisma-form-section {
    background: linear-gradient(135deg, var(--off-white) 0%, var(--light-accent) 100%);
    padding: 4rem 0;
}

/* Başlık bölümü */
.section-header {
    margin-bottom: 3rem;
}

.section-title {
    color: var(--primary-color);
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.section-subtitle {
    color: var(--text-color);
    font-size: 1.25rem;
    max-width: 800px;
    margin: 0 auto;
}

.title-decoration {
    display: flex;
    justify-content: center;
    margin-top: 1.5rem;
    gap: 8px;
}

.title-decoration span {
    height: 4px;
    width: 30px;
    border-radius: 50px;
    background-color: var(--accent-color);
}

.title-decoration span:nth-child(2) {
    width: 60px;
    background-color: var(--primary-color);
}

/* Form Kartı */
.form-card {
    border-radius: 20px;
    overflow: hidden;
    background: var(--white);
    box-shadow: 0 10px 25px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.form-card:hover {
    box-shadow: 0 15px 35px rgba(64, 36, 1, 0.1);
}

.form-card .card-header {
    padding: 1.5rem;
    background: linear-gradient(to right, var(--accent-color), var(--light-accent));
    color: var(--secondary-color);
    border-radius: 20px 20px 0 0;
    font-weight: 600;
    text-shadow: 0 1px 1px rgba(255,255,255,0.5);
    border: none;
}

.form-card .card-body {
    padding: 2rem;
}

/* Form Elemanları */
.calisma-form .form-label {
    color: var(--text-color);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.calisma-form .form-control, 
.calisma-form .form-select {
    padding: 0.8rem 1.2rem;
    border-radius: 10px;
    border: 1px solid var(--light-gray);
    background-color: var(--off-white);
    color: var(--text-color);
    transition: all 0.3s ease;
}

.calisma-form .form-control:focus, 
.calisma-form .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(115, 68, 41, 0.25);
    background-color: var(--white);
}

.calisma-form textarea.form-control {
    min-height: 150px;
}

.calisma-form .form-text {
    color: var(--light-text);
    font-size: 0.85rem;
    margin-top: 0.5rem;
}

.calisma-form .invalid-feedback {
    color: var(--error-color);
    font-size: 0.85rem;
    margin-top: 0.5rem;
}

/* Fotoğraf Formset */
.photo-form-item {
    padding: 1rem;
    border-radius: 10px;
    background-color: var(--off-white);
    transition: all 0.3s ease;
}

.photo-form-item:hover {
    background-color: var(--light-accent);
}

/* Butonlar */
.form-actions {
    display: flex;
    align-items: center;
}

.calisma-form .btn-primary {
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 50px;
    color: var(--white);
    font-weight: 600;
    box-shadow: 0 5px 15px rgba(115, 68, 41, 0.3);
    transition: all 0.3s ease;
}

.calisma-form .btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(115, 68, 41, 0.4);
    color: var(--white);
}

.calisma-form .btn-outline-secondary {
    color: var(--text-color);
    border-color: var(--light-gray);
    border-radius: 50px;
    padding: 0.8rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.calisma-form .btn-outline-secondary:hover {
    background-color: var(--light-gray);
    color: var(--text-color);
}

.calisma-form .btn-danger {
    background: linear-gradient(to right, #C62828, #B71C1C);
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 50px;
    color: var(--white);
    font-weight: 600;
    box-shadow: 0 5px 15px rgba(198, 40, 40, 0.3);
    transition: all 0.3s ease;
}

.calisma-form .btn-danger:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(198, 40, 40, 0.4);
    color: var(--white);
}

/* Animasyonlar */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.wow {
    animation: fadeIn 0.8s ease forwards;
}

.fadeIn { animation-name: fadeIn; }
.fadeInUp { animation-name: fadeInUp; }

@keyframes fadeInUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}
