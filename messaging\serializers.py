from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import Conversation, Message, Announcement, UserAnnouncementReadStatus

User = get_user_model()

class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'first_name', 'last_name'] # İht<PERSON><PERSON> göre di<PERSON>er alanlar eklenebilir

class MessageSerializer(serializers.ModelSerializer):
    sender = UserSerializer(read_only=True)

    class Meta:
        model = Message
        fields = ['id', 'conversation', 'sender', 'content', 'timestamp']
        read_only_fields = ['sender', 'timestamp']

class ConversationSerializer(serializers.ModelSerializer):
    participants = UserSerializer(many=True, read_only=True)
    last_message = serializers.SerializerMethodField()

    class Meta:
        model = Conversation
        fields = ['id', 'participants', 'created_at', 'updated_at', 'last_message']

    def get_last_message(self, obj):
        last_message = obj.messages.order_by('-timestamp').first()
        if last_message:
            return MessageSerializer(last_message).data
        return None

class AnnouncementSerializer(serializers.ModelSerializer):
    sender = UserSerializer(read_only=True)

    class Meta:
        model = Announcement
        fields = ['id', 'sender', 'content', 'timestamp']
        read_only_fields = ['sender', 'timestamp']

class UserAnnouncementReadStatusSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    announcement = AnnouncementSerializer(read_only=True)

    class Meta:
        model = UserAnnouncementReadStatus
        fields = ['id', 'user', 'announcement', 'is_read', 'read_at']
        read_only_fields = ['user', 'announcement']