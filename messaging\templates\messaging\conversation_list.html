{% extends 'base.html' %}
{% load static %}

{% block title %}Mesajlarım - Küp Cadısı{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'assets/css/modern-theme.css' %}">
<link rel="stylesheet" href="{% static 'assets/css/form-styles.css' %}">
<link rel="stylesheet" href="{% static 'assets/css/messaging.css' %}">
{% endblock %}

{% block content %}
<section class="modern-section py-5">
    <div class="container">
        <!-- Ba<PERSON><PERSON><PERSON><PERSON> Bölümü -->
        <div class="modern-section-header text-center mb-5 wow fadeIn" data-wow-delay="0.2s">
            <h1 class="modern-title">Mesajlarım</h1>
            <p class="modern-subtitle">Konuşmalarınızı görüntüleyin ve yeni mesajlar gönderin</p>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-10">
                <!-- <PERSON><PERSON> Butonu -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h3 class="mb-0"><i class="bi bi-chat-dots me-2"></i>Aktif Konuşmalar</h3>
                    <a href="{% url 'messaging:new_conversation' %}" class="modern-btn">
                        <i class="bi bi-plus-circle me-2"></i>Yeni Konuşma
                    </a>
                </div>

                <!-- Konuşma Listesi -->
                <div class="modern-card wow fadeInUp" data-wow-delay="0.3s">
                    <div class="modern-card-body p-0">
                        {% for conversation in conversations %}
                        <div class="conversation-item">
                            <a href="{% url 'messaging:conversation_detail' conversation.id %}" class="conversation-link">
                                <div class="d-flex align-items-center">
                                    <div class="conversation-avatar me-3">
                                        <i class="bi bi-person-circle"></i>
                                    </div>
                                    <div class="conversation-content flex-grow-1">
                                        <div class="conversation-header d-flex justify-content-between align-items-center">
                                            <h6 class="conversation-title mb-1">
                                                {% for participant in conversation.participants.all %}
                                                    {% if participant != request.user %}
                                                        {{ participant.get_full_name|default:participant.username }}{% if not forloop.last %}, {% endif %}
                                                    {% endif %}
                                                {% endfor %}
                                            </h6>
                                            <small class="conversation-time text-muted">
                                                {{ conversation.updated_at|timesince }} önce
                                            </small>
                                        </div>
                                        <p class="conversation-preview mb-0 text-muted">
                                            {% if conversation.messages.last %}
                                                {{ conversation.messages.last.content|truncatechars:50 }}
                                            {% else %}
                                                Henüz mesaj yok
                                            {% endif %}
                                        </p>
                                    </div>
                                    <div class="conversation-arrow">
                                        <i class="bi bi-chevron-right"></i>
                                    </div>
                                </div>
                            </a>
                        </div>
                        {% empty %}
                        <div class="empty-state text-center py-5">
                            <i class="bi bi-chat-square-text display-1 text-muted mb-3"></i>
                            <h4 class="text-muted mb-3">Henüz konuşmanız yok</h4>
                            <p class="text-muted mb-4">Diğer kullanıcılarla mesajlaşmaya başlamak için yeni bir konuşma başlatın.</p>
                            <a href="{% url 'messaging:new_conversation' %}" class="modern-btn">
                                <i class="bi bi-plus-circle me-2"></i>İlk Konuşmanızı Başlatın
                            </a>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/wow/1.1.2/wow.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        new WOW().init();
    });
</script>
{% endblock %}