from django.shortcuts import render, get_object_or_404
from django.http import HttpResponse, JsonResponse
from django.contrib.auth.decorators import login_required
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.db.models import Q

from rest_framework import generics, permissions, status
from rest_framework.response import Response
from rest_framework.views import APIView

from .models import Conversation, Message, Announcement, UserAnnouncementReadStatus
from .serializers import ConversationSerializer, MessageSerializer, AnnouncementSerializer, UserAnnouncementReadStatusSerializer
from django.shortcuts import render, get_object_or_404
from django.http import HttpResponse, JsonResponse
from django.contrib.auth.decorators import login_required, user_passes_test
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.db.models import Q

from rest_framework import generics, permissions, status
from rest_framework.response import Response
from rest_framework.views import APIView

from .models import Conversation, Message, Announcement, UserAnnouncementReadStatus
from .serializers import ConversationSerializer, MessageSerializer, AnnouncementSerializer, UserAnnouncementReadStatusSerializer
from .permissions import IsApprovedUserOrAdmin, IsParticipantOfConversation # Yeni izin sınıfını import ediyoruz

User = get_user_model()

# Kullanıcının onaylanmış veya yönetici olup olmadığını kontrol eden yardımcı fonksiyon
def is_approved_user_or_admin(user):
    if user.is_staff or user.is_superuser:
        return True
    if user.is_authenticated:
        try:
            return hasattr(user, 'profil') and user.profil.is_approved
        except:
            return False
    return False

from django.shortcuts import render, get_object_or_404, redirect
from django.http import HttpResponse, JsonResponse
from django.contrib.auth.decorators import login_required, user_passes_test
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.db.models import Q

from rest_framework import generics, permissions, status
from rest_framework.response import Response
from rest_framework.views import APIView

from .models import Conversation, Message, Announcement, UserAnnouncementReadStatus
from .serializers import ConversationSerializer, MessageSerializer, AnnouncementSerializer, UserAnnouncementReadStatusSerializer, UserSerializer
from .permissions import IsApprovedUserOrAdmin, IsParticipantOfConversation # Yeni izin sınıfını import ediyoruz

User = get_user_model()

# Kullanıcının onaylanmış veya yönetici olup olmadığını kontrol eden yardımcı fonksiyon
def is_approved_user_or_admin(user):
    if user.is_staff or user.is_superuser:
        return True
    if user.is_authenticated:
        try:
            return hasattr(user, 'profil') and user.profil.is_approved
        except:
            return False
    return False

# HTML Sayfalarını Render Eden Fonksiyon Tabanlı Görünümler
@login_required
@user_passes_test(is_approved_user_or_admin) # İzin kontrolü
def conversation_list_view(request):
    """
    Kimliği doğrulanmış kullanıcının dahil olduğu konuşmaları listeler ve HTML render eder.
    """
    conversations = Conversation.objects.filter(participants=request.user).order_by('-updated_at')
    return render(request, 'messaging/conversation_list.html', {'conversations': conversations})

@login_required
def conversation_detail_view(request, pk):
    """
    Belirli bir konuşmanın detaylarını ve mesajlarını görüntüler ve HTML render eder.
    """
    conversation = get_object_or_404(Conversation, pk=pk)

    # Kullanıcının konuşmanın bir parçası olup olmadığını ve onaylanmış/yönetici olup olmadığını kontrol et
    if request.user not in conversation.participants.all() or not is_approved_user_or_admin(request.user):
         return HttpResponse("Bu konuşmayı görüntüleme izniniz yok.", status=403)

    messages = conversation.messages.all()
    return render(request, 'messaging/conversation_detail.html', {'conversation': conversation, 'messages': messages})

@login_required
@user_passes_test(is_approved_user_or_admin) # İzin kontrolü
def announcement_list_view(request):
    """
    Tüm duyuruları listeler ve HTML render eder.
    """
    announcements = Announcement.objects.all().order_by('-timestamp')

    # Kullanıcının duyuruları okuma durumunu al
    for announcement in announcements:
        announcement.is_read = UserAnnouncementReadStatus.objects.filter(
            user=request.user,
            announcement=announcement,
            is_read=True
        ).exists()

    return render(request, 'messaging/announcement_list.html', {'announcements': announcements})

@login_required
@user_passes_test(is_approved_user_or_admin) # İzin kontrolü
def new_conversation_view(request):
    """
    Yeni konuşma başlatmak için kullanıcıları listeler ve HTML render eder.
    """
    # Onaylanmış kullanıcıları ve yöneticileri listele (kendisi hariç)
    approved_users_and_admins = User.objects.filter(
        Q(profil__is_approved=True) | Q(is_staff=True) | Q(is_superuser=True)
    ).exclude(id=request.user.id)

from django.shortcuts import render, get_object_or_404, redirect
from django.http import HttpResponse, JsonResponse
from django.contrib.auth.decorators import login_required, user_passes_test
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.db.models import Q

from rest_framework import generics, permissions, status
from rest_framework.response import Response
from rest_framework.views import APIView

from .models import Conversation, Message, Announcement, UserAnnouncementReadStatus
from .serializers import ConversationSerializer, MessageSerializer, AnnouncementSerializer, UserAnnouncementReadStatusSerializer, UserSerializer
from .permissions import IsApprovedUserOrAdmin, IsParticipantOfConversation # Yeni izin sınıfını import ediyoruz

User = get_user_model()

# Kullanıcının onaylanmış veya yönetici olup olmadığını kontrol eden yardımcı fonksiyon
def is_approved_user_or_admin(user):
    if user.is_staff or user.is_superuser:
        return True
    if user.is_authenticated:
        try:
            return hasattr(user, 'profil') and user.profil.is_approved
        except:
            return False
    return False

# HTML Sayfalarını Render Eden Fonksiyon Tabanlı Görünümler
@login_required
@user_passes_test(is_approved_user_or_admin) # İzin kontrolü
def conversation_list_view(request):
    """
    Kimliği doğrulanmış kullanıcının dahil olduğu konuşmaları listeler ve HTML render eder.
    """
    conversations = Conversation.objects.filter(participants=request.user).order_by('-updated_at')
    return render(request, 'messaging/conversation_list.html', {'conversations': conversations})

@login_required
def conversation_detail_view(request, pk):
    """
    Belirli bir konuşmanın detaylarını ve mesajlarını görüntüler ve HTML render eder.
    """
    conversation = get_object_or_404(Conversation, pk=pk)

    # Kullanıcının konuşmanın bir parçası olup olmadığını ve onaylanmış/yönetici olup olmadığını kontrol et
    if request.user not in conversation.participants.all() or not is_approved_user_or_admin(request.user):
         return HttpResponse("Bu konuşmayı görüntüleme izniniz yok.", status=403)

    messages = conversation.messages.all()
    return render(request, 'messaging/conversation_detail.html', {'conversation': conversation, 'messages': messages})

@login_required
@user_passes_test(is_approved_user_or_admin) # İzin kontrolü
def announcement_list_view(request):
    """
    Tüm duyuruları listeler ve HTML render eder.
    """
    announcements = Announcement.objects.all().order_by('-timestamp')

    # Kullanıcının duyuruları okuma durumunu al
    for announcement in announcements:
        announcement.is_read = UserAnnouncementReadStatus.objects.filter(
            user=request.user,
            announcement=announcement,
            is_read=True
        ).exists()

    return render(request, 'messaging/announcement_list.html', {'announcements': announcements})

@login_required
@user_passes_test(is_approved_user_or_admin) # İzin kontrolü
def new_conversation_view(request):
    """
    Yeni konuşma başlatmak için kullanıcıları listeler ve HTML render eder.
    """
    # Onaylanmış kullanıcıları ve yöneticileri listele (kendisi hariç)
    approved_users_and_admins = User.objects.filter(
        Q(profil__is_approved=True) | Q(is_staff=True) | Q(is_superuser=True)
    ).exclude(id=request.user.id)

    return render(request, 'messaging/new_conversation.html', {'users': approved_users_and_admins})

@login_required
@user_passes_test(lambda u: u.is_staff or u.is_superuser) # Sadece yöneticilere izin ver
def create_announcement_view(request):
    """
    Yöneticilerin duyuru oluşturması için arayüzü render eder.
    """
    if request.method == 'POST':
        content = request.POST.get('content')
        if content:
            Announcement.objects.create(sender=request.user, content=content)
            return redirect('messaging:announcement_list') # Duyuru listesine yönlendir
        else:
            # Hata mesajı gösterilebilir
            pass
    return render(request, 'messaging/create_announcement.html')


# Django REST Framework API Görünümleri
class ConversationListView(generics.ListAPIView):
    """
    Kimliği doğrulanmış kullanıcının dahil olduğu konuşmaları listeler.
    """
    serializer_class = ConversationSerializer
    permission_classes = [permissions.IsAuthenticated, IsApprovedUserOrAdmin] # İzin kontrolü

    def get_queryset(self):
        user = self.request.user
        # Kullanıcının dahil olduğu konuşmaları döndür
        return Conversation.objects.filter(participants=user).order_by('-updated_at')

class ConversationDetailView(generics.RetrieveAPIView):
    """
    Belirli bir konuşmanın detaylarını ve mesajlarını görüntüler.
    """
    serializer_class = ConversationSerializer
    permission_classes = [permissions.IsAuthenticated, IsApprovedUserOrAdmin, IsParticipantOfConversation] # İzin kontrolü
    queryset = Conversation.objects.all()

class ConversationCreateView(generics.CreateAPIView):
    """
    Yeni konuşma oluşturur.
    """
    serializer_class = ConversationSerializer
    permission_classes = [permissions.IsAuthenticated, IsApprovedUserOrAdmin] # İzin kontrolü

    def create(self, request, *args, **kwargs):
        participant_ids = request.data.get('participants', [])
        if not participant_ids:
            return Response({"detail": "Katılımcı seçmelisiniz."}, status=status.HTTP_400_BAD_REQUEST)

        # Seçilen kullanıcıların varlığını ve onaylı/yönetici olup olmadığını kontrol et
        participants = list(User.objects.filter(id__in=participant_ids))
        if len(participants) != len(participant_ids):
             return Response({"detail": "Geçersiz katılımcı(lar) seçildi."}, status=status.HTTP_400_BAD_REQUEST)

        for participant in participants:
            if not is_approved_user_or_admin(participant):
                 return Response({"detail": "Seçilen katılımcılardan biri onaylanmamış veya yönetici değil."}, status=status.HTTP_403_FORBIDDEN)

        # Mevcut konuşmayı kontrol et (aynı katılımcılarla)
        # Basit bir kontrol: Sadece 2 katılımcılı konuşmalar için
        if len(participants) == 1: # Kendisiyle konuşma başlatamaz
             return Response({"detail": "Kendinizle konuşma başlatamazsınız."}, status=status.HTTP_400_BAD_REQUEST)
        if len(participants) == 2:
            user1, user2 = sorted(participants, key=lambda u: u.id)
            existing_conversation = Conversation.objects.filter(participants=user1).filter(participants=user2).first()
            if existing_conversation:
                # Mevcut konuşmaya yönlendir
                return Response({"detail": "Mevcut konuşmaya yönlendiriliyor.", "conversation_id": existing_conversation.id}, status=status.HTTP_200_OK)


        # Yeni konuşma oluştur
        conversation = Conversation.objects.create()
        conversation.participants.set(participants + [request.user]) # Mevcut kullanıcıyı da ekle

        serializer = self.get_serializer(conversation)
        return Response(serializer.data, status=status.HTTP_201_CREATED)


class MessageCreateView(generics.CreateAPIView):
    """
    Yeni mesaj oluşturur.
    """
    serializer_class = MessageSerializer
    permission_classes = [permissions.IsAuthenticated, IsApprovedUserOrAdmin] # İzin kontrolü

    def perform_create(self, serializer):
        # Mesajı gönderen kullanıcıyı ve konuşmayı belirle
        conversation_id = self.request.data.get('conversation')
        try:
            conversation = Conversation.objects.get(id=conversation_id)
            # Kullanıcının konuşmanın bir parçası olup olmadığını kontrol et
            if self.request.user in conversation.participants.all():
                serializer.save(sender=self.request.user, conversation=conversation)
            else:
                return Response({"detail": "Bu konuşmaya mesaj gönderme izniniz yok."}, status=status.HTTP_403_FORBIDDEN)
        except Conversation.DoesNotExist:
            return Response({"detail": "Konuşma bulunamadı."}, status=status.HTTP_404_NOT_FOUND)

class AnnouncementCreateView(generics.CreateAPIView):
    """
    Yeni duyuru oluşturur (sadece yöneticiler için).
    """
    serializer_class = AnnouncementSerializer
    permission_classes = [permissions.IsAdminUser] # Sadece admin kullanıcılar duyuru oluşturabilir

    def perform_create(self, serializer):
        # Duyuruyu gönderen kullanıcıyı (yönetici) belirle
        serializer.save(sender=self.request.user)

class AnnouncementListView(generics.ListAPIView):
    """
    Tüm duyuruları listeler.
    """
    serializer_class = AnnouncementSerializer
    permission_classes = [permissions.IsAuthenticated, IsApprovedUserOrAdmin] # İzin kontrolü
    queryset = Announcement.objects.all().order_by('-timestamp')

class MarkAnnouncementAsReadView(APIView):
    """
    Belirli bir duyuruyu okundu olarak işaretler.
    """
    permission_classes = [permissions.IsAuthenticated, IsApprovedUserOrAdmin] # İzin kontrolü

    def post(self, request, pk):
        try:
            announcement = Announcement.objects.get(pk=pk)
            user = request.user
            # Duyuru okuma durumunu oluştur veya güncelle
            read_status, created = UserAnnouncementReadStatus.objects.get_or_create(
                user=user,
                announcement=announcement,
                defaults={'is_read': True, 'read_at': timezone.now()}
            )
            if not created:
                read_status.is_read = True
                read_status.read_at = timezone.now()
                read_status.save()

            serializer = UserAnnouncementReadStatusSerializer(read_status)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except Announcement.DoesNotExist:
            return Response({"detail": "Duyuru bulunamadı."}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
