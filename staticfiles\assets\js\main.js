/**
* Template Name: Kelly
* Template URL: https://bootstrapmade.com/kelly-free-bootstrap-cv-resume-html-template/
* Updated: Aug 07 2024 with Bootstrap v5.3.3
* Author: BootstrapMade.com
* License: https://bootstrapmade.com/license/
*/

(function() {
  "use strict";

  /**
   * Apply .scrolled class to the body as the page is scrolled down
   */
  function toggleScrolled() {
    const selectBody = document.querySelector('body');
    const selectHeader = document.querySelector('#header');
    if (!selectHeader.classList.contains('scroll-up-sticky') && !selectHeader.classList.contains('sticky-top') && !selectHeader.classList.contains('fixed-top')) return;
    window.scrollY > 100 ? selectBody.classList.add('scrolled') : selectBody.classList.remove('scrolled');
  }

  document.addEventListener('scroll', toggleScrolled);
  window.addEventListener('load', toggleScrolled);

  /**
   * Mobile nav toggle
   */
  const mobileNavToggleBtn = document.querySelector('.mobile-nav-toggle');
  const body = document.querySelector('body');
  const navMenu = document.querySelector('#navmenu');

  function mobileNavToogle() {
    // Toggle body class
    body.classList.toggle('mobile-nav-active');

    // Toggle icon
    mobileNavToggleBtn.classList.toggle('bi-list');
    mobileNavToggleBtn.classList.toggle('bi-x');

    // Log for debugging
    console.log('Mobile nav toggled. Active:', body.classList.contains('mobile-nav-active'));
  }

  if (mobileNavToggleBtn) {
    mobileNavToggleBtn.addEventListener('click', function(e) {
      e.preventDefault();
      mobileNavToogle();
    });
  }

  /**
   * Hide mobile nav on same-page/hash links
   */
  document.querySelectorAll('#navmenu a').forEach(navmenuLink => {
    navmenuLink.addEventListener('click', (e) => {
      // Dropdown toggle'ları hariç tüm linkler için menüyü kapat
      if (!navmenuLink.classList.contains('toggle-dropdown') &&
          !navmenuLink.parentElement.classList.contains('dropdown') &&
          body.classList.contains('mobile-nav-active')) {

        // Menüyü kapat
        mobileNavToogle();

        // Log for debugging
        console.log('Menu closed by link click');
      }
    });
  });

  /**
   * Toggle mobile nav dropdowns
   */
  document.querySelectorAll('.navmenu .toggle-dropdown, #navmenu .dropdown i').forEach(navmenu => {
    navmenu.addEventListener('click', function(e) {
      if (document.querySelector('.mobile-nav-active')) {
        e.preventDefault();
        this.parentNode.classList.toggle('active');
        this.parentNode.nextElementSibling.classList.toggle('dropdown-active');
        e.stopImmediatePropagation();
      }
    });
  });

  /**
   * Close mobile menu when clicking outside
   */
  document.addEventListener('click', function(e) {
    // Eğer mobil menü açıksa ve tıklanan öğe menü veya toggle butonu değilse menüyü kapat
    if (body.classList.contains('mobile-nav-active')) {
      // Tıklanan öğe veya üst öğelerinden biri navmenu veya toggle butonu mu kontrol et
      let targetElement = e.target;
      let isMenuClick = false;

      while (targetElement != null) {
        if (targetElement.id === 'navmenu' || targetElement.classList.contains('mobile-nav-toggle')) {
          isMenuClick = true;
          break;
        }
        targetElement = targetElement.parentElement;
      }

      // Menü dışına tıklandıysa kapat
      if (!isMenuClick) {
        mobileNavToogle();
        console.log('Menu closed by outside click');
      }
    }
  });

  /**
   * Responsive behavior for auth buttons
   */
  function adjustAuthButtons() {
    const authButtons = document.querySelectorAll('.modern-btn-sm');
    if (window.innerWidth < 992) {
      authButtons.forEach(button => {
        button.classList.add('icon-only');
      });
    } else {
      authButtons.forEach(button => {
        button.classList.remove('icon-only');
      });
    }
  }

  // Run on page load
  window.addEventListener('load', adjustAuthButtons);

  // Run on window resize
  window.addEventListener('resize', adjustAuthButtons);

  /**
   * Preloader
   */
  const preloader = document.querySelector('#preloader');
  if (preloader) {
    window.addEventListener('load', () => {
      preloader.remove();
    });
  }

  /**
   * Scroll top button
   */
  let scrollTop = document.querySelector('.scroll-top');

  function toggleScrollTop() {
    if (scrollTop) {
      window.scrollY > 100 ? scrollTop.classList.add('active') : scrollTop.classList.remove('active');
    }
  }
  scrollTop.addEventListener('click', (e) => {
    e.preventDefault();
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  });

  window.addEventListener('load', toggleScrollTop);
  document.addEventListener('scroll', toggleScrollTop);

  /**
   * Animation on scroll function and init
   */
  function aosInit() {
    AOS.init({
      duration: 600,
      easing: 'ease-in-out',
      once: true,
      mirror: false
    });
  }
  window.addEventListener('load', aosInit);

  /**
   * Animate the skills items on reveal
   */
  let skillsAnimation = document.querySelectorAll('.skills-animation');
  skillsAnimation.forEach((item) => {
    new Waypoint({
      element: item,
      offset: '80%',
      handler: function(direction) {
        let progress = item.querySelectorAll('.progress .progress-bar');
        progress.forEach(el => {
          el.style.width = el.getAttribute('aria-valuenow') + '%';
        });
      }
    });
  });

  /**
   * Initiate Pure Counter
   */
  new PureCounter();

  /**
   * Init swiper sliders
   */
  function initSwiper() {
    document.querySelectorAll(".init-swiper").forEach(function(swiperElement) {
      let config = JSON.parse(
        swiperElement.querySelector(".swiper-config").innerHTML.trim()
      );

      if (swiperElement.classList.contains("swiper-tab")) {
        initSwiperWithCustomPagination(swiperElement, config);
      } else {
        new Swiper(swiperElement, config);
      }
    });
  }

  window.addEventListener("load", initSwiper);

  /**
   * Initiate glightbox
   */
  const glightbox = GLightbox({
    selector: '.glightbox'
  });

  /**
   * Init isotope layout and filters
   */
  document.querySelectorAll('.isotope-layout').forEach(function(isotopeItem) {
    let layout = isotopeItem.getAttribute('data-layout') ?? 'masonry';
    let filter = isotopeItem.getAttribute('data-default-filter') ?? '*';
    let sort = isotopeItem.getAttribute('data-sort') ?? 'original-order';

    let initIsotope;
    imagesLoaded(isotopeItem.querySelector('.isotope-container'), function() {
      initIsotope = new Isotope(isotopeItem.querySelector('.isotope-container'), {
        itemSelector: '.isotope-item',
        layoutMode: layout,
        filter: filter,
        sortBy: sort
      });
    });

    isotopeItem.querySelectorAll('.isotope-filters li').forEach(function(filters) {
      filters.addEventListener('click', function() {
        isotopeItem.querySelector('.isotope-filters .filter-active').classList.remove('filter-active');
        this.classList.add('filter-active');
        initIsotope.arrange({
          filter: this.getAttribute('data-filter')
        });
        if (typeof aosInit === 'function') {
          aosInit();
        }
      }, false);
    });
  });

  /**
   * Animasyonlu Bileşenler
   */
  function initAnimatedComponents() {
    // Sayaç animasyonu
    const counterElements = document.querySelectorAll('.counter-number');

    function animateCounter(counter) {
      const target = parseInt(counter.getAttribute('data-target'));
      const duration = 2000; // ms cinsinden süre
      const stepTime = 20; // her adım arasındaki ms cinsinden süre
      const totalSteps = duration / stepTime;
      const stepValue = target / totalSteps;
      let current = 0;

      const timer = setInterval(() => {
        current += stepValue;
        counter.textContent = Math.round(current);

        if (current >= target) {
          counter.textContent = target;
          clearInterval(timer);
        }
      }, stepTime);
    }

    // Yetenek çubukları animasyonu
    const skillBars = document.querySelectorAll('.skill-progress');

    function animateSkillBars() {
      skillBars.forEach(bar => {
        const width = bar.getAttribute('data-width');
        bar.style.width = width + '%';
      });
    }

    // Görünürlük kontrolü için Intersection Observer
    const animationObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          // Sayaç animasyonu
          if (entry.target.classList.contains('counter-number')) {
            animateCounter(entry.target);
          }

          // Yetenek çubukları animasyonu
          if (entry.target.classList.contains('skill-bar')) {
            setTimeout(() => {
              entry.target.querySelector('.skill-progress').style.width =
                entry.target.querySelector('.skill-progress').getAttribute('data-width') + '%';
            }, 300);
          }

          // Gözlemlemeyi durdur
          animationObserver.unobserve(entry.target);
        }
      });
    }, {
      threshold: 0.2
    });

    // Sayaçları gözlemle
    counterElements.forEach(counter => {
      animationObserver.observe(counter);
    });

    // Yetenek çubuklarını gözlemle
    document.querySelectorAll('.skill-bar').forEach(bar => {
      animationObserver.observe(bar);
    });
  }

  window.addEventListener('load', initAnimatedComponents);

})();