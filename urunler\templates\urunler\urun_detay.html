{% extends 'base.html' %}
{% load static %}

{% block title %}{{ product.name }} - <PERSON><PERSON><PERSON>{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@9/swiper-bundle.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/glightbox/dist/css/glightbox.min.css">

<style>
    /* Yönetim Paneli Stilleri */
    .admin-panel {
        background-color: rgba(var(--color-sand-rgb), 0.1);
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(var(--color-brown-rgb), 0.15);
        border: 1px solid rgba(var(--color-brown-rgb), 0.1);
    }

    .admin-panel-title {
        background: linear-gradient(135deg, var(--color-brown) 0%, var(--color-sand) 100%);
        color: white;
        padding: 12px 15px;
        margin: 0;
        font-size: 1rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }

    .admin-panel-title i {
        margin-right: 8px;
    }

    .admin-menu {
        display: flex;
        flex-direction: column;
    }

    .admin-menu-item {
        display: flex;
        align-items: center;
        padding: 12px 15px;
        text-decoration: none;
        color: var(--text-color);
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .admin-menu-item:last-child {
        border-bottom: none;
    }

    .admin-menu-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background-color: var(--color-brown);
        transform: translateX(-4px);
        transition: transform 0.3s ease;
    }

    .admin-menu-item:hover {
        background-color: rgba(var(--color-sand-rgb), 0.15);
        color: var(--color-brown);
    }

    .admin-menu-item:hover::before {
        transform: translateX(0);
    }

    .admin-menu-icon {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background-color: var(--color-brown);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        color: white;
        font-size: 1rem;
        flex-shrink: 0;
        transition: all 0.3s ease;
        box-shadow: 0 3px 6px rgba(var(--color-brown-rgb), 0.3);
    }

    .admin-menu-item:hover .admin-menu-icon {
        transform: scale(1.1) rotate(5deg);
        box-shadow: 0 5px 10px rgba(var(--color-brown-rgb), 0.4);
    }

    .admin-menu-content {
        flex-grow: 1;
    }

    .admin-menu-content h5 {
        margin: 0 0 3px;
        font-size: 0.95rem;
        font-weight: 600;
        color: var(--color-brown);
        transition: all 0.3s ease;
    }

    .admin-menu-item:hover .admin-menu-content h5 {
        color: var(--color-brown);
        transform: translateX(5px);
    }

    .admin-menu-content p {
        margin: 0;
        font-size: 0.75rem;
        color: var(--text-muted);
        transition: all 0.3s ease;
    }

    .admin-menu-item:hover .admin-menu-content p {
        transform: translateX(5px);
    }

    .admin-menu-arrow {
        color: var(--color-brown);
        font-size: 1rem;
        opacity: 0.5;
        transition: all 0.3s ease;
    }

    .admin-menu-item:hover .admin-menu-arrow {
        opacity: 1;
        transform: translateX(5px);
        color: var(--color-brown);
    }

    /* Özel ikon renkleri */
    .edit-icon {
        background-color: var(--color-navy);
        box-shadow: 0 3px 6px rgba(var(--color-navy-rgb), 0.3);
    }

    .admin-menu-item:hover .edit-icon {
        box-shadow: 0 5px 10px rgba(var(--color-navy-rgb), 0.4);
    }

    .photo-icon {
        background-color: var(--color-moss);
        box-shadow: 0 3px 6px rgba(var(--color-moss-rgb), 0.3);
    }

    .admin-menu-item:hover .photo-icon {
        box-shadow: 0 5px 10px rgba(var(--color-moss-rgb), 0.4);
    }

    .delete-icon {
        background-color: var(--color-brown);
        box-shadow: 0 3px 6px rgba(var(--color-brown-rgb), 0.3);
    }

    .admin-menu-item:hover .delete-icon {
        box-shadow: 0 5px 10px rgba(var(--color-brown-rgb), 0.4);
    }

    .delete-item {
        color: var(--color-brown);
    }

    .delete-item:hover {
        background-color: rgba(var(--color-brown-rgb), 0.05);
    }

    /* Etiket Stilleri */
    .modern-tag-cloud {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
    }

    .modern-tag {
        display: inline-block;
        padding: 6px 12px;
        background-color: rgba(var(--color-moss-rgb), 0.1);
        color: var(--color-moss);
        border-radius: 20px;
        font-size: 0.85rem;
        text-decoration: none;
        transition: all 0.3s ease;
        border: 1px solid rgba(var(--color-moss-rgb), 0.2);
    }

    .modern-tag:hover {
        background-color: var(--color-moss);
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 4px 8px rgba(var(--color-moss-rgb), 0.3);
    }

    /* Etiket Rozet Stilleri */
    .badge.bg-moss {
        background-color: var(--color-moss) !important;
        font-size: 0.7rem;
        padding: 0.25em 0.6em;
        margin-right: 0.3rem;
        border-radius: 20px;
        box-shadow: 0 2px 4px rgba(var(--color-moss-rgb), 0.3);
        transition: all 0.3s ease;
    }

    .badge.bg-moss:hover {
        transform: translateY(-2px);
        box-shadow: 0 3px 6px rgba(var(--color-moss-rgb), 0.4);
    }

    /* Benzerlik Puanı Rozetleri */
    .similarity-score .badge {
        font-size: var(--font-size-xs);
        padding: var(--space-xxs) var(--space-xs);
        border-radius: 20px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(var(--color-brown-rgb), 0.2);
    }

    .similarity-score .badge.bg-success {
        background-color: var(--color-moss) !important;
    }

    .similarity-score .badge.bg-moss {
        background-color: var(--color-moss) !important;
    }

    .similarity-score .badge.bg-secondary {
        background-color: var(--text-muted) !important;
    }

    .similarity-score .badge:hover {
        transform: translateY(calc(var(--space-xxs) * -1));
        box-shadow: 0 3px 6px rgba(var(--color-brown-rgb), 0.3);
    }
</style>
{% endblock %}

{% block content %}
<section class="modern-section py-5">
    <div class="container modern-container">
        <!-- Başlık Bölümü -->
        <div class="modern-header text-center mb-5 wow fadeIn" data-wow-delay="0.2s">
            <h1 class="autumn-heading">Ürün Detayları</h1>
            <p class="lead text-muted">{{ product.name }} hakkında daha fazla bilgi edinin</p>
        </div>

        <div class="row g-4">
            <!-- Ürün Görselleri -->
            <div class="col-lg-8 wow fadeInLeft" data-wow-delay="0.3s">
                <div class="modern-card mb-4">
                    <div class="swiper product-slider">
                        <div class="swiper-wrapper">
                            {% for image in product.images.all %}
                            <div class="swiper-slide">
                                <div class="image-container">
                                    <img src="{{ image.image.url }}" alt="{{ product.name }}" class="modern-image">
                                    <a href="{{ image.image.url }}" class="modern-btn-circle position-absolute top-50 start-50 translate-middle" title="{{ product.name }}" data-gallery="product-gallery">
                                        <i class="bi bi-zoom-in"></i>
                                    </a>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        <div class="swiper-pagination"></div>
                        <div class="swiper-button-next"></div>
                        <div class="swiper-button-prev"></div>
                    </div>
                </div>

                <!-- Ürün Açıklaması -->
                <div class="modern-card wow fadeInUp" data-wow-delay="0.4s">
                    <div class="modern-card-header">
                        <h3 class="modern-title"><i class="bi bi-info-circle me-2"></i>Ürün Açıklaması</h3>
                    </div>
                    <div class="modern-card-body">
                        <div class="product-description">
                            {{ product.description|linebreaks }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Ürün Bilgileri -->
            <div class="col-lg-4">
                <div class="modern-card mb-4 wow fadeInRight urun-bilgi-karti" data-wow-delay="0.3s">
                    <div class="modern-card-header autumn-card-header">
                        <h3><i class="bi bi-info-square me-2 text-white"></i>Ürün Bilgileri</h3>
                    </div>
                    <div class="modern-card-body p-0">
                        <div class="modern-info-list">
                            <div class="modern-info-item modern-list-item">
                                <strong>Kategori</strong>
                                <span>{{ product.category.name }}</span>
                            </div>
                            <div class="modern-info-item modern-list-item">
                                <strong>Fiyat</strong>
                                <span class="text-brown fw-bold">₺{{ product.price }}</span>
                            </div>
                            <div class="modern-info-item modern-list-item">
                                <strong>Stok Durumu</strong>
                                <span class="{% if product.is_available %}text-moss{% else %}text-brown{% endif %}">
                                    {% if product.is_available %}Stokta Var{% else %}Stokta Yok{% endif %}
                                </span>
                            </div>
                            <div class="modern-info-item modern-list-item">
                                <strong>Stok Miktarı</strong>
                                <span>{{ product.stock_quantity }}</span>
                            </div>
                            <div class="modern-info-item modern-list-item">
                                <strong>Eklenme Tarihi</strong>
                                <span>{{ product.created_at|date:"d.m.Y" }}</span>
                            </div>
                            {% if product.ekleyen %}
                            <div class="modern-info-item modern-list-item">
                                <strong>Ekleyen</strong>
                                <span>
                                    <a href="{% url 'uyelik:kullanici_profil' username=product.ekleyen.username %}" class="modern-link">
                                        <i class="bi bi-person-fill text-navy"></i> {{ product.ekleyen.username }}
                                    </a>
                                </span>
                            </div>
                            {% endif %}
                        </div>

                        <!-- Yönetim Paneli (Sadece Yöneticiler) -->
                        {% if request.user.is_authenticated and request.user.is_superuser %}
                        <div class="admin-panel mt-3 mb-3">
                            <h5 class="admin-panel-title"><i class="bi bi-gear-fill me-2"></i>Yönetim Paneli</h5>
                            <div class="admin-menu">
                                <!-- Ürün Düzenle -->
                                <a href="{% url 'urunler:urun_duzenle' product.id product.slug %}" class="admin-menu-item">
                                    <div class="admin-menu-icon edit-icon">
                                        <i class="bi bi-pencil-square"></i>
                                    </div>
                                    <div class="admin-menu-content">
                                        <h5>Ürünü Düzenle</h5>
                                        <p class="text-muted" style="font-size: var(--font-size-sm);">Ürün bilgilerini güncelle</p>
                                    </div>
                                    <div class="admin-menu-arrow">
                                        <i class="bi bi-chevron-right"></i>
                                    </div>
                                </a>

                                <!-- Fotoğraf Ekle (Düzenleme sayfasına yönlendir) -->
                                <a href="{% url 'urunler:urun_duzenle' product.id product.slug %}" class="admin-menu-item">
                                    <div class="admin-menu-icon photo-icon">
                                        <i class="bi bi-image"></i>
                                    </div>
                                    <div class="admin-menu-content">
                                        <h5>Fotoğraf Ekle</h5>
                                        <p class="text-muted" style="font-size: var(--font-size-sm);">Düzenleme sayfasında fotoğraf ekle</p>
                                    </div>
                                    <div class="admin-menu-arrow">
                                        <i class="bi bi-chevron-right"></i>
                                    </div>
                                </a>

                                <!-- Ürünü Sil -->
                                <a href="#" class="admin-menu-item delete-item" data-bs-toggle="modal" data-bs-target="#deleteProductModal">
                                    <div class="admin-menu-icon delete-icon">
                                        <i class="bi bi-trash"></i>
                                    </div>
                                    <div class="admin-menu-content">
                                        <h5>Ürünü Sil</h5>
                                        <p class="text-muted" style="font-size: var(--font-size-sm);">Bu ürünü kalıcı olarak sil</p>
                                    </div>
                                    <div class="admin-menu-arrow">
                                        <i class="bi bi-chevron-right"></i>
                                    </div>
                                </a>

                                <!-- Yeni Ürün Ekle -->
                                <a href="{% url 'urunler:urun_ekle' %}" class="admin-menu-item">
                                    <div class="admin-menu-icon">
                                        <i class="bi bi-plus-circle-fill"></i>
                                    </div>
                                    <div class="admin-menu-content">
                                        <h5>Yeni Ürün Ekle</h5>
                                        <p class="text-muted" style="font-size: var(--font-size-sm);">Mağazaya yeni bir ürün ekle</p>
                                    </div>
                                    <div class="admin-menu-arrow">
                                        <i class="bi bi-chevron-right"></i>
                                    </div>
                                </a>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Satın Al Butonu -->
                        {% if product.is_available %}
                        <div class="p-3 text-center">
                            <button class="modern-btn modern-btn-primary">
                                <i class="bi bi-bag-plus me-2"></i>Sepete Ekle
                            </button>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Ürün Etiketleri -->
                <div class="modern-card mb-4 wow fadeInRight etiketler-karti" data-wow-delay="0.4s">
                    <div class="modern-card-header">
                        <h3><i class="bi bi-tags me-2 text-white"></i>Etiketler</h3>
                    </div>
                    <div class="modern-card-body">
                        <div class="modern-tag-cloud">
                            {% for tag in product.tags.all %}
                            <a href="{% url 'urunler:urun_listesi' %}?tag={{ tag.slug }}" class="modern-tag">
                                {{ tag.name }}
                            </a>
                            {% empty %}
                            <span class="text-muted">Bu ürün için henüz etiket eklenmemiş</span>
                            {% endfor %}
                        </div>
                    </div>
                </div>

                <!-- Benzer Ürünler -->
                <div class="modern-card wow fadeInRight benzer-urunler-karti" data-wow-delay="0.5s">
                    <div class="modern-card-header">
                        <h3><i class="bi bi-grid me-2 text-white"></i>Benzer Ürünler</h3>
                        <div class="small text-white-50 mt-1">Gelişmiş benzerlik algoritması ile</div>
                    </div>
                    <div class="modern-card-body p-0 sidebar-body">
                        <ul class="modern-sidebar-list">
                            {% for similar_product in similar_products %}
                            <li>
                                <a href="{% url 'urunler:urun_detay' similar_product.id similar_product.slug %}" class="modern-sidebar-item">
                                    <div class="modern-sidebar-thumbnail">
                                        <img src="{{ similar_product.images.first.image.url }}" alt="{{ similar_product.name }}">
                                    </div>
                                    <div class="modern-sidebar-info">
                                        <h6 class="mb-1 autumn-title text-navy">{{ similar_product.name }}</h6>
                                        <span class="text-primary fw-bold small">₺{{ similar_product.price }}</span>

                                        <!-- Benzerlik Puanı -->
                                        <div class="mt-1">
                                            <div class="similarity-score" title="Benzerlik Puanı">
                                                {% with score=similar_product.similarity_score|floatformat:1 %}
                                                    {% if score >= 6 %}
                                                        <span class="badge bg-success">Çok Benzer ({{ score }})</span>
                                                    {% elif score >= 3 %}
                                                        <span class="badge bg-moss">Benzer ({{ score }})</span>
                                                    {% else %}
                                                        <span class="badge bg-secondary">Az Benzer ({{ score }})</span>
                                                    {% endif %}
                                                {% endwith %}
                                            </div>
                                        </div>

                                        <!-- Ortak Etiketler -->
                                        {% if similar_product.tags.all %}
                                        <div class="mt-1">
                                            {% for tag in similar_product.tags.all %}
                                                {% if tag in product.tags.all %}
                                                <span class="badge bg-moss text-white">{{ tag.name }}</span>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                    </div>
                                </a>
                            </li>
                            {% empty %}
                            <li class="text-center p-3">
                                <span class="text-muted">Benzer ürün bulunamadı</span>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {% if request.user.is_authenticated and request.user.is_superuser %}


    <!-- Ürün Silme Modal (Sadece Yöneticiler) -->
    <div class="modal fade" id="deleteProductModal" tabindex="-1" aria-labelledby="deleteProductModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header" style="background: linear-gradient(135deg, var(--color-brown) 0%, var(--color-sand) 100%); color: white;">
                    <h5 class="modal-title" id="deleteProductModalLabel"><i class="bi bi-trash me-2"></i>Ürünü Sil</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Kapat"></button>
                </div>
                <div class="modal-body">
                    <p class="mb-0">Bu ürünü silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.</p>
                    <p class="text-danger mt-3"><strong>Uyarı:</strong> Ürün ile ilişkili tüm fotoğraflar da silinecektir.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="modern-btn modern-btn-outline" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-1"></i>İptal
                    </button>
                    <form action="{% url 'urunler:urun_sil' product.id product.slug %}" method="post">
                        {% csrf_token %}
                        <button type="submit" class="modern-btn modern-btn-primary">
                            <i class="bi bi-trash me-1"></i>Evet, Sil
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</section>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/swiper@9/swiper-bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/glightbox/dist/js/glightbox.min.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Swiper başlatma
        const swiper = new Swiper('.product-slider', {
            loop: true,
            pagination: {
                el: '.swiper-pagination',
                clickable: true
            },
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev'
            },
            autoplay: {
                delay: 5000,
                disableOnInteraction: false
            }
        });

        // GLightbox başlatma
        const lightbox = GLightbox({
            selector: '.modern-btn-circle',
            touchNavigation: true,
            loop: true,
            autoplayVideos: true
        });

        // Fotoğraf önizleme
        const photoInput = document.getElementById('id_image');
        const previewContainer = document.getElementById('preview-container');
        const imagePreview = document.getElementById('image-preview');

        if (photoInput) {
            photoInput.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        imagePreview.src = e.target.result;
                        previewContainer.classList.remove('d-none');
                    }

                    reader.readAsDataURL(this.files[0]);
                } else {
                    previewContainer.classList.add('d-none');
                }
            });
        }

        // Modal kapatıldığında formu sıfırla
        const addPhotoModal = document.getElementById('addPhotoModal');
        if (addPhotoModal) {
            addPhotoModal.addEventListener('hidden.bs.modal', function() {
                const form = this.querySelector('form');
                if (form) {
                    form.reset();
                    previewContainer.classList.add('d-none');
                }
            });
        }
    });
</script>
{% endblock %}
