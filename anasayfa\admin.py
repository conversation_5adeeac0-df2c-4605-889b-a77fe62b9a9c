from django.contrib import admin
from django.contrib import messages
from .models import Hakkimda, Egitim, IletisimBilgisi, SosyalMedya

class EgitimInline(admin.TabularInline):
    model = Egitim
    extra = 1

@admin.register(Hakkimda)
class HakkimdaAdmin(admin.ModelAdmin):
    list_display = ('baslik', 'user', 'is_primary', 'guncelleme_tarihi')
    search_fields = ('baslik', 'aciklama')
    list_filter = ('is_primary', 'guncelleme_tarihi')
    list_editable = ('is_primary',)
    inlines = [EgitimInline]
    actions = ['make_primary']

    def make_primary(self, request, queryset):
        if queryset.count() > 1:
            self.message_user(request, "Sadece bir profil birincil olarak işaretlenebilir.", messages.ERROR)
            return

        queryset.update(is_primary=True)
        self.message_user(request, "Seçilen profil birincil olarak işaretlendi.")

    make_primary.short_description = "Seçilen profili birincil olarak işaretle"

@admin.register(IletisimBilgisi)
class IletisimBilgisiAdmin(admin.ModelAdmin):
    list_display = ('user', 'email', 'telefon', 'is_primary', 'guncelleme_tarihi')
    search_fields = ('email', 'telefon', 'adres')
    list_filter = ('is_primary', 'guncelleme_tarihi')
    list_editable = ('is_primary',)
    actions = ['make_primary']

    def make_primary(self, request, queryset):
        if queryset.count() > 1:
            self.message_user(request, "Sadece bir iletişim bilgisi birincil olarak işaretlenebilir.", messages.ERROR)
            return

        queryset.update(is_primary=True)
        self.message_user(request, "Seçilen iletişim bilgisi birincil olarak işaretlendi.")

    make_primary.short_description = "Seçilen iletişim bilgisini birincil olarak işaretle"

@admin.register(SosyalMedya)
class SosyalMedyaAdmin(admin.ModelAdmin):
    list_display = ('user', 'platform', 'url', 'aktif', 'is_primary_set', 'guncelleme_tarihi')
    list_filter = ('platform', 'aktif', 'is_primary_set', 'guncelleme_tarihi')
    search_fields = ('url',)
    list_editable = ('aktif', 'is_primary_set')
    actions = ['make_primary_set']

    def make_primary_set(self, request, queryset):
        # Aynı kullanıcıya ait olmayan sosyal medya hesapları seçildiyse uyarı ver
        users = queryset.values_list('user', flat=True).distinct()
        if users.count() > 1:
            self.message_user(request, "Sadece aynı kullanıcıya ait sosyal medya hesapları birincil olarak işaretlenebilir.", messages.ERROR)
            return

        # Seçilen sosyal medya hesaplarının kullanıcısını birincil olarak işaretle
        user_id = users.first()
        SosyalMedya.objects.filter(user_id=user_id).update(is_primary_set=True)
        SosyalMedya.objects.exclude(user_id=user_id).update(is_primary_set=False)

        self.message_user(request, f"Seçilen kullanıcının tüm sosyal medya hesapları birincil olarak işaretlendi.")

    make_primary_set.short_description = "Seçilen kullanıcının sosyal medya hesaplarını birincil olarak işaretle"
