import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth import get_user_model
from django.utils import timezone
from .models import Conversation, Message
from .permissions import IsApprovedUserOrAdmin # İzin sınıfını import ediyoruz

User = get_user_model()

class ChatConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.room_name = self.scope['url_route']['kwargs']['room_name']
        self.room_group_name = f'chat_{self.room_name}'
        self.user = self.scope['user']

        # Kullanıcının kimliği doğrulanmış ve onaylanmış veya yönetici mi kontrol et
        if not self.user.is_authenticated or not await self.is_approved_user_or_admin(self.user):
             await self.close()
             return

        # Konuşmayı al veya oluştur
        self.conversation = await self.get_or_create_conversation()

        if not self.conversation:
            await self.close()
            return

        # Kullanıcının konuşmanın bir parçası olup olmadığını kontrol et
        if not await self.is_user_in_conversation(self.user, self.conversation):
             await self.close()
             return

        # Join room group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )

        await self.accept()

        # Kullanıcının çevrimiçi olduğunu diğer kullanıcılara bildir
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'user_status',
                'user_id': self.user.id,
                'status': 'online'
            }
        )


    async def disconnect(self, close_code):
        # Leave room group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )

        # Kullanıcının çevrimdışı olduğunu diğer kullanıcılara bildir
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'user_status',
                'user_id': self.user.id,
                'status': 'offline'
            }
        )


    # Receive message from WebSocket
    async def receive(self, text_data):
        text_data_json = json.loads(text_data)
        message_type = text_data_json.get('type')

        if message_type == 'chat_message':
            message_content = text_data_json['message']

            # Mesajı veritabanına kaydet
            message = await self.create_message(self.user, self.conversation, message_content)

            # Send message to room group
            await self.channel_layer.group_send(
                self.room_group_name,
                {
                    'type': 'chat_message',
                    'message': message_content,
                    'sender': self.user.username,
                    'timestamp': message.timestamp.isoformat(),
                }
            )
        elif message_type == 'edit_message':
            message_id = text_data_json.get('message_id')
            new_content = text_data_json.get('content')
            if message_id and new_content:
                await self.edit_message(message_id, new_content)
        elif message_type == 'delete_message':
            message_id = text_data_json.get('message_id')
            if message_id:
                await self.delete_message(message_id)


    # Receive message from room group
    async def chat_message(self, event):
        message = event['message']
        sender = event['sender']
        timestamp = event['timestamp']

        # Send message to WebSocket
        await self.send(text_data=json.dumps({
            'type': 'chat_message',
            'message': message,
            'sender': sender,
            'timestamp': timestamp,
        }))

    # Receive user status from room group
    async def user_status(self, event):
        user_id = event['user_id']
        status = event['status']

        # Send user status to WebSocket
        await self.send(text_data=json.dumps({
            'type': 'user_status',
            'user_id': user_id,
            'status': status,
        }))

    # Receive message edited from room group
    async def message_edited(self, event):
        message_id = event['message_id']
        new_content = event['new_content']

        await self.send(text_data=json.dumps({
            'type': 'message_edited',
            'message_id': message_id,
            'new_content': new_content,
        }))

    # Receive message deleted from room group
    async def message_deleted(self, event):
        message_id = event['message_id']

        await self.send(text_data=json.dumps({
            'type': 'message_deleted',
            'message_id': message_id,
        }))


    @database_sync_to_async
    def is_approved_user_or_admin(self, user):
        if user.is_staff or user.is_superuser:
            return True
        if user.is_authenticated:
            try:
                return hasattr(user, 'profil') and user.profil.is_approved
            except:
                return False
        return False

    @database_sync_to_async
    def get_or_create_conversation(self):
        # room_name'e göre konuşmayı bul veya oluştur
        # Basitlik için, room_name'in katılımcı ID'lerinin birleşimi olduğunu varsayalım (örn: user1_user2)
        # Gerçek uygulamada daha karmaşık bir mantık gerekebilir.
        try:
            # Katılımcı ID'lerini room_name'den ayır
            participant_ids = sorted([int(id) for id in self.room_name.split('_')])
            if len(participant_ids) != 2:
                 return None # Geçersiz room_name formatı

            user1_id, user2_id = participant_ids

            # Kullanıcıların varlığını kontrol et
            user1 = User.objects.filter(id=user1_id).first()
            user2 = User.objects.filter(id=user2_id).first()

            if not user1 or not user2:
                 return None # Kullanıcılar bulunamadı

            # Kullanıcıların onaylı veya yönetici olduğunu kontrol et
            if not self.is_approved_user_or_admin(user1) or not self.is_approved_user_or_admin(user2):
                 return None

            # Mevcut konuşmayı bul
            conversation = Conversation.objects.filter(participants=user1).filter(participants=user2).first()

            if not conversation:
                # Yeni konuşma oluştur
                conversation = Conversation.objects.create()
                conversation.participants.add(user1, user2)

            return conversation
        except (ValueError, User.DoesNotExist, Exception) as e:
            print(f"Error getting or creating conversation: {e}")
            return None

    @database_sync_to_async
    def create_message(self, sender, conversation, content):
        return Message.objects.create(sender=sender, conversation=conversation, content=content)

    @database_sync_to_async
    def edit_message(self, message_id, new_content):
        try:
            message = Message.objects.get(id=message_id, sender=self.user, conversation=self.conversation)
            message.content = new_content
            message.save()
            # Mesajın düzenlendiğini gruba bildir
            self.channel_layer.group_send(
                self.room_group_name,
                {
                    'type': 'message_edited',
                    'message_id': message.id,
                    'new_content': message.content,
                }
            )
        except Message.DoesNotExist:
            # Kullanıcı mesajı düzenleme yetkisine sahip değil veya mesaj bulunamadı
            pass # Hata yönetimi burada yapılabilir

    @database_sync_to_async
    def delete_message(self, message_id):
        try:
            message = Message.objects.get(id=message_id, sender=self.user, conversation=self.conversation)
            message_id_to_send = message.id # Mesaj silinmeden önce ID'yi al
            message.delete()
            # Mesajın silindiğini gruba bildir
            self.channel_layer.group_send(
                self.room_group_name,
                {
                    'type': 'message_deleted',
                    'message_id': message_id_to_send,
                }
            )
        except Message.DoesNotExist:
            # Kullanıcı mesajı silme yetkisine sahip değil veya mesaj bulunamadı
            pass # Hata yönetimi burada yapılabilir


    @database_sync_to_async
    def is_user_in_conversation(self, user, conversation):
        return conversation.participants.filter(id=user.id).exists()