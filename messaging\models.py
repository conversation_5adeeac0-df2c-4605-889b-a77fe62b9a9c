from django.db import models
from django.contrib.auth import get_user_model
from django.conf import settings

User = get_user_model()

class Conversation(models.Model):
    """
    Kullanıcılar arasındaki konuşmaları temsil eder.
    """
    participants = models.ManyToManyField(User, related_name='conversations')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Conversation {self.id}"

class Message(models.Model):
    """
    Bir konuşma içindeki tek bir mesajı temsil eder.
    """
    conversation = models.ForeignKey(Conversation, related_name='messages', on_delete=models.CASCADE)
    sender = models.ForeignKey(User, related_name='sent_messages', on_delete=models.CASCADE)
    content = models.TextField()
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['timestamp']

    def __str__(self):
        return f"Message from {self.sender.username} in Conversation {self.conversation.id}"

class Announcement(models.Model):
    """
    Yöneticiler tarafından onaylanmış kullanıcılara gönderilen duyuruları temsil eder.
    """
    sender = models.ForeignKey(User, related_name='sent_announcements', on_delete=models.CASCADE) # Gönderen yönetici olmalı
    content = models.TextField()
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-timestamp']

    def __str__(self):
        return f"Announcement from {self.sender.username} at {self.timestamp.strftime('%Y-%m-%d %H:%M')}"

class UserAnnouncementReadStatus(models.Model):
    """
    Onaylanmış kullanıcıların duyuruları okuma durumunu takip eder.
    """
    user = models.ForeignKey(User, related_name='announcement_read_statuses', on_delete=models.CASCADE)
    announcement = models.ForeignKey(Announcement, related_name='read_statuses', on_delete=models.CASCADE)
    is_read = models.BooleanField(default=False)
    read_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        unique_together = ('user', 'announcement')

    def __str__(self):
        return f"{self.user.username} read status for Announcement {self.announcement.id}: {self.is_read}"

class Notification(models.Model):
    NOTIFICATION_TYPES = [
        ('new_message', 'Yeni Mesaj'),
        ('new_conversation', 'Yeni Konuşma'),
        ('message_edited', 'Mesaj Düzenlendi'),
        ('announcement', 'Duyuru'),
    ]

    recipient = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notifications')
    sender = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sent_notifications', null=True, blank=True)
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES)
    title = models.CharField(max_length=255)
    message = models.TextField()
    is_read = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    # İlgili objeler için foreign key'ler
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, null=True, blank=True)
    message_obj = models.ForeignKey(Message, on_delete=models.CASCADE, null=True, blank=True)
    announcement = models.ForeignKey(Announcement, on_delete=models.CASCADE, null=True, blank=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.recipient.username} - {self.title}"
