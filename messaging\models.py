from django.db import models
from django.contrib.auth import get_user_model
from django.conf import settings

User = get_user_model()

class Conversation(models.Model):
    """
    Kullanıcılar arasındaki konuşmaları temsil eder.
    """
    participants = models.ManyToManyField(User, related_name='conversations')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Conversation {self.id}"

class Message(models.Model):
    """
    Bir konuşma içindeki tek bir mesajı temsil eder.
    """
    conversation = models.ForeignKey(Conversation, related_name='messages', on_delete=models.CASCADE)
    sender = models.ForeignKey(User, related_name='sent_messages', on_delete=models.CASCADE)
    content = models.TextField()
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['timestamp']

    def __str__(self):
        return f"Message from {self.sender.username} in Conversation {self.conversation.id}"

class Announcement(models.Model):
    """
    Yöneticiler tarafından onaylanmış kullanıcılara gönderilen duyuruları temsil eder.
    """
    sender = models.ForeignKey(User, related_name='sent_announcements', on_delete=models.CASCADE) # Gönderen yönetici olmalı
    content = models.TextField()
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-timestamp']

    def __str__(self):
        return f"Announcement from {self.sender.username} at {self.timestamp.strftime('%Y-%m-%d %H:%M')}"

class UserAnnouncementReadStatus(models.Model):
    """
    Onaylanmış kullanıcıların duyuruları okuma durumunu takip eder.
    """
    user = models.ForeignKey(User, related_name='announcement_read_statuses', on_delete=models.CASCADE)
    announcement = models.ForeignKey(Announcement, related_name='read_statuses', on_delete=models.CASCADE)
    is_read = models.BooleanField(default=False)
    read_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        unique_together = ('user', 'announcement')

    def __str__(self):
        return f"{self.user.username} read status for Announcement {self.announcement.id}: {self.is_read}"
