{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - Kü<PERSON> Cadısı{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'assets/css/modern-theme.css' %}">
{% endblock %}

{% block content %}
<section class="calisma-form-section py-5">
    <div class="container">
        <!-- <PERSON>şlık Bölümü -->
        <div class="section-header text-center mb-5 wow fadeIn" data-wow-delay="0.2s">
            <h1 class="display-4 fw-bold mb-3 section-title">{{ title }}</h1>
            <p class="lead section-subtitle">{% if video %}{{ video.baslik }} videosunu düzenleyin{% else %}Video bilgilerini doldurun{% endif %}</p>
            <div class="title-decoration">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="form-card wow fadeInUp" data-wow-delay="0.3s">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="bi bi-camera-video me-2"></i>Video Bilgileri</h5>
                        {% if video %}
                        <a href="{% url 'nasilyapilir:video_detay' video.slug %}" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-eye me-1"></i>Görüntüle
                        </a>
                        {% endif %}
                    </div>
                    <div class="card-body">
                        <form method="post" enctype="multipart/form-data" class="video-form">
                            {% csrf_token %}

                            {% if form.errors %}
                            <div class="alert alert-danger">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                Lütfen formdaki hataları düzeltin.
                            </div>
                            {% endif %}

                            <div class="row g-4">
                                <!-- Video Başlığı -->
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="{{ form.baslik.id_for_label }}" class="form-label">{{ form.baslik.label }} <span class="text-danger">*</span></label>
                                        {{ form.baslik }}
                                        {% if form.baslik.help_text %}
                                        <small class="form-text text-muted">{{ form.baslik.help_text }}</small>
                                        {% endif %}
                                        {% if form.baslik.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.baslik.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- YouTube URL -->
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="{{ form.youtube_url.id_for_label }}" class="form-label">{{ form.youtube_url.label }} <span class="text-danger">*</span></label>
                                        {{ form.youtube_url }}
                                        {% if form.youtube_url.help_text %}
                                        <small class="form-text text-muted">{{ form.youtube_url.help_text }}</small>
                                        {% endif %}
                                        {% if form.youtube_url.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.youtube_url.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Kategori -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="{{ form.kategori.id_for_label }}" class="form-label">{{ form.kategori.label }} <span class="text-danger">*</span></label>
                                        {{ form.kategori }}
                                        {% if form.kategori.help_text %}
                                        <small class="form-text text-muted">{{ form.kategori.help_text }}</small>
                                        {% endif %}
                                        {% if form.kategori.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.kategori.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Etiketler -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="{{ form.tags.id_for_label }}" class="form-label">{{ form.tags.label }}</label>
                                        {{ form.tags }}
                                        <small class="form-text text-muted">Virgülle ayırarak birden fazla etiket ekleyebilirsiniz</small>
                                        {% if form.tags.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.tags.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Açıklama -->
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="{{ form.aciklama.id_for_label }}" class="form-label">{{ form.aciklama.label }} <span class="text-danger">*</span></label>
                                        {{ form.aciklama }}
                                        {% if form.aciklama.help_text %}
                                        <small class="form-text text-muted">{{ form.aciklama.help_text }}</small>
                                        {% endif %}
                                        {% if form.aciklama.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.aciklama.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Önizleme Resmi -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="{{ form.onizleme_resmi.id_for_label }}" class="form-label">{{ form.onizleme_resmi.label }}</label>
                                        {{ form.onizleme_resmi }}
                                        {% if form.onizleme_resmi.help_text %}
                                        <small class="form-text text-muted">{{ form.onizleme_resmi.help_text }}</small>
                                        {% endif %}
                                        {% if form.onizleme_resmi.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.onizleme_resmi.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Aktif -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="form-check">
                                            {{ form.aktif }}
                                            <label for="{{ form.aktif.id_for_label }}" class="form-check-label">{{ form.aktif.label }}</label>
                                        </div>
                                        {% if form.aktif.help_text %}
                                        <small class="form-text text-muted">{{ form.aktif.help_text }}</small>
                                        {% endif %}
                                        {% if form.aktif.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.aktif.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Butonlar -->
                                <div class="col-12 mt-4">
                                    <div class="form-actions">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-save me-2"></i>{% if video %}Güncelle{% else %}Kaydet{% endif %}
                                        </button>
                                        <a href="{% if video %}{% url 'nasilyapilir:video_detay' video.slug %}{% else %}{% url 'nasilyapilir:video_listesi' %}{% endif %}" class="btn btn-outline-secondary ms-2">
                                            <i class="bi bi-x-circle me-2"></i>İptal
                                        </a>
                                        {% if video %}
                                        <a href="{% url 'nasilyapilir:video_sil' video.slug %}" class="btn btn-danger ms-auto">
                                            <i class="bi bi-trash me-2"></i>Videoyu Sil
                                        </a>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

{% endblock %}

<style>
    /* Modern renk paleti */
    :root {
        --primary-color: #734429;
        --secondary-color: #402401;
        --accent-color: #D9B391;
        --light-accent: #E0D8C8;
        --dark-bg: #2D1A00;
        --text-color: #402401;
        --light-text: #734429;
        --white: #ffffff;
        --off-white: #F8F5F0;
        --light-gray: #E9E4D9;
        --success-color: #2E7D32;
        --error-color: #C62828;
    }

    /* Genel stil */
    .calisma-form-section {
        background: linear-gradient(135deg, var(--off-white) 0%, var(--light-accent) 100%);
        padding: 4rem 0;
    }

    /* Başlık bölümü */
    .section-header {
        margin-bottom: 3rem;
    }

    .section-title {
        color: var(--primary-color);
        font-weight: 700;
        margin-bottom: 1rem;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
    }

    .section-subtitle {
        color: var(--text-color);
        font-size: 1.25rem;
        max-width: 800px;
        margin: 0 auto;
    }

    .title-decoration {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 2rem;
        gap: 0.5rem;
    }

    .title-decoration span {
        width: 40px;
        height: 3px;
        background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
        border-radius: 2px;
    }

    .title-decoration span:nth-child(2) {
        width: 60px;
        height: 4px;
    }

    /* Form kartı */
    .form-card {
        background: var(--white);
        border-radius: 20px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        overflow: hidden;
        border: 1px solid rgba(115, 68, 41, 0.1);
        transition: all 0.3s ease;
    }

    .form-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 25px 50px rgba(0,0,0,0.15);
    }

    .card-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: var(--white);
        padding: 1.5rem 2rem;
        border-bottom: none;
    }

    .card-header h5 {
        font-weight: 600;
        margin: 0;
    }

    .card-body {
        padding: 2.5rem;
    }

    /* Form elemanları */
    .form-group {
        margin-bottom: 2rem;
    }

    .form-label {
        color: var(--primary-color);
        font-weight: 600;
        margin-bottom: 0.75rem;
        font-size: 1rem;
    }

    .form-control {
        border: 2px solid var(--light-gray);
        border-radius: 12px;
        padding: 0.875rem 1.25rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        background-color: var(--off-white);
    }

    .form-control:focus {
        border-color: var(--accent-color);
        box-shadow: 0 0 0 0.25rem rgba(217, 179, 145, 0.25);
        background-color: var(--white);
    }

    .form-control:hover {
        border-color: var(--accent-color);
        background-color: var(--white);
    }

    /* Select elemanları */
    .form-select {
        border: 2px solid var(--light-gray);
        border-radius: 12px;
        padding: 0.875rem 1.25rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        background-color: var(--off-white);
    }

    .form-select:focus {
        border-color: var(--accent-color);
        box-shadow: 0 0 0 0.25rem rgba(217, 179, 145, 0.25);
        background-color: var(--white);
    }

    /* Textarea */
    textarea.form-control {
        min-height: 120px;
        resize: vertical;
    }

    /* Checkbox */
    .form-check-input {
        width: 1.25rem;
        height: 1.25rem;
        border: 2px solid var(--light-gray);
        border-radius: 6px;
    }

    .form-check-input:checked {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    .form-check-input:focus {
        box-shadow: 0 0 0 0.25rem rgba(217, 179, 145, 0.25);
    }

    .form-check-label {
        color: var(--text-color);
        font-weight: 500;
        margin-left: 0.5rem;
    }

    /* Butonlar */
    .form-actions {
        display: flex;
        gap: 1rem;
        align-items: center;
        flex-wrap: wrap;
        padding-top: 1rem;
        border-top: 1px solid var(--light-gray);
    }

    .btn {
        padding: 0.875rem 2rem;
        border-radius: 50px;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        border: none;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-width: 140px;
    }

    .btn-primary {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: var(--white);
        box-shadow: 0 4px 15px rgba(115, 68, 41, 0.3);
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(115, 68, 41, 0.4);
        color: var(--white);
    }

    .btn-outline-secondary {
        background: transparent;
        border: 2px solid var(--light-gray);
        color: var(--text-color);
    }

    .btn-outline-secondary:hover {
        background: var(--light-gray);
        border-color: var(--accent-color);
        color: var(--primary-color);
        transform: translateY(-2px);
    }

    .btn-danger {
        background: linear-gradient(135deg, var(--error-color) 0%, #B71C1C 100%);
        color: var(--white);
        box-shadow: 0 4px 15px rgba(198, 40, 40, 0.3);
    }

    .btn-danger:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(198, 40, 40, 0.4);
        color: var(--white);
    }

    .btn-sm {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        min-width: auto;
    }

    /* Yardım metinleri */
    .form-text {
        color: var(--light-text);
        font-size: 0.875rem;
        margin-top: 0.5rem;
    }

    /* Hata mesajları */
    .invalid-feedback {
        color: var(--error-color);
        font-size: 0.875rem;
        margin-top: 0.5rem;
    }

    .alert-danger {
        background-color: rgba(198, 40, 40, 0.1);
        border: 1px solid rgba(198, 40, 40, 0.2);
        color: var(--error-color);
        border-radius: 12px;
        padding: 1rem 1.5rem;
        margin-bottom: 2rem;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .calisma-form-section {
            padding: 2rem 0;
        }

        .card-body {
            padding: 1.5rem;
        }

        .form-actions {
            flex-direction: column;
            align-items: stretch;
        }

        .btn {
            width: 100%;
            margin-bottom: 0.5rem;
        }

        .section-title {
            font-size: 2rem;
        }

        .section-subtitle {
            font-size: 1.1rem;
        }
    }

    /* Animasyonlar */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .wow.fadeInUp {
        animation: fadeInUp 0.8s ease-out;
    }

    .wow.fadeIn {
        animation: fadeIn 0.8s ease-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
</style>

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/wow/1.1.2/wow.min.js"></script>
<script>
    // WOW animasyonlarını başlat
    new WOW().init();
</script>
{% endblock %}
