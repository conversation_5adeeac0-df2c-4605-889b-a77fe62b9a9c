{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - Kü<PERSON> Cadısı{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'assets/css/modern-theme.css' %}">
{% endblock %}

{% block content %}
<section class="video-form-section py-5">
    <div class="container">
        <!-- Başlık Bölümü -->
        <div class="section-header text-center mb-5 wow fadeIn" data-wow-delay="0.2s">
            <h1 class="section-title">{{ title }}</h1>
            <p class="section-subtitle">Video bilgilerini doldurun</p>
            <div class="title-decoration">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>

        <!-- Form Kartı -->
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="form-card wow fadeInUp" data-wow-delay="0.3s">
                    <div class="card-header">
                        <h3><i class="bi bi-camera-video me-2"></i>{{ title }}</h3>
                    </div>
                    <div class="card-body">
                        <form method="post" enctype="multipart/form-data">
                            {% csrf_token %}
                            <div class="row">
                                <!-- Video Başlığı -->
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="{{ form.baslik.id_for_label }}" class="form-label">{{ form.baslik.label }} <span class="text-danger">*</span></label>
                                        {{ form.baslik }}
                                        {% if form.baslik.help_text %}
                                        <small class="form-text text-muted">{{ form.baslik.help_text }}</small>
                                        {% endif %}
                                        {% if form.baslik.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.baslik.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- YouTube URL -->
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="{{ form.youtube_url.id_for_label }}" class="form-label">{{ form.youtube_url.label }} <span class="text-danger">*</span></label>
                                        {{ form.youtube_url }}
                                        {% if form.youtube_url.help_text %}
                                        <small class="form-text text-muted">{{ form.youtube_url.help_text }}</small>
                                        {% endif %}
                                        {% if form.youtube_url.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.youtube_url.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Kategori -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="{{ form.kategori.id_for_label }}" class="form-label">{{ form.kategori.label }} <span class="text-danger">*</span></label>
                                        {{ form.kategori }}
                                        {% if form.kategori.help_text %}
                                        <small class="form-text text-muted">{{ form.kategori.help_text }}</small>
                                        {% endif %}
                                        {% if form.kategori.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.kategori.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Etiketler -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="{{ form.tags.id_for_label }}" class="form-label">{{ form.tags.label }}</label>
                                        {{ form.tags }}
                                        {% if form.tags.help_text %}
                                        <small class="form-text text-muted">{{ form.tags.help_text }}</small>
                                        {% endif %}
                                        {% if form.tags.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.tags.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Açıklama -->
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="{{ form.aciklama.id_for_label }}" class="form-label">{{ form.aciklama.label }} <span class="text-danger">*</span></label>
                                        {{ form.aciklama }}
                                        {% if form.aciklama.help_text %}
                                        <small class="form-text text-muted">{{ form.aciklama.help_text }}</small>
                                        {% endif %}
                                        {% if form.aciklama.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.aciklama.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Önizleme Resmi -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="{{ form.onizleme_resmi.id_for_label }}" class="form-label">{{ form.onizleme_resmi.label }}</label>
                                        {{ form.onizleme_resmi }}
                                        {% if form.onizleme_resmi.help_text %}
                                        <small class="form-text text-muted">{{ form.onizleme_resmi.help_text }}</small>
                                        {% endif %}
                                        {% if form.onizleme_resmi.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.onizleme_resmi.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Aktif -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="form-check">
                                            {{ form.aktif }}
                                            <label for="{{ form.aktif.id_for_label }}" class="form-check-label">{{ form.aktif.label }}</label>
                                        </div>
                                        {% if form.aktif.help_text %}
                                        <small class="form-text text-muted">{{ form.aktif.help_text }}</small>
                                        {% endif %}
                                        {% if form.aktif.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.aktif.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Butonlar -->
                                <div class="col-12 mt-4">
                                    <div class="form-actions">
                                        <button type="submit" class="modern-btn">
                                            <i class="bi bi-check-circle me-2"></i>{% if video %}Güncelle{% else %}Kaydet{% endif %}
                                        </button>
                                        <a href="{% if video %}{% url 'nasilyapilir:video_detay' video.slug %}{% else %}{% url 'nasilyapilir:video_listesi' %}{% endif %}" class="modern-btn modern-btn-outline ms-2">
                                            <i class="bi bi-x-circle me-2"></i>İptal
                                        </a>
                                        {% if video %}
                                        <button type="button" class="modern-btn modern-btn-danger ms-auto" data-bs-toggle="modal" data-bs-target="#deleteModal">
                                            <i class="bi bi-trash me-2"></i>Sil
                                        </button>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

{% if video %}
<!-- Silme Onay Modalı -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content modern-modal">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel"><i class="bi bi-exclamation-triangle me-2"></i>Videoyu Sil</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Kapat"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <p>Bu videoyu silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.</p>
                    <p class="fw-bold mt-2">{{ video.baslik }}</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="modern-btn modern-btn-outline" data-bs-dismiss="modal">İptal</button>
                <form action="{% url 'nasilyapilir:video_sil' video.slug %}" method="post">
                    {% csrf_token %}
                    <button type="submit" class="modern-btn modern-btn-danger">Sil</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
