{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - <PERSON>ü<PERSON> Cadısı{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'assets/css/modern-theme.css' %}">
{% endblock %}

{% block content %}
<section class="modern-section py-5">
    <div class="container">
        <!-- Başlık Bölümü -->
        <div class="modern-section-header text-center mb-5 wow fadeIn" data-wow-delay="0.2s">
            <h1 class="modern-title">{{ title }}</h1>
            <p class="modern-subtitle">{% if video %}{{ video.baslik }} videosunu düzenleyin{% else %}Video bilgilerini doldurun{% endif %}</p>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="modern-card wow fadeInUp" data-wow-delay="0.3s">
                    <div class="modern-card-header">
                        <h3><i class="bi bi-camera-video me-2"></i>Video Bilgileri</h3>
                    </div>
                    <div class="modern-card-body">
                        <form method="post" enctype="multipart/form-data" class="calisma-form">
                            {% csrf_token %}

                            {% if form.errors %}
                            <div class="alert alert-danger">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                Lütfen formdaki hataları düzeltin.
                            </div>
                            {% endif %}

                            <div class="row g-4">
                                <!-- Video Başlığı -->
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="{{ form.baslik.id_for_label }}" class="form-label">{{ form.baslik.label }} <span class="text-danger">*</span></label>
                                        {{ form.baslik }}
                                        {% if form.baslik.help_text %}
                                        <small class="form-text text-muted">{{ form.baslik.help_text }}</small>
                                        {% endif %}
                                        {% if form.baslik.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.baslik.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- YouTube URL -->
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="{{ form.youtube_url.id_for_label }}" class="form-label">{{ form.youtube_url.label }} <span class="text-danger">*</span></label>
                                        {{ form.youtube_url }}
                                        {% if form.youtube_url.help_text %}
                                        <small class="form-text text-muted">{{ form.youtube_url.help_text }}</small>
                                        {% endif %}
                                        {% if form.youtube_url.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.youtube_url.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Kategori -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="{{ form.kategori.id_for_label }}" class="form-label">{{ form.kategori.label }} <span class="text-danger">*</span></label>
                                        {{ form.kategori }}
                                        {% if form.kategori.help_text %}
                                        <small class="form-text text-muted">{{ form.kategori.help_text }}</small>
                                        {% endif %}
                                        {% if form.kategori.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.kategori.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Etiketler -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="{{ form.tags.id_for_label }}" class="form-label">{{ form.tags.label }}</label>
                                        {{ form.tags }}
                                        <small class="form-text text-muted">Virgülle ayırarak birden fazla etiket ekleyebilirsiniz</small>
                                        {% if form.tags.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.tags.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Açıklama -->
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="{{ form.aciklama.id_for_label }}" class="form-label">{{ form.aciklama.label }} <span class="text-danger">*</span></label>
                                        {{ form.aciklama }}
                                        {% if form.aciklama.help_text %}
                                        <small class="form-text text-muted">{{ form.aciklama.help_text }}</small>
                                        {% endif %}
                                        {% if form.aciklama.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.aciklama.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Önizleme Resmi -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="{{ form.onizleme_resmi.id_for_label }}" class="form-label">{{ form.onizleme_resmi.label }}</label>
                                        {{ form.onizleme_resmi }}
                                        {% if form.onizleme_resmi.help_text %}
                                        <small class="form-text text-muted">{{ form.onizleme_resmi.help_text }}</small>
                                        {% endif %}
                                        {% if form.onizleme_resmi.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.onizleme_resmi.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Aktif -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="form-check">
                                            {{ form.aktif }}
                                            <label for="{{ form.aktif.id_for_label }}" class="form-check-label">{{ form.aktif.label }}</label>
                                        </div>
                                        {% if form.aktif.help_text %}
                                        <small class="form-text text-muted">{{ form.aktif.help_text }}</small>
                                        {% endif %}
                                        {% if form.aktif.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.aktif.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Butonlar -->
                                <div class="col-12 mt-4">
                                    <div class="form-actions">
                                        <button type="submit" class="modern-btn">
                                            <i class="bi bi-check-circle me-2"></i>{% if video %}Güncelle{% else %}Kaydet{% endif %}
                                        </button>
                                        <a href="{% if video %}{% url 'nasilyapilir:video_detay' video.slug %}{% else %}{% url 'nasilyapilir:video_listesi' %}{% endif %}" class="modern-btn modern-btn-outline ms-2">
                                            <i class="bi bi-x-circle me-2"></i>İptal
                                        </a>
                                        {% if video %}
                                        <a href="{% url 'nasilyapilir:video_sil' video.slug %}" class="modern-btn modern-btn-danger ms-auto">
                                            <i class="bi bi-trash me-2"></i>Sil
                                        </a>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

<style>
    /* Modern renk paleti */
    :root {
        --primary-color: #734429;
        --secondary-color: #402401;
        --accent-color: #D9B391;
        --light-accent: #E0D8C8;
        --dark-bg: #2D1A00;
        --text-color: #402401;
        --light-text: #734429;
        --white: #ffffff;
        --off-white: #F8F5F0;
        --light-gray: #E9E4D9;
        --success-color: #2E7D32;
        --error-color: #C62828;
    }

    /* Genel stil */
    .modern-section {
        background: linear-gradient(135deg, var(--off-white) 0%, var(--light-accent) 100%);
        padding: 4rem 0;
    }

    /* Başlık bölümü */
    .modern-section-header {
        margin-bottom: 3rem;
    }

    .modern-title {
        color: var(--primary-color);
        font-weight: 700;
        margin-bottom: 1rem;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
    }

    .modern-subtitle {
        color: var(--text-color);
        font-size: 1.25rem;
        max-width: 800px;
        margin: 0 auto;
    }

    /* Form Kartı */
    .modern-card {
        border-radius: 20px;
        overflow: hidden;
        background: var(--white);
        box-shadow: 0 10px 25px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
    }

    .modern-card:hover {
        box-shadow: 0 15px 35px rgba(64, 36, 1, 0.1);
    }

    .modern-card-header {
        padding: 1.5rem;
        background: linear-gradient(to right, var(--accent-color), var(--light-accent));
        color: var(--secondary-color);
        border-radius: 20px 20px 0 0;
        font-weight: 600;
        text-shadow: 0 1px 1px rgba(255,255,255,0.5);
    }

    .modern-card-body {
        padding: 2rem;
    }

    /* Form Elemanları */
    .form-label {
        color: var(--text-color);
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .form-control, .form-select {
        padding: 0.8rem 1.2rem;
        border-radius: 10px;
        border: 1px solid var(--light-gray);
        background-color: var(--off-white);
        color: var(--text-color);
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.25rem rgba(115, 68, 41, 0.25);
        background-color: var(--white);
    }

    textarea.form-control {
        min-height: 150px;
    }

    .form-text {
        color: var(--light-text);
        font-size: 0.85rem;
        margin-top: 0.5rem;
    }

    .invalid-feedback {
        color: var(--error-color);
        font-size: 0.85rem;
        margin-top: 0.5rem;
    }

    /* Butonlar */
    .form-actions {
        display: flex;
        align-items: center;
    }

    .modern-btn {
        background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
        border: none;
        padding: 0.8rem 1.5rem;
        border-radius: 50px;
        color: var(--white);
        font-weight: 600;
        box-shadow: 0 5px 15px rgba(115, 68, 41, 0.3);
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
    }

    .modern-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(115, 68, 41, 0.4);
        color: var(--white);
    }

    .modern-btn-outline {
        color: var(--text-color);
        border: 1px solid var(--light-gray);
        background: transparent;
        border-radius: 50px;
        padding: 0.8rem 1.5rem;
        font-weight: 500;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
    }

    .modern-btn-outline:hover {
        background-color: var(--light-gray);
        color: var(--text-color);
    }

    .modern-btn-danger {
        background: linear-gradient(to right, #C62828, #B71C1C);
        border: none;
        padding: 0.8rem 1.5rem;
        border-radius: 50px;
        color: var(--white);
        font-weight: 600;
        box-shadow: 0 5px 15px rgba(198, 40, 40, 0.3);
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
    }

    .modern-btn-danger:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(198, 40, 40, 0.4);
        color: var(--white);
    }

    /* Animasyonlar */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .wow {
        animation: fadeIn 0.8s ease forwards;
    }

    .fadeIn { animation-name: fadeIn; }
    .fadeInUp { animation-name: fadeInUp; }

    @keyframes fadeInUp {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/wow/1.1.2/wow.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // WOW.js başlatma
        new WOW().init();

        // Form elemanlarını düzenleme
        const formControls = document.querySelectorAll('.form-control, .form-select');
        formControls.forEach(control => {
            control.classList.add('form-control');

            if (control.tagName === 'SELECT') {
                control.classList.add('form-select');
            }
        });
    });
</script>
{% endblock %}