{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - <PERSON><PERSON><PERSON> Cadısı{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h3><i class="bi bi-camera-video me-2"></i>{{ title }}</h3>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}

                        {% if form.errors %}
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            Lütfen formdaki hataları düzeltin.
                        </div>
                        {% endif %}

                        <div class="row g-3">
                            <!-- Video Başlığı -->
                            <div class="col-12">
                                <label for="{{ form.baslik.id_for_label }}" class="form-label">{{ form.baslik.label }} <span class="text-danger">*</span></label>
                                {{ form.baslik }}
                                {% if form.baslik.errors %}
                                <div class="text-danger">{{ form.baslik.errors }}</div>
                                {% endif %}
                            </div>

                            <!-- YouTube URL -->
                            <div class="col-12">
                                <label for="{{ form.youtube_url.id_for_label }}" class="form-label">{{ form.youtube_url.label }} <span class="text-danger">*</span></label>
                                {{ form.youtube_url }}
                                {% if form.youtube_url.errors %}
                                <div class="text-danger">{{ form.youtube_url.errors }}</div>
                                {% endif %}
                            </div>

                            <!-- Kategori -->
                            <div class="col-md-6">
                                <label for="{{ form.kategori.id_for_label }}" class="form-label">{{ form.kategori.label }} <span class="text-danger">*</span></label>
                                {{ form.kategori }}
                                {% if form.kategori.errors %}
                                <div class="text-danger">{{ form.kategori.errors }}</div>
                                {% endif %}
                            </div>

                            <!-- Etiketler -->
                            <div class="col-md-6">
                                <label for="{{ form.tags.id_for_label }}" class="form-label">{{ form.tags.label }}</label>
                                {{ form.tags }}
                                <small class="form-text text-muted">Virgülle ayırarak birden fazla etiket ekleyebilirsiniz</small>
                                {% if form.tags.errors %}
                                <div class="text-danger">{{ form.tags.errors }}</div>
                                {% endif %}
                            </div>

                            <!-- Açıklama -->
                            <div class="col-12">
                                <label for="{{ form.aciklama.id_for_label }}" class="form-label">{{ form.aciklama.label }} <span class="text-danger">*</span></label>
                                {{ form.aciklama }}
                                {% if form.aciklama.errors %}
                                <div class="text-danger">{{ form.aciklama.errors }}</div>
                                {% endif %}
                            </div>

                            <!-- Önizleme Resmi -->
                            <div class="col-md-6">
                                <label for="{{ form.onizleme_resmi.id_for_label }}" class="form-label">{{ form.onizleme_resmi.label }}</label>
                                {{ form.onizleme_resmi }}
                                {% if form.onizleme_resmi.errors %}
                                <div class="text-danger">{{ form.onizleme_resmi.errors }}</div>
                                {% endif %}
                            </div>

                            <!-- Aktif -->
                            <div class="col-md-6">
                                <div class="form-check mt-4">
                                    {{ form.aktif }}
                                    <label for="{{ form.aktif.id_for_label }}" class="form-check-label">{{ form.aktif.label }}</label>
                                </div>
                                {% if form.aktif.errors %}
                                <div class="text-danger">{{ form.aktif.errors }}</div>
                                {% endif %}
                            </div>

                            <!-- Butonlar -->
                            <div class="col-12 mt-4">
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-circle me-2"></i>{% if video %}Güncelle{% else %}Kaydet{% endif %}
                                    </button>
                                    <a href="{% if video %}{% url 'nasilyapilir:video_detay' video.slug %}{% else %}{% url 'nasilyapilir:video_listesi' %}{% endif %}" class="btn btn-secondary">
                                        <i class="bi bi-x-circle me-2"></i>İptal
                                    </a>
                                    {% if video %}
                                    <a href="{% url 'nasilyapilir:video_sil' video.slug %}" class="btn btn-danger ms-auto">
                                        <i class="bi bi-trash me-2"></i>Sil
                                    </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
