from django.contrib import admin
from .models import Video, Kategori, Etiket

@admin.register(Kategori)
class KategoriAdmin(admin.ModelAdmin):
    list_display = ('ad', 'slug')
    prepopulated_fields = {'slug': ('ad',)}
    search_fields = ('ad',)

@admin.register(Etiket)
class EtiketAdmin(admin.ModelAdmin):
    list_display = ('ad', 'slug')
    prepopulated_fields = {'slug': ('ad',)}
    search_fields = ('ad',)

@admin.register(Video)
class VideoAdmin(admin.ModelAdmin):
    list_display = ('baslik', 'kategori', 'goruntulenme', 'begeni', 'tarih', 'aktif')
    list_filter = ('aktif', 'tarih', 'kategori', 'etiketler')
    search_fields = ('baslik', 'aciklama')
    prepopulated_fields = {'slug': ('baslik',)}
    date_hierarchy = 'tarih'
    filter_horizontal = ('etiketler',) 