# Bu dosya artık boş çünkü Profil modeli uyelik uygulamasına taşındı
# <PERSON><PERSON><PERSON> gerekli olan django.db.models importu bırakıldı
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.models import User
from django.core.validators import FileExtensionValidator, MaxValueValidator, MinValueValidator
import os
import uuid

def hakkimda_resim_yolu(instance, filename):
    """Profil resimlerini güvenli bir şekilde yüklemek için özel bir yol oluşturur."""
    # Dosya uzantısını al
    ext = filename.split('.')[-1]
    # Benzersiz bir dosya adı oluştur
    filename = f"{uuid.uuid4().hex}.{ext}"
    # Kullanıcı ID'sine göre klasörleme yap
    return os.path.join('hakkimda', str(instance.user.id), filename)

class Hakkimda(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='hakkimda')
    baslik = models.CharField(_("Başlık"), max_length=200)
    aciklama = models.TextField(_("Açıklama"))
    resim = models.ImageField(
        _("Profil Resmi"),
        upload_to=hakkimda_resim_yolu,
        null=True,
        blank=True,
        validators=[
            FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'gif', 'webp']),
        ]
    )
    sanat_baslangic = models.TextField(_("Seramik Sanatına Başlangıç"), blank=True, null=True)
    kullandigi_teknikler = models.TextField(_("Kullandığı Teknikler"), blank=True, null=True)
    atolye_bilgileri = models.TextField(_("Atölye Bilgileri"), blank=True, null=True)
    is_primary = models.BooleanField(_("Birincil Profil"), default=False, help_text=_("Bu profil, site genelinde görüntülenecek birincil profil olarak kullanılacak."))
    olusturma_tarihi = models.DateTimeField(_("Oluşturma Tarihi"), auto_now_add=True)
    guncelleme_tarihi = models.DateTimeField(_("Güncelleme Tarihi"), auto_now=True)

    class Meta:
        verbose_name = _("Hakkımda")
        verbose_name_plural = _("Hakkımda")

    def __str__(self):
        return self.baslik

    def save(self, *args, **kwargs):
        # Eğer bu kayıt birincil olarak işaretlendiyse, diğer tüm kayıtları birincil olmaktan çıkar
        if self.is_primary:
            Hakkimda.objects.filter(is_primary=True).update(is_primary=False)
        # Eğer hiç birincil kayıt yoksa ve bu yeni bir kayıtsa, bunu birincil yap
        elif not Hakkimda.objects.filter(is_primary=True).exists() and not self.pk:
            self.is_primary = True
        super().save(*args, **kwargs)

class Egitim(models.Model):
    hakkimda = models.ForeignKey(Hakkimda, on_delete=models.CASCADE, related_name='egitimler')
    okul = models.CharField(_("Okul"), max_length=200)
    bolum = models.CharField(_("Bölüm"), max_length=200)
    baslangic_tarihi = models.DateField(_("Başlangıç Tarihi"))
    bitis_tarihi = models.DateField(_("Bitiş Tarihi"), null=True, blank=True)
    aciklama = models.TextField(_("Açıklama"), blank=True)
    olusturma_tarihi = models.DateTimeField(_("Oluşturma Tarihi"), auto_now_add=True)
    guncelleme_tarihi = models.DateTimeField(_("Güncelleme Tarihi"), auto_now=True)

    class Meta:
        verbose_name = _("Eğitim")
        verbose_name_plural = _("Eğitimler")

    def __str__(self):
        return f"{self.okul} - {self.bolum}"

class IletisimBilgisi(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='iletisim_bilgileri')
    email = models.EmailField(_("E-posta"))
    telefon = models.CharField(_("Telefon"), max_length=20)
    adres = models.TextField(_("Adres"))
    calisma_saatleri = models.CharField(_("Çalışma Saatleri"), max_length=200, default="Pazartesi - Cumartesi: 10:00 - 18:00")
    is_primary = models.BooleanField(_("Birincil İletişim Bilgisi"), default=False, help_text=_("Bu iletişim bilgisi, site genelinde görüntülenecek birincil iletişim bilgisi olarak kullanılacak."))
    olusturma_tarihi = models.DateTimeField(_("Oluşturma Tarihi"), auto_now_add=True)
    guncelleme_tarihi = models.DateTimeField(_("Güncelleme Tarihi"), auto_now=True)

    class Meta:
        verbose_name = _("İletişim Bilgisi")
        verbose_name_plural = _("İletişim Bilgileri")

    def __str__(self):
        return f"{self.user.username} - İletişim Bilgileri"

    def save(self, *args, **kwargs):
        # Eğer bu kayıt birincil olarak işaretlendiyse, diğer tüm kayıtları birincil olmaktan çıkar
        if self.is_primary:
            IletisimBilgisi.objects.filter(is_primary=True).update(is_primary=False)
        # Eğer hiç birincil kayıt yoksa ve bu yeni bir kayıtsa, bunu birincil yap
        elif not IletisimBilgisi.objects.filter(is_primary=True).exists() and not self.pk:
            self.is_primary = True
        super().save(*args, **kwargs)

class SosyalMedya(models.Model):
    PLATFORM_CHOICES = [
        ('instagram', 'Instagram'),
        ('facebook', 'Facebook'),
        ('twitter', 'Twitter'),
        ('whatsapp', 'WhatsApp'),
        ('youtube', 'YouTube'),
        ('linkedin', 'LinkedIn'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sosyal_medya')
    platform = models.CharField(_("Platform"), max_length=20, choices=PLATFORM_CHOICES)
    url = models.URLField(_("URL"))
    aktif = models.BooleanField(_("Aktif"), default=True)
    is_primary_set = models.BooleanField(_("Birincil Sosyal Medya Seti"), default=False, help_text=_("Bu kullanıcının sosyal medya hesapları, site genelinde görüntülenecek birincil sosyal medya hesapları olarak kullanılacak."))
    olusturma_tarihi = models.DateTimeField(_("Oluşturma Tarihi"), auto_now_add=True)
    guncelleme_tarihi = models.DateTimeField(_("Güncelleme Tarihi"), auto_now=True)

    class Meta:
        verbose_name = _("Sosyal Medya")
        verbose_name_plural = _("Sosyal Medya")

    def save(self, *args, **kwargs):
        # Eğer bu kayıt birincil set olarak işaretlendiyse, diğer tüm kayıtları birincil olmaktan çıkar
        if self.is_primary_set:
            # Aynı kullanıcının diğer sosyal medya hesaplarını da birincil set olarak işaretle
            SosyalMedya.objects.filter(user=self.user).update(is_primary_set=True)
            # Diğer kullanıcıların sosyal medya hesaplarını birincil olmaktan çıkar
            SosyalMedya.objects.exclude(user=self.user).update(is_primary_set=False)
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.user.username} - {self.get_platform_display()}"
