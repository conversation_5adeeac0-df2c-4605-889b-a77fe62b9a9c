document.addEventListener('DOMContentLoaded', function() {
    console.log('Sayfa yüklendi');
    
    // WOW.js animasyonlarını başlat
    new WOW().init();
    
    // Bootstrap tooltips başlat
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });

    // Fotoğraf yükleme alanı için sürükle-bırak desteği
    setupPhotoUpload();
    
    // Form gönderiminde yükleniyor animasyonu
    setupFormSubmission();
    
    // Fotoğraf silme onayı
    setupPhotoDeleteConfirmation();
    
    // Dosya yükleme alanına tıklama olayı
    const fileUploadArea = document.getElementById('fileUploadArea');
    const fileInput = document.getElementById('id_fotograflar');
    
    if (fileUploadArea && fileInput) {
        console.log('Dosya yükleme alanı ve input bulundu');
        
        fileUploadArea.addEventListener('click', function(e) {
            console.log('Dosya yükleme alanına tıklandı');
            fileInput.click();
        });
    }
});

/**
 * Fotoğraf yükleme alanı için sürükle-bırak desteği
 */
function setupPhotoUpload() {
    const fileInput = document.querySelector('.file-upload-wrapper input[type="file"]');
    if (!fileInput) return;
    
    // Dosya seçildiğinde önizleme göster
    fileInput.addEventListener('change', function(e) {
        console.log('Dosya seçildi:', this.files);
        showPhotoPreview(this.files);
    });
    
    // Sürükle-bırak desteği
    const dropArea = document.querySelector('.file-upload-wrapper');
    if (!dropArea) return;
    
    console.log('Dosya yükleme alanı bulundu:', dropArea);
    
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, preventDefaults, false);
    });
    
    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }
    
    ['dragenter', 'dragover'].forEach(eventName => {
        dropArea.addEventListener(eventName, highlight, false);
    });
    
    ['dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, unhighlight, false);
    });
    
    function highlight() {
        dropArea.classList.add('highlight');
    }
    
    function unhighlight() {
        dropArea.classList.remove('highlight');
    }
    
    dropArea.addEventListener('drop', handleDrop, false);
    
    function handleDrop(e) {
        console.log('Dosya bırakıldı');
        const dt = e.dataTransfer;
        const files = dt.files;
        fileInput.files = files;
        showPhotoPreview(files);
    }
}

/**
 * Seçilen fotoğrafların önizlemesini göster
 */
function showPhotoPreview(files) {
    if (!files || files.length === 0) return;
    
    // Önizleme alanı oluştur veya var olanı temizle
    let previewContainer = document.querySelector('.photo-preview-container');
    if (!previewContainer) {
        previewContainer = document.createElement('div');
        previewContainer.className = 'photo-preview-container mt-3';
        const fileInput = document.querySelector('input[type="file"]');
        fileInput.parentElement.appendChild(previewContainer);
    } else {
        previewContainer.innerHTML = '';
    }
    
    // Önizleme başlığı
    const previewTitle = document.createElement('h6');
    previewTitle.className = 'mb-2';
    previewTitle.innerHTML = '<i class="bi bi-images me-2"></i>Yüklenecek Fotoğraflar';
    previewContainer.appendChild(previewTitle);
    
    // Önizleme grid
    const previewGrid = document.createElement('div');
    previewGrid.className = 'row g-2';
    previewContainer.appendChild(previewGrid);
    
    // Her dosya için önizleme oluştur
    Array.from(files).forEach(file => {
        // Sadece resim dosyalarını kabul et
        if (!file.type.match('image.*')) return;
        
        const col = document.createElement('div');
        col.className = 'col-md-3 col-sm-4 col-6';
        
        const card = document.createElement('div');
        card.className = 'card h-100 photo-preview-item';
        
        const img = document.createElement('img');
        img.className = 'card-img-top';
        img.alt = file.name;
        
        // Dosya boyutu bilgisi
        const cardBody = document.createElement('div');
        cardBody.className = 'card-body p-2';
        
        const fileName = document.createElement('p');
        fileName.className = 'card-text small text-truncate mb-1';
        fileName.textContent = file.name;
        
        const fileSize = document.createElement('p');
        fileSize.className = 'card-text small text-muted';
        fileSize.textContent = formatFileSize(file.size);
        
        cardBody.appendChild(fileName);
        cardBody.appendChild(fileSize);
        
        card.appendChild(img);
        card.appendChild(cardBody);
        col.appendChild(card);
        previewGrid.appendChild(col);
        
        // Resmi yükle
        const reader = new FileReader();
        reader.onload = function(e) {
            img.src = e.target.result;
        };
        reader.readAsDataURL(file);
    });
}

/**
 * Dosya boyutunu formatla
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Form gönderiminde yükleniyor animasyonu
 */
function setupFormSubmission() {
    const form = document.querySelector('.calisma-form');
    if (!form) return;
    
    form.addEventListener('submit', function(e) {
        const submitBtn = this.querySelector('button[type="submit"]');
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Kaydediliyor...';
        submitBtn.disabled = true;
        
        // Diğer butonları da devre dışı bırak
        const otherBtns = this.querySelectorAll('a.btn');
        otherBtns.forEach(btn => {
            btn.classList.add('disabled');
        });
    });
}

/**
 * Fotoğraf silme onayı
 */
function setupPhotoDeleteConfirmation() {
    const checkboxes = document.querySelectorAll('input[type="checkbox"][name^="sil_fotograf_"]');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const photoItem = this.closest('.photo-item');
            if (this.checked) {
                photoItem.classList.add('photo-to-delete');
            } else {
                photoItem.classList.remove('photo-to-delete');
            }
        });
    });
}
