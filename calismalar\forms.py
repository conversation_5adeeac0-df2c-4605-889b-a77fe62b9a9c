from django import forms
from django.core.exceptions import ValidationError
from django.conf import settings
import os
from .models import Calisma, CalismaFotograf, Kategori, Etiket

def validate_file_size(value):
    """Dosya boyutunu kontrol eder"""
    max_size = settings.MAX_UPLOAD_SIZE
    if value.size > max_size:
        max_size_mb = max_size / (1024 * 1024)
        raise ValidationError(f'Dosya boyutu {int(max_size_mb)}MB\'dan büyük olamaz.')

def validate_file_type(value):
    """Dosya türünü kontrol eder (dosya uzantısı kontrolü)"""
    # Dosya uzantısını kontrol et
    ext = os.path.splitext(value.name)[1].lower()
    allowed_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']

    if ext not in allowed_extensions:
        raise ValidationError(f'Geç<PERSON><PERSON> dosya uzantısı. İzin verilen uzantılar: {", ".join(allowed_extensions)}')



class CalismaForm(forms.ModelForm):
    """Çalışma oluşturma ve düzenleme formu."""
    class Meta:
        model = Calisma
        fields = ['baslik', 'aciklama', 'kategori', 'etiketler']
        widgets = {
            'baslik': forms.TextInput(attrs={'class': 'form-control'}),
            'aciklama': forms.Textarea(attrs={'class': 'form-control', 'rows': 5}),
            'kategori': forms.Select(attrs={'class': 'form-control'}),
            'etiketler': forms.SelectMultiple(attrs={'class': 'form-control'}),
        }

    def clean_baslik(self):
        """Başlık temizleme ve XSS koruması."""
        baslik = self.cleaned_data['baslik']
        # Basit XSS koruması
        baslik = baslik.replace('<', '&lt;').replace('>', '&gt;')
        return baslik

class CalismaFotografForm(forms.ModelForm):
    """Çalışma fotoğrafı ekleme formu."""
    class Meta:
        model = CalismaFotograf
        fields = ['fotograf', 'aciklama']
        widgets = {
            'fotograf': forms.FileInput(attrs={'class': 'form-control'}),
            'aciklama': forms.TextInput(attrs={'class': 'form-control'}),
        }

    def clean_fotograf(self):
        """Fotoğraf validasyonu."""
        fotograf = self.cleaned_data.get('fotograf')
        if fotograf and not isinstance(fotograf, str):
            # Dosya boyutu kontrolü
            validate_file_size(fotograf)

            try:
                # Dosya türü ve içerik kontrolü
                validate_file_type(fotograf)
            except Exception as e:
                # Hata durumunda kullanıcıya bilgi ver
                raise ValidationError(f"Dosya doğrulama hatası: {str(e)}")

        return fotograf

class KategoriForm(forms.ModelForm):
    """Kategori oluşturma ve düzenleme formu."""
    class Meta:
        model = Kategori
        fields = ['ad', 'aciklama']
        widgets = {
            'ad': forms.TextInput(attrs={'class': 'form-control'}),
            'aciklama': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }

class EtiketForm(forms.ModelForm):
    """Etiket oluşturma ve düzenleme formu."""
    class Meta:
        model = Etiket
        fields = ['ad']
        widgets = {
            'ad': forms.TextInput(attrs={'class': 'form-control'}),
        }
