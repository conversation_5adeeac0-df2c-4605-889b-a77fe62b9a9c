from django.apps import AppConfig


class CalismalarConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'calismalar'

    def ready(self):
        """
        Uygulama başlatıldığında çalışacak kod.
        İzinleri ve grupları otomatik olarak kurar.
        """
        # İçe aktarmaları burada yapıyoruz çünkü Django uygulaması tam olarak yüklenmeden önce
        # model içe aktarmaları sorunlara neden olabilir
        import django.db.models.signals
        from django.db.models.signals import post_migrate
        from django.dispatch import receiver

        @receiver(post_migrate)
        def create_permissions(sender, **kwargs):
            """
            Migrasyon sonrası izinleri ve grupları oluşturur.
            """
            if sender.name == self.name:
                from .permissions import setup_calisma_permissions
                setup_calisma_permissions()
