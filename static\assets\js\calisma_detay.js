document.addEventListener('DOMContentLoaded', function() {
    // WOW.js animasyonlarını başlat
    new WOW().init();
    
    // Swiper slider başlat
    const calismaSlider = new Swiper('.calisma-slider', {
        slidesPerView: 1,
        spaceBetween: 30,
        loop: true,
        autoplay: {
            delay: 5000,
            disableOnInteraction: false,
        },
        pagination: {
            el: '.swiper-pagination',
            clickable: true,
        },
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        },
    });
    
    // Fotoğraf galerisi lightbox 
    const lightbox = GLightbox({
        selector: '.image-link',
        touchNavigation: true,
        loop: true,
        autoplayVideos: true
    });
    
    // Görselleri lazy loading ile yükle
    const lazyImages = document.querySelectorAll('img[data-src]');
    
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const image = entry.target;
                    image.src = image.dataset.src;
                    image.classList.add('loaded');
                    imageObserver.unobserve(image);
                }
            });
        });
        
        lazyImages.forEach(image => {
            imageObserver.observe(image);
        });
    } else {
        // Fallback for browsers without IntersectionObserver support
        lazyImages.forEach(image => {
            image.src = image.dataset.src;
        });
    }
    
    // Beğeni işlevselliği için AJAX
    const likeButton = document.querySelector('.like-button');
    if (likeButton) {
        likeButton.addEventListener('click', function(e) {
            e.preventDefault();
            
            const url = this.getAttribute('href');
            const likeCount = document.querySelector('.like-count');
            
            fetch(url, {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCsrfToken()
                },
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Beğeni sayısını güncelle
                    if (likeCount) {
                        likeCount.textContent = data.like_count;
                    }
                    
                    // Buton stilini güncelle
                    this.classList.toggle('liked', data.liked);
                    
                    // İkon değiştir
                    const icon = this.querySelector('i');
                    if (icon) {
                        icon.className = data.liked ? 'bi bi-heart-fill me-2' : 'bi bi-heart me-2';
                    }
                    
                    // Buton metnini güncelle
                    const buttonText = this.querySelector('span');
                    if (buttonText) {
                        buttonText.textContent = data.liked ? 'Beğenildi' : 'Beğen';
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
        });
    }
    
    // CSRF Token'ı alma fonksiyonu
    function getCsrfToken() {
        return document.querySelector('input[name="csrfmiddlewaretoken"]').value;
    }
});
