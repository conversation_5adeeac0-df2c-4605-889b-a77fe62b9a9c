/**
 * <PERSON><PERSON>p Cadısı Inline Editing Script
 * Bu script, admin kullanıc<PERSON>ın Hakkımda ve İletişim sayfalarındaki içeriği
 * aynı sayfa üzerinden düzenleyebilmesini sağlar.
 * 
 * Güncelleme: Tek bir düzenleme butonu ve modal pencere yaklaşımı eklendi.
 */

// Global CSRF token değişkeni
let csrfToken;

document.addEventListener('DOMContentLoaded', function() {
    // Admin kullanıcısı olup olmadığını kontrol et
    const isAdmin = document.body.classList.contains('user-is-admin');
    
    if (!isAdmin) return;
    
    // CSRF token'ı al
    csrfToken = document.querySelector('input[name="csrfmiddlewaretoken"]').value;
    
    // Konsola bilgi yazdır (hata ayıklama için)
    console.log('Düzenleme modu aktif, admin kullanıcısı tespit edildi');
    
    // Sayfa yüklenme durumunu kontrol et
    setTimeout(() => {
        // Sayfayı kontrol et
        if (document.querySelector('.hakkimda-section')) {
            console.log('Hakkımda sayfası tespit edildi');
        } else if (document.querySelector('.iletisim-section')) {
            console.log('İletişim sayfası tespit edildi');
            // Sosyal medya grubu için özel işlem
            initializeSocialMediaGroup();
        }
    }, 500); // Sayfa tam olarak yüklensin diye küçük bir gecikme ekledik
    
    /**
     * Sosyal medya grubu için özel işlem
     */
    function initializeSocialMediaGroup() {
        const socialMediaGroup = document.querySelector('.social-links-container');
        if (!socialMediaGroup) return;
        
        // Düzenleme ikonu zaten varsa işlem yapma
        if (socialMediaGroup.querySelector('.edit-icon')) return;
        
        // Düzenleme simgesi ekle
        const editIcon = document.createElement('span');
        editIcon.className = 'edit-icon';
        editIcon.innerHTML = '<i class="bi bi-pencil-square"></i>';
        editIcon.title = 'Sosyal medya bağlantılarını düzenlemek için tıklayın';
        
        socialMediaGroup.appendChild(editIcon);
        
        // Düzenleme modunu etkinleştir
        editIcon.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            showSocialMediaModal(socialMediaGroup);
        });
    }
    
    /**
     * Düzenleme modunu etkinleştir - HTML onclick olayı için global fonksiyon
     */
    window.makeEditable = function(element) {
        const contentType = element.dataset.contentType;
        const contentId = element.dataset.contentId;
        const contentElement = element.querySelector('.content-text');
        const originalHtml = contentElement.innerHTML;
        let originalContent = contentElement.innerText.trim();
        
        // Düzenleme durumundaysa çık
        if (element.classList.contains('editing')) return;
        
        console.log('Düzenleme başlatılıyor:', contentType, contentId);
        
        // Düzenleme modunu etkinleştir
        element.classList.add('editing');
        
        // Resim içeriyorsa özel işlem yap
        if (contentType === 'hakkimda_resim') {
            // Şu an için resim düzenleme devre dışı
            alert('Resim düzenleme şu an desteklenmiyor. Lütfen admin panelini kullanın.');
            element.classList.remove('editing');
            return;
        } 
        // Uzun metinler için textarea kullan
        else if (contentType === 'hakkimda_aciklama' || contentType === 'atolye_aciklama') {
            contentElement.innerHTML = `<textarea class="form-control edit-textarea">${originalContent}</textarea>`;
        } 
        // Diğer tüm alanlar için normal input kullan
        else {
            contentElement.innerHTML = `<input type="text" class="form-control edit-input" value="${originalContent}">`;
        }
        
        // Düzenleme işlevlerini ekle
        let editInput = contentElement.querySelector('.edit-input');
        let editTextarea = contentElement.querySelector('.edit-textarea');
        let inputElement = editInput || editTextarea;
        
        if (inputElement) {
            inputElement.focus();
            
            // Kaydet ve İptal butonlarını ekle
            const actionButtons = document.createElement('div');
            actionButtons.className = 'edit-actions';
            actionButtons.innerHTML = `
                <button class="btn btn-sm btn-success save-btn" onclick="saveEditedContent(this.closest('.editable-content'))"><i class="bi bi-check-lg"></i></button>
                <button class="btn btn-sm btn-danger cancel-btn" onclick="cancelEditingContent(this.closest('.editable-content'))"><i class="bi bi-x-lg"></i></button>
            `;
            
            element.appendChild(actionButtons);
            
            // Enter tuşu ile kaydetme
            inputElement.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    saveEditedContent(element);
                }
                
                // Escape tuşu ile iptal
                if (e.key === 'Escape') {
                    cancelEditingContent(element);
                }
            });
            
            // Orijinal içeriği ve HTML'i sakla
            element.dataset.originalContent = originalContent;
            element.dataset.originalHtml = originalHtml;
        }
    };
    
    /**
     * Düzenlemeyi iptal et - Global fonksiyon
     */
    window.cancelEditingContent = function(element) {
        const contentElement = element.querySelector('.content-text');
        const originalHtml = element.dataset.originalHtml;
        
        contentElement.innerHTML = originalHtml;
        
        // Düzenleme modunu kapat
        element.classList.remove('editing');
        
        // Eklenen butonları kaldır
        const actionButtons = element.querySelector('.edit-actions');
        if (actionButtons) {
            actionButtons.remove();
        }
    };
    
    /**
     * Düzenlenen içeriği kaydet - Global fonksiyon
     */
    window.saveEditedContent = function(element) {
        const contentType = element.dataset.contentType;
        const contentId = element.dataset.contentId;
        const contentElement = element.querySelector('.content-text');
        let newContent;
        
        // Form elemanının tipine göre değeri al
        if (contentElement.querySelector('textarea')) {
            newContent = contentElement.querySelector('textarea').value;
        } else if (contentElement.querySelector('input')) {
            newContent = contentElement.querySelector('input').value;
        } else {
            newContent = contentElement.innerHTML;
        }
        
        console.log('Kaydedilen içerik:', contentType, contentId, newContent);
        
        // AJAX ile içeriği güncelle
        let url = '';
        let data = {
            'content_type': contentType,
            'content_id': contentId,
            'content': newContent,
            'csrfmiddlewaretoken': csrfToken
        };
        
        // Sayfa türüne göre doğru URL'i belirle
        if (document.querySelector('.hakkimda-section')) {
            url = '/hakkimda/inline-update/';
        } else if (document.querySelector('.iletisim-section')) {
            url = '/iletisim/inline-update/';
        }
        
        // Yükleniyor göstergesi
        element.classList.add('loading');
        
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: new URLSearchParams(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Görünümü güncelle
                if (contentType === 'sosyal_medya_url') {
                    // Sosyal medya için ikon göster
                    const icon = contentElement.querySelector('i') ? contentElement.querySelector('i').outerHTML : element.dataset.originalHtml;
                    contentElement.innerHTML = icon;
                } else {
                    contentElement.innerHTML = newContent;
                }
                
                // Düzenleme modunu kapat
                element.classList.remove('editing');
                element.classList.remove('loading');
                const actionButtons = element.querySelector('.edit-actions');
                if (actionButtons) {
                    element.removeChild(actionButtons);
                }
                
                // Başarı mesajı göster
                showNotification('success', 'İçerik başarıyla güncellendi');
            } else {
                // Hata durumu
                showNotification('error', data.message || 'Bir hata oluştu!');
                element.classList.remove('loading');
            }
        })
        .catch(error => {
            console.error('Hata:', error);
            showNotification('error', 'Bir hata oluştu!');
            element.classList.remove('loading');
        });
    };
    
    /**
     * İçeriği sunucuya gönder ve güncelle
     */
    function saveContent(contentType, contentId, newContent, callback) {
        console.log('Kaydediliyor:', contentType, contentId);
        
        // Sayfa türüne göre doğru URL'i belirle
        let url = '';
        if (document.querySelector('.hakkimda-section')) {
            url = '/hakkimda/inline-update/';
        } else if (document.querySelector('.iletisim-section')) {
            url = '/iletisim/inline-update/';
        }
        
        // AJAX ile içeriği güncelle
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: new URLSearchParams({
                'content_type': contentType,
                'content_id': contentId,
                'content': newContent,
                'csrfmiddlewaretoken': csrfToken
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('Başarıyla güncellendi:', contentType, contentId);
                // Sayfa içeriğini güncelle
                updatePageContent(contentType, contentId, newContent);
                if (callback) callback(true, 'Başarıyla güncellendi');
            } else {
                console.error('Güncelleme hatası:', data.error);
                if (callback) callback(false, data.error || 'Güncelleme sırasında bir hata oluştu');
            }
        })
        .catch(error => {
            console.error('AJAX hatası:', error);
            if (callback) callback(false, 'Sunucu ile iletişim sırasında bir hata oluştu');
        });
    }
    
    /**
     * Sosyal medya    /**
     * Bildirim göster
     */
    function showNotification(type = 'info', message) {
        // Mevcut bildirimleri kaldır
        const existingNotifications = document.querySelectorAll('.notification');
        existingNotifications.forEach(notification => {
            notification.remove();
        });
        
        // Yeni bildirim oluştur
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="bi ${type === 'success' ? 'bi-check-circle' : type === 'error' ? 'bi-x-circle' : 'bi-info-circle'}"></i>
                <span>${message}</span>
                    <div class="form-group mb-3">
                        <label>LinkedIn URL</label>
                        <input type="text" class="form-control" id="linkedin-url" placeholder="https://linkedin.com/in/...">
                    </div>
                </div>
                <div class="edit-modal-footer">
                    <button type="button" class="btn btn-secondary cancel-modal">Vazgeç</button>
                    <button type="button" class="btn btn-primary save-modal">Kaydet</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Modalı göster
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);
        
        // Kapat düğmesi
        modal.querySelector('.close-modal').addEventListener('click', () => {
            closeModal(modal);
        });
        
        // İptal düğmesi
        modal.querySelector('.cancel-modal').addEventListener('click', () => {
            closeModal(modal);
        });
        
        // Kaydet düğmesi
        modal.querySelector('.save-modal').addEventListener('click', () => {
            // Değerleri al
            const instagramUrl = modal.querySelector('#instagram-url').value;
            const facebookUrl = modal.querySelector('#facebook-url').value;
            const youtubeUrl = modal.querySelector('#youtube-url').value;
            const linkedinUrl = modal.querySelector('#linkedin-url').value;
            
            // Her URL için ayrı ayrı güncelleme yap
            saveSocialMediaUrl('instagram', instagramUrl);
            saveSocialMediaUrl('facebook', facebookUrl);
            saveSocialMediaUrl('youtube', youtubeUrl);
            saveSocialMediaUrl('linkedin', linkedinUrl);
            
            // Başarı mesajı göster
            showNotification('Sosyal medya bağlantıları güncellendi', 'success');
            
            // Modalı kapat
            closeModal(modal);
        });
    }
    
    /**
     * Modalı kapat
     */
    function closeModal(modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(modal);
        }, 300);
    }
    
    /**
     * Hakkımda sayfasındaki düzenleme ikonlarını manuel olarak ekle
     */
    function addEditIconsToHakkimda() {
        // Tüm düzenlenebilir içerikleri bul
        document.querySelectorAll('.editable-content').forEach(el => {
            // Zaten ikonu varsa ekleme
            if (el.querySelector('.edit-icon')) return;
            
            // Düzenleme ikonu oluştur
            const editIcon = document.createElement('span');
            editIcon.className = 'edit-icon';
            editIcon.innerHTML = '<i class="bi bi-pencil-square"></i>';
            editIcon.title = 'Düzenlemek için tıklayın';
            
            // İkonu elemana ekle
            el.appendChild(editIcon);
            
            // Stil ekle
            el.style.position = 'relative';
            el.style.border = '1px dashed transparent';
            
            // Hover durumunda görünürlük
            el.addEventListener('mouseenter', function() {
                this.style.backgroundColor = 'rgba(235, 239, 243, 0.5)';
                this.style.borderColor = 'rgba(115, 68, 41, 0.3)';
                editIcon.style.opacity = '1';
            });
            
            el.addEventListener('mouseleave', function() {
                this.style.backgroundColor = 'transparent';
                this.style.borderColor = 'transparent';
                editIcon.style.opacity = '0.8';
            });
            
            // Düzenleme modunu etkinleştir
            editIcon.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // Sosyal medya grubu için özel işlem
                if (el.dataset.contentType === 'sosyal_medya_group') {
                    showSocialMediaModal(el);
                } else {
                    makeEditable(el);
                }
            });
        });
        
        console.log('Hakkımda sayfası düzenleme ikonları eklendi');
    }
    
    /**
     * Sosyal medya URL'sini kaydet
     */
    function saveSocialMediaUrl(platform, url) {
        // Platform indeksini belirle
        const platformIndex = {
            'instagram': 0,
            'facebook': 1,
            'youtube': 2,
            'linkedin': 3
        }[platform];
        
        // URL'yi güncelle
        fetch('/anasayfa/iletisim/inline-update/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken
            },
            body: JSON.stringify({
                content_type: 'sosyal_medya_url',
                content_id: platformIndex,
                content: url
            })
        })
        .then(response => response.json())
        .then(data => {
            console.log('Sosyal medya güncellemesi:', data);
        })
        .catch(error => {
            console.error('Sosyal medya güncelleme hatası:', error);
        });
    }
});
