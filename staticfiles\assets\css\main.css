/**
 * <PERSON><PERSON><PERSON> - Ana CSS Dosyası
 * Temel: <PERSON> Template
 * Son güncelleme: 07.05.2025
 */

/*--------------------------------------------------------------
# Yazı Tipleri ve Renk Değişkenleri
--------------------------------------------------------------*/
/* Yazı Tipleri */
:root {
  --default-font: "Roboto", system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --heading-font: "<PERSON>lew<PERSON>", sans-serif;
  --nav-font: "Poppins", sans-serif;
}

/* Global Renkler - Tüm site genelinde kullanılan ana renkler */
:root { 
  /* Yeni Renk Paleti */
  --color-gray: #BABDBF; /* Seyahat-1 - Açık gri */
  --color-teal: #3F7373; /* Seyahat-2 - Turkuaz/Petrol */
  --color-orange: #BF754B; /* Seyahat-3 - Turuncu/Toprak */
  --color-rust: #A64826; /* Seyahat-4 - Pas/Kiremit */
  --color-brown: #732B1A; /* Seyahat-5 - Kahverengi */
  
  /* Fonksiyonel Renkler */
  --primary-color: var(--color-teal); /* Ana renk - Turkuaz */
  --secondary-color: var(--color-brown); /* İkincil renk - Kahverengi */
  --accent-color: var(--color-orange); /* Vurgu rengi - Turuncu */
  --light-accent: var(--color-gray); /* Hafif vurgu - Gri */
  --highlight-color: var(--color-rust); /* Belirginleştirme - Pas */
  
  /* Göz Dostu Renkler */
  --soft-white: #f8f7f4; /* Saf beyaz yerine krem tonu - sayfa arkaplanı */
  --soft-light: #f0ece6; /* Daha yumuşak krem tonu - kart arkaplanı */
  --off-white: #f4f1eb; /* Hafif krem tonu */
  --light-gray: #e6e2dc; /* Açık gri */
  --medium-gray: #d3cdc5; /* Orta gri ton */
  --text-soft: #5c5752; /* Siyah yerine koyu gri - ana metin */
  --text-muted: #8c847c; /* Orta koyu gri - ikincil metin */
  --white: #ffffff; /* Saf beyaz (sınırlı kullanım için) */
  
  /* RGB Değerleri (CSS filtreler, gölgeler vb. için) */
  --color-gray-rgb: 186, 189, 191; /* Seyahat-1 RGB */
  --color-teal-rgb: 63, 115, 115; /* Seyahat-2 RGB */
  --color-orange-rgb: 191, 117, 75; /* Seyahat-3 RGB */
  --color-rust-rgb: 166, 72, 38; /* Seyahat-4 RGB */
  --color-brown-rgb: 115, 43, 26; /* Seyahat-5 RGB */
  
  --primary-color-rgb: var(--color-teal-rgb); /* Ana renk RGB */
  --secondary-color-rgb: var(--color-brown-rgb); /* İkincil renk RGB */
  --accent-color-rgb: var(--color-orange-rgb); /* Vurgu rengi RGB */
  
  /* Fonksiyonel Renkler */
  --background-color: var(--soft-white); /* Arka plan rengi */
  --default-color: var(--text-soft); /* Varsayılan metin rengi */
  --heading-color: var(--primary-color); /* Başlık rengi */
  --surface-color: var(--soft-light); /* Kutu arka plan rengi */
  --contrast-color: var(--soft-white); /* Kontrast metin rengi */
}

/* Nav Menu Colors - The following color variables are used specifically for the navigation menu. They are separate from the global colors to allow for more customization options */
:root {
  --nav-color: #402401;  /* The default color of the main navmenu links */
  --nav-hover-color: #A68444; /* Applied to main navmenu links when they are hovered over or active */
  --nav-mobile-background-color: #ffffff; /* Used as the background color for mobile navigation menu */
  --nav-dropdown-background-color: #ffffff; /* Used as the background color for dropdown items that appear when hovering over primary navigation items */
  --nav-dropdown-color: #402401; /* Used for navigation links of the dropdown items in the navigation menu. */
  --nav-dropdown-hover-color: #A68444; /* Similar to --nav-hover-color, this color is applied to dropdown navigation links when they are hovered over. */
}

/* Color Presets - These classes override global colors when applied to any section or element, providing reuse of the sam color scheme. */

.light-background {
  --background-color: #d4c9b8;
  --surface-color: #D9B391;
}

.dark-background {
  --background-color: #060606;
  --default-color: #ffffff;
  --heading-color: #ffffff;
  --surface-color: #252525;
  --contrast-color: #ffffff;
}

/* Smooth scroll */
:root {
  scroll-behavior: smooth;
}

/*--------------------------------------------------------------
# General Styling & Shared Classes
--------------------------------------------------------------*/
body {
  color: var(--default-color);
  background-color: var(--background-color);
  font-family: var(--default-font);
}

a {
  color: var(--accent-color);
  text-decoration: none;
  transition: 0.3s;
}

a:hover {
  color: color-mix(in srgb, var(--accent-color), transparent 25%);
  text-decoration: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--heading-color);
  font-family: var(--heading-font);
}

/* PHP Email Form Messages
------------------------------*/
.php-email-form .error-message {
  display: none;
  background: #df1529;
  color: #ffffff;
  text-align: left;
  padding: 15px;
  margin-bottom: 24px;
  font-weight: 600;
}

.php-email-form .sent-message {
  display: none;
  color: #ffffff;
  background: #059652;
  text-align: center;
  padding: 15px;
  margin-bottom: 24px;
  font-weight: 600;
}

.php-email-form .loading {
  display: none;
  background: var(--surface-color);
  text-align: center;
  padding: 15px;
  margin-bottom: 24px;
}

.php-email-form .loading:before {
  content: "";
  display: inline-block;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  margin: 0 10px -6px 0;
  border: 3px solid var(--accent-color);
  border-top-color: var(--surface-color);
  animation: php-email-form-loading 1s linear infinite;
}

@keyframes php-email-form-loading {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/*--------------------------------------------------------------
# Global Header
--------------------------------------------------------------*/
.header {
  color: var(--default-color);
  background-color: var(--background-color);
  padding: 15px 0;
  transition: all 0.5s;
  z-index: 997;
  box-shadow: 0px 0 18px rgba(0, 0, 0, 0.1);
}

.header .logo {
  line-height: 1;
}

.header .logo img {
  max-height: 36px;
  margin-right: 8px;
}

.header .logo h1 {
  font-size: 32px;
  margin: 0;
  font-weight: 300;
  text-transform: uppercase;
  color: var(--heading-color);
}

.header .header-social-links {
  padding-right: 15px;
}

.header .header-social-links a {
  color: color-mix(in srgb, var(--default-color), transparent 40%);
  padding-left: 6px;
  display: inline-block;
  transition: 0.3s;
  font-size: 16px;
}

.header .header-social-links a:hover {
  color: var(--accent-color);
}

.header .header-social-links a i {
  line-height: 0px;
}

/*--------------------------------------------------------------
# Auth Buttons (Eklenecek Kod)
--------------------------------------------------------------*/
.header .auth-buttons {
  padding-right: 15px;
  display: flex;
  gap: 10px;
  align-items: center; /* Butonları dikeyde ortalamak için */
}

.header .auth-buttons .btn-auth {
  display: inline-flex;
  align-items: center;
  padding: 8px 15px;
  border-radius: 30px;
  font-size: 14px;
  font-weight: 500;
  color: var(--heading-color);
  background-color: rgba(255, 255, 255, 0.2); /* Şeffaf arka plan */
  /* border: 1px solid rgba(166, 132, 68, 0.3); */ /* Kenarlık kaldırıldı */
  border: none; /* Kenarlığı tamamen kaldırır */
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); /* Gölge efekti */
  font-family: var(--nav-font);
}

.header .auth-buttons .btn-auth i {
  margin-right: 5px;
  font-size: 16px;
}

.header .auth-buttons .btn-auth:hover {
  background-color: rgba(166, 132, 68, 0.1); /* Hover'da hafif renk */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15); /* Hover'da artan gölge */
  transform: translateY(-2px); /* Hover'da hafif yukarı hareket */
  color: var(--accent-color);
}

.header .auth-buttons .btn-profile {
    background-color: var(--accent-color);
    color: var(--contrast-color);
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.header .auth-buttons .btn-profile:hover {
    background-color: var(--heading-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.header .auth-buttons .btn-logout {
    background-color: transparent;
    color: var(--default-color);
    border: 1px solid var(--accent-color);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.header .auth-buttons .btn-logout:hover {
    background-color: var(--accent-color);
    color: var(--contrast-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

@media (max-width: 1200px) {
  .header .logo {
    order: 1;
  }

  /* .header .header-social-links { order: 2; } // Bu satırı kaldırabilir veya yorumda bırakabilirsiniz */
  .header .auth-buttons {
    order: 2; /* Logo ve menü arasındaki sıralamayı ayarlar */
  }

  .header .navmenu {
    order: 3;
  }
}

@media (max-width: 768px) {
  .header .auth-buttons {
    gap: 5px; /* Mobil görünümde butonlar arası boşluk */
  }

  .header .auth-buttons .btn-auth {
    padding: 6px 10px; /* Mobil görünümde buton dolgusu */
    font-size: 12px; /* Mobil görünümde yazı boyutu */
  }

  .header .auth-buttons .btn-auth i {
    font-size: 14px; /* Mobil görünümde ikon boyutu */
  }

  .header .auth-buttons .btn-profile,
  .header .auth-buttons .btn-logout {
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
  }
}

/*--------------------------------------------------------------
# Navigation Menu
--------------------------------------------------------------*/
/* Desktop Navigation */
@media (min-width: 1200px) {
  .navmenu {
    padding: 0;
  }

  .navmenu ul {
    margin: 0;
    padding: 0;
    display: flex;
    list-style: none;
    align-items: center;
  }

  .navmenu li {
    position: relative;
  }

  .navmenu>ul>li {
    white-space: nowrap;
    padding: 15px 14px;
  }

  .navmenu>ul>li:last-child {
    padding-right: 0;
  }

  .navmenu a,
  .navmenu a:focus {
    color: var(--nav-color);
    font-size: 15px;
    padding: 0 2px;
    font-family: var(--nav-font);
    font-weight: 400;
    display: flex;
    align-items: center;
    justify-content: space-between;
    white-space: nowrap;
    transition: 0.3s;
    position: relative;
  }

  .navmenu a i,
  .navmenu a:focus i {
    font-size: 12px;
    line-height: 0;
    margin-left: 5px;
    transition: 0.3s;
  }

  .navmenu>ul>li>a:before {
    content: "";
    position: absolute;
    height: 2px;
    bottom: -6px;
    left: 0;
    background-color: var(--nav-hover-color);
    visibility: hidden;
    width: 0px;
    transition: all 0.3s ease-in-out 0s;
  }

  .navmenu a:hover:before,
  .navmenu li:hover>a:before,
  .navmenu .active:before {
    visibility: visible;
    width: 25px;
  }

  .navmenu li:hover>a,
  .navmenu .active,
  .navmenu .active:focus {
    color: var(--nav-hover-color);
  }

  .navmenu .dropdown ul {
    margin: 0;
    padding: 10px 0;
    background: var(--nav-dropdown-background-color);
    display: block;
    position: absolute;
    visibility: hidden;
    left: 14px;
    top: 130%;
    opacity: 0;
    transition: 0.3s;
    border-radius: 4px;
    z-index: 99;
    box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.1);
  }

  .navmenu .dropdown ul li {
    min-width: 200px;
  }

  .navmenu .dropdown ul a {
    padding: 10px 20px;
    font-size: 15px;
    text-transform: none;
    color: var(--nav-dropdown-color);
  }

  .navmenu .dropdown ul a.sitename {
    text-transform: none !important;
  }

  .navmenu .dropdown ul a i {
    font-size: 12px;
  }

  .navmenu .dropdown ul a:hover,
  .navmenu .dropdown ul .active:hover,
  .navmenu .dropdown ul li:hover>a {
    color: var(--nav-dropdown-hover-color);
  }

  .navmenu .dropdown:hover>ul {
    opacity: 1;
    top: 100%;
    visibility: visible;
  }

  .navmenu .dropdown .dropdown ul {
    top: 0;
    left: -90%;
    visibility: hidden;
  }

  .navmenu .dropdown .dropdown:hover>ul {
    opacity: 1;
    top: 0;
    left: -100%;
    visibility: visible;
  }
}

/* Mobile Navigation */
@media (max-width: 1199px) {
  .mobile-nav-toggle {
    color: var(--nav-color);
    font-size: 28px;
    line-height: 0;
    margin-right: 10px;
    cursor: pointer;
    transition: color 0.3s;
  }

  .navmenu {
    padding: 0;
    z-index: 9997;
  }

  .navmenu ul {
    display: none;
    list-style: none;
    position: absolute;
    inset: 60px 20px 20px 20px;
    padding: 10px 0;
    margin: 0;
    border-radius: 6px;
    background-color: var(--nav-mobile-background-color);
    border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
    box-shadow: none;
    overflow-y: auto;
    transition: 0.3s;
    z-index: 9998;
  }

  .navmenu a,
  .navmenu a:focus {
    color: var(--nav-dropdown-color);
    padding: 10px 20px;
    font-family: var(--nav-font);
    font-size: 17px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: space-between;
    white-space: nowrap;
    transition: 0.3s;
  }

  .navmenu a i,
  .navmenu a:focus i {
    font-size: 12px;
    line-height: 0;
    margin-left: 5px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: 0.3s;
    background-color: color-mix(in srgb, var(--accent-color), transparent 90%);
  }

  .navmenu a i:hover,
  .navmenu a:focus i:hover {
    background-color: var(--accent-color);
    color: var(--contrast-color);
  }

  .navmenu a:hover,
  .navmenu .active,
  .navmenu .active:focus {
    color: var(--nav-dropdown-hover-color);
  }

  .navmenu .active i,
  .navmenu .active:focus i {
    background-color: var(--accent-color);
    color: var(--contrast-color);
    transform: rotate(180deg);
  }

  .navmenu .dropdown ul {
    position: static;
    display: none;
    z-index: 99;
    padding: 10px 0;
    margin: 10px 20px;
    background-color: var(--nav-dropdown-background-color);
    transition: all 0.5s ease-in-out;
  }

  .navmenu .dropdown ul ul {
    background-color: rgba(33, 37, 41, 0.1);
  }

  .navmenu .dropdown>.dropdown-active {
    display: block;
    background-color: rgba(33, 37, 41, 0.03);
  }

  .mobile-nav-active {
    overflow: hidden;
  }

  .mobile-nav-active .mobile-nav-toggle {
    color: #fff;
    position: absolute;
    font-size: 32px;
    top: 15px;
    right: 15px;
    margin-right: 0;
    z-index: 9999;
  }

  .mobile-nav-active .navmenu {
    position: fixed;
    overflow: hidden;
    inset: 0;
    background: rgba(33, 37, 41, 0.8);
    transition: 0.3s;
  }

  .mobile-nav-active .navmenu>ul {
    display: block;
  }
}

/*--------------------------------------------------------------
# Global Footer
--------------------------------------------------------------*/
.footer {
  color: var(--default-color);
  background-color: var(--background-color);
  font-size: 14px;
  padding: 40px 0;
  position: relative;
}

.footer .copyright p {
  margin-bottom: 0;
}

.footer .social-links {
  margin-top: 20px;
}

.footer .social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 50%);
  font-size: 16px;
  color: color-mix(in srgb, var(--default-color), transparent 50%);
  margin: 0 5px;
  transition: 0.3s;
}

.footer .social-links a:hover {
  color: var(--accent-color);
  border-color: var(--accent-color);
}

.footer .credits {
  margin-top: 10px;
  font-size: 13px;
  text-align: center;
}

/*--------------------------------------------------------------
# Preloader
--------------------------------------------------------------*/
#preloader {
  position: fixed;
  inset: 0;
  z-index: 9999;
  overflow: hidden;
  background-color: var(--background-color);
  transition: all 0.6s ease-out;
  width: 100%;
  height: 100vh;
}

#preloader:before,
#preloader:after {
  content: "";
  position: absolute;
  border: 4px solid var(--accent-color);
  border-radius: 50%;
  animation: animate-preloader 2s cubic-bezier(0, 0.2, 0.8, 1) infinite;
}

#preloader:after {
  animation-delay: -0.5s;
}

@keyframes animate-preloader {
  0% {
    width: 10px;
    height: 10px;
    top: calc(50% - 5px);
    left: calc(50% - 5px);
    opacity: 1;
  }

  100% {
    width: 72px;
    height: 72px;
    top: calc(50% - 36px);
    left: calc(50% - 36px);
    opacity: 0;
  }
}

/*--------------------------------------------------------------
# Scroll Top Button
--------------------------------------------------------------*/
.scroll-top {
  position: fixed;
  visibility: hidden;
  opacity: 0;
  right: 15px;
  bottom: -15px;
  z-index: 99999;
  background-color: var(--accent-color);
  width: 44px;
  height: 44px;
  border-radius: 50px;
  transition: all 0.4s;
}

.scroll-top i {
  font-size: 24px;
  color: var(--contrast-color);
  line-height: 0;
}

.scroll-top:hover {
  background-color: color-mix(in srgb, var(--accent-color), transparent 20%);
  color: var(--contrast-color);
}

.scroll-top.active {
  visibility: visible;
  opacity: 1;
  bottom: 15px;
}

/*--------------------------------------------------------------
# Disable aos animation delay on mobile devices
--------------------------------------------------------------*/
@media screen and (max-width: 768px) {
  [data-aos-delay] {
    transition-delay: 0 !important;
  }
}

/*--------------------------------------------------------------
# Global Page Titles & Breadcrumbs
--------------------------------------------------------------*/
.page-title {
  color: var(--default-color);
  background-color: var(--background-color);
  padding: 25px 0;
  position: relative;
  border-bottom: 1px solid color-mix(in srgb, var(--default-color), transparent 85%);
}

.page-title h1 {
  font-size: 24px;
  font-weight: 400;
}

.page-title .breadcrumbs ol {
  display: flex;
  flex-wrap: wrap;
  list-style: none;
  padding: 0;
  margin: 0;
  font-size: 14px;
  font-weight: 400;
}

.page-title .breadcrumbs ol li+li {
  padding-left: 10px;
}

.page-title .breadcrumbs ol li+li::before {
  content: "/";
  display: inline-block;
  padding-right: 10px;
  color: color-mix(in srgb, var(--default-color), transparent 70%);
}

/*--------------------------------------------------------------
# Global Sections
--------------------------------------------------------------*/
section,
.section {
  color: var(--default-color);
  background-color: var(--background-color);
  padding: 60px 0;
  scroll-margin-top: 100px;
  overflow: clip;
}

@media (max-width: 1199px) {

  section,
  .section {
    scroll-margin-top: 66px;
  }
}

/*--------------------------------------------------------------
# Global Section Titles
--------------------------------------------------------------*/
.section-title {
  text-align: center;
  padding-bottom: 60px;
  position: relative;
}

.section-title h2 {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 20px;
  padding-bottom: 20px;
  position: relative;
}

.section-title h2:after {
  content: "";
  position: absolute;
  display: block;
  width: 50px;
  height: 3px;
  background: var(--accent-color);
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
}

.section-title p {
  margin-bottom: 0;
}

/*--------------------------------------------------------------
# Hero Section
--------------------------------------------------------------*/
.hero {
  width: 100%;
  min-height: calc(100vh - 82px);
  position: relative;
  padding: 80px 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

@media (max-width: 1200px) {
  .hero {
    min-height: calc(100vh - 68px);
  }
}

.hero img {
  position: absolute;
  inset: 0;
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 1;
}

.hero .container {
  position: relative;
  z-index: 3;
}

.hero h2 {
  margin: 0;
  font-size: 48px;
  font-weight: 700;
}

.hero p {
  margin: 10px 0 0 0;
  font-size: 24px;
  color: var(--heading-color);
}

.hero .btn-get-started {
  color: var(--contrast-color);
  background: var(--accent-color);
  font-family: var(--heading-font);
  text-transform: uppercase;
  font-weight: 600;
  font-size: 12px;
  letter-spacing: 1px;
  display: inline-block;
  padding: 12px 40px;
  border-radius: 50px;
  transition: 0.5s;
  margin-top: 30px;
}

.hero .btn-get-started:hover {
  background: color-mix(in srgb, var(--accent-color) 90%, white 20%);
}

@media (max-width: 768px) {
  .hero h2 {
    font-size: 32px;
  }

  .hero p {
    font-size: 18px;
  }
}

/*--------------------------------------------------------------
# About Section
--------------------------------------------------------------*/
.about .content h2 {
  font-weight: 700;
  font-size: 24px;
}

.about .content ul {
  list-style: none;
  padding: 0;
}

.about .content ul li {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.about .content ul strong {
  margin-right: 10px;
}

.about .content ul i {
  font-size: 16px;
  margin-right: 5px;
  color: var(--accent-color);
  line-height: 0;
}

/*--------------------------------------------------------------
# Skills Section
--------------------------------------------------------------*/
.skills .progress {
  height: 60px;
  display: block;
  background: none;
  border-radius: 0;
}

.skills .progress .skill {
  color: var(--heading-color);
  padding: 0;
  margin: 0 0 6px 0;
  text-transform: uppercase;
  display: block;
  font-weight: 600;
  font-family: var(--heading-font);
}

.skills .progress .skill .val {
  float: right;
  font-style: normal;
}

.skills .progress-bar-wrap {
  background: color-mix(in srgb, var(--default-color), transparent 90%);
  height: 10px;
}

.skills .progress-bar {
  width: 1px;
  height: 10px;
  transition: 0.9s;
  background-color: var(--accent-color);
}

/*--------------------------------------------------------------
# Stats Section
--------------------------------------------------------------*/
.stats .stats-item {
  padding: 30px;
  width: 100%;
}

.stats .stats-item span {
  color: var(--heading-color);
  font-size: 48px;
  display: block;
  font-weight: 700;
  margin-bottom: 20px;
  padding-bottom: 20px;
  position: relative;
}

.stats .stats-item span:after {
  content: "";
  position: absolute;
  display: block;
  width: 25px;
  height: 3px;
  background: var(--accent-color);
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
}

.stats .stats-item p {
  color: color-mix(in srgb, var(--default-color), transparent 40%);
  padding: 0;
  margin: 0;
  font-family: var(--heading-font);
  font-weight: 500;
}

/*--------------------------------------------------------------
# Testimonials Section
--------------------------------------------------------------*/
.testimonials .section-header {
  margin-bottom: 40px;
}

.testimonials .testimonials-carousel,
.testimonials .testimonials-slider {
  overflow: hidden;
}

.testimonials .testimonial-item {
  text-align: center;
}

.testimonials .testimonial-item .testimonial-img {
  width: 120px;
  border-radius: 50%;
  border: 4px solid var(--background-color);
  margin: 0 auto;
}

.testimonials .testimonial-item h3 {
  font-size: 20px;
  font-weight: bold;
  margin: 10px 0 5px 0;
}

.testimonials .testimonial-item h4 {
  font-size: 14px;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
  margin: 0 0 15px 0;
}

.testimonials .testimonial-item .stars {
  margin-bottom: 15px;
}

.testimonials .testimonial-item .stars i {
  color: #ffc107;
  margin: 0 1px;
}

.testimonials .testimonial-item .quote-icon-left,
.testimonials .testimonial-item .quote-icon-right {
  color: color-mix(in srgb, var(--accent-color), transparent 50%);
  font-size: 26px;
  line-height: 0;
}

.testimonials .testimonial-item .quote-icon-left {
  display: inline-block;
  left: -5px;
  position: relative;
}

.testimonials .testimonial-item .quote-icon-right {
  display: inline-block;
  right: -5px;
  position: relative;
  top: 10px;
  transform: scale(-1, -1);
}

.testimonials .testimonial-item p {
  font-style: italic;
  margin: 0 auto 15px auto;
}

.testimonials .swiper-wrapper {
  height: auto;
}

.testimonials .swiper-pagination {
  margin-top: 20px;
  position: relative;
}

.testimonials .swiper-pagination .swiper-pagination-bullet {
  width: 12px;
  height: 12px;
  opacity: 1;
  background-color: color-mix(in srgb, var(--default-color), transparent 85%);
}

.testimonials .swiper-pagination .swiper-pagination-bullet-active {
  background-color: var(--accent-color);
}

@media (min-width: 992px) {
  .testimonials .testimonial-item p {
    width: 80%;
  }
}

/*--------------------------------------------------------------
# Resume Section
--------------------------------------------------------------*/
.resume .resume-title {
  color: var(--heading-color);
  font-size: 26px;
  font-weight: 700;
  margin-top: 20px;
  margin-bottom: 20px;
}

.resume .resume-item {
  padding: 0 0 20px 20px;
  margin-top: -2px;
  border-left: 2px solid var(--accent-color);
  position: relative;
}

.resume .resume-item h4 {
  line-height: 18px;
  font-size: 18px;
  font-weight: 600;
  text-transform: uppercase;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  margin-bottom: 10px;
}

.resume .resume-item h5 {
  font-size: 16px;
  padding: 5px 15px;
  display: inline-block;
  font-weight: 600;
  margin-bottom: 10px;
}

.resume .resume-item ul {
  padding-left: 20px;
}

.resume .resume-item ul li {
  padding-bottom: 10px;
}

.resume .resume-item:last-child {
  padding-bottom: 0;
}

.resume .resume-item::before {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  border-radius: 50px;
  left: -9px;
  top: 0;
  background: var(--background-color);
  border: 2px solid var(--accent-color);
}

/*--------------------------------------------------------------
# Services Section
--------------------------------------------------------------*/
.services .service-item {
  background-color: var(--surface-color);
  box-shadow: 0px 5px 90px 0px rgba(0, 0, 0, 0.1);
  height: 100%;
  padding: 60px 30px;
  text-align: center;
  transition: 0.3s;
  border-radius: 5px;
}

.services .service-item .icon {
  margin: 0 auto;
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: ease-in-out 0.3s;
  position: relative;
}

.services .service-item .icon i {
  font-size: 36px;
  transition: 0.5s;
  position: relative;
}

.services .service-item .icon svg {
  position: absolute;
  top: 0;
  left: 0;
}

.services .service-item .icon svg path {
  transition: 0.5s;
  fill: color-mix(in srgb, var(--default-color), transparent 95%);
}

.services .service-item h3 {
  font-weight: 700;
  margin: 10px 0 15px 0;
  font-size: 22px;
}

.services .service-item p {
  line-height: 24px;
  font-size: 14px;
  margin-bottom: 0;
}

.services .service-item:hover {
  box-shadow: 0px 5px 35px 0px rgba(0, 0, 0, 0.1);
}

.services .service-item.item-cyan i {
  color: #0dcaf0;
}

.services .service-item.item-cyan:hover .icon i {
  color: #fff;
}

.services .service-item.item-cyan:hover .icon path {
  fill: #0dcaf0;
}

.services .service-item.item-orange i {
  color: #fd7e14;
}

.services .service-item.item-orange:hover .icon i {
  color: #fff;
}

.services .service-item.item-orange:hover .icon path {
  fill: #fd7e14;
}

.services .service-item.item-teal i {
  color: #20c997;
}

.services .service-item.item-teal:hover .icon i {
  color: #fff;
}

.services .service-item.item-teal:hover .icon path {
  fill: #20c997;
}

.services .service-item.item-red i {
  color: #df1529;
}

.services .service-item.item-red:hover .icon i {
  color: #fff;
}

.services .service-item.item-red:hover .icon path {
  fill: #df1529;
}

.services .service-item.item-indigo i {
  color: #6610f2;
}

.services .service-item.item-indigo:hover .icon i {
  color: #fff;
}

.services .service-item.item-indigo:hover .icon path {
  fill: #6610f2;
}

.services .service-item.item-pink i {
  color: #f3268c;
}

.services .service-item.item-pink:hover .icon i {
  color: #fff;
}

.services .service-item.item-pink:hover .icon path {
  fill: #f3268c;
}

/*--------------------------------------------------------------
# Portfolio Section
--------------------------------------------------------------*/
.portfolio .portfolio-filters {
  padding: 0;
  margin: 0 auto 20px auto;
  list-style: none;
  text-align: center;
}

.portfolio .portfolio-filters li {
  cursor: pointer;
  display: inline-block;
  padding: 8px 20px 10px 20px;
  margin: 0;
  font-size: 15px;
  font-weight: 500;
  line-height: 1;
  margin-bottom: 5px;
  border-radius: 50px;
  transition: all 0.3s ease-in-out;
  font-family: var(--heading-font);
}

.portfolio .portfolio-filters li:hover,
.portfolio .portfolio-filters li.filter-active {
  color: var(--contrast-color);
  background-color: var(--accent-color);
}

.portfolio .portfolio-filters li:first-child {
  margin-left: 0;
}

.portfolio .portfolio-filters li:last-child {
  margin-right: 0;
}

@media (max-width: 575px) {
  .portfolio .portfolio-filters li {
    font-size: 14px;
    margin: 0 0 10px 0;
  }
}

.portfolio .portfolio-item {
  position: relative;
  overflow: hidden;
}

.portfolio .portfolio-item .portfolio-info {
  opacity: 0;
  position: absolute;
  left: 12px;
  right: 12px;
  bottom: -100%;
  z-index: 3;
  transition: all ease-in-out 0.5s;
  background: color-mix(in srgb, var(--surface-color), transparent 10%);
  padding: 15px;
}

.portfolio .portfolio-item .portfolio-info h4 {
  font-size: 18px;
  font-weight: 600;
  padding-right: 50px;
}

.portfolio .portfolio-item .portfolio-info p {
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  font-size: 14px;
  margin-bottom: 0;
  padding-right: 50px;
}

.portfolio .portfolio-item .portfolio-info .preview-link,
.portfolio .portfolio-item .portfolio-info .details-link {
  position: absolute;
  right: 50px;
  font-size: 24px;
  top: calc(50% - 14px);
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  transition: 0.3s;
  line-height: 0;
}

.portfolio .portfolio-item .portfolio-info .preview-link:hover,
.portfolio .portfolio-item .portfolio-info .details-link:hover {
  color: var(--accent-color);
}

.portfolio .portfolio-item .portfolio-info .details-link {
  right: 14px;
  font-size: 28px;
}

.portfolio .portfolio-item:hover .portfolio-info {
  opacity: 1;
  bottom: 0;
}

/*--------------------------------------------------------------
# Portfolio Details Section
--------------------------------------------------------------*/
.portfolio-details .portfolio-details-slider img {
  width: 100%;
}

.portfolio-details .portfolio-details-slider .swiper-pagination {
  margin-top: 20px;
  position: relative;
}

.portfolio-details .portfolio-details-slider .swiper-pagination .swiper-pagination-bullet {
  width: 12px;
  height: 12px;
  background-color: color-mix(in srgb, var(--default-color), transparent 85%);
  opacity: 1;
}

.portfolio-details .portfolio-details-slider .swiper-pagination .swiper-pagination-bullet-active {
  background-color: var(--accent-color);
}

.portfolio-details .portfolio-info {
  background-color: var(--surface-color);
  padding: 30px;
  box-shadow: 0px 0 30px rgba(0, 0, 0, 0.1);
}

.portfolio-details .portfolio-info h3 {
  font-size: 22px;
  font-weight: 700;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid color-mix(in srgb, var(--default-color), transparent 85%);
}

.portfolio-details .portfolio-info ul {
  list-style: none;
  padding: 0;
  font-size: 15px;
}

.portfolio-details .portfolio-info ul li+li {
  margin-top: 10px;
}

.portfolio-details .portfolio-description {
  padding-top: 30px;
}

.portfolio-details .portfolio-description h2 {
  font-size: 26px;
  font-weight: 700;
  margin-bottom: 20px;
}

.portfolio-details .portfolio-description p {
  padding: 0;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
}

/*--------------------------------------------------------------
# Contact Section
--------------------------------------------------------------*/
.contact .info-wrap {
  background-color: var(--surface-color);
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.1);
  padding: 30px;
}

@media (max-width: 575px) {
  .contact .info-wrap {
    padding: 20px;
  }
}

.contact .info-item {
  margin-bottom: 40px;
}

.contact .info-item i {
  font-size: 20px;
  color: var(--accent-color);
  background: color-mix(in srgb, var(--accent-color), transparent 92%);
  width: 44px;
  height: 44px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50px;
  transition: all 0.3s ease-in-out;
  margin-right: 15px;
}

.contact .info-item h3 {
  padding: 0;
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 5px;
}

.contact .info-item p {
  padding: 0;
  margin-bottom: 0;
  font-size: 14px;
}

.contact .info-item:hover i {
  background: var(--accent-color);
  color: var(--contrast-color);
}

.contact .php-email-form {
  background-color: var(--surface-color);
  height: 100%;
  padding: 30px;
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.1);
}

@media (max-width: 575px) {
  .contact .php-email-form {
    padding: 20px;
  }
}

.contact .php-email-form input[type=text],
.contact .php-email-form input[type=email],
.contact .php-email-form textarea {
  font-size: 14px;
  padding: 10px 15px;
  box-shadow: none;
  border-radius: 0;
  color: var(--default-color);
  background-color: var(--surface-color);
  border-color: color-mix(in srgb, var(--default-color), transparent 80%);
}

.contact .php-email-form input[type=text]:focus,
.contact .php-email-form input[type=email]:focus,
.contact .php-email-form textarea:focus {
  border-color: var(--accent-color);
}

.contact .php-email-form input[type=text]::placeholder,
.contact .php-email-form input[type=email]::placeholder,
.contact .php-email-form textarea::placeholder {
  color: color-mix(in srgb, var(--default-color), transparent 70%);
}

.contact .php-email-form button[type=submit] {
  color: var(--contrast-color);
  background: var(--accent-color);
  border: 0;
  padding: 10px 30px;
  transition: 0.4s;
  border-radius: 50px;
}

.contact .php-email-form button[type=submit]:hover {
  background: color-mix(in srgb, var(--accent-color), transparent 25%);
}

/*--------------------------------------------------------------
# Starter Section Section
--------------------------------------------------------------*/

/* Section Background Colors */
section {
  background-color: var(--background-color);
  padding: 60px 0;
}

.section-bg {
  background-color: #d4c9b8;
}

.section-title {
  background-color: #e0d8c8;
  padding: 20px;
  margin-bottom: 30px;
  border-radius: 5px;
}

.service-item, .portfolio-item, .testimonial-item {
  background-color: #e0d8c8;
  border-radius: 5px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
}

/* Modal Styles */
#loginModal .modal-content {
    background-color: var(--surface-color);
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

#loginModal .modal-header {
    border-bottom: 2px solid var(--accent-color);
    padding: 1.5rem;
    background-color: var(--surface-color);
    border-radius: 15px 15px 0 0;
}

#loginModal .modal-title {
    color: var(--heading-color);
    font-family: var(--heading-font);
    font-weight: 600;
    font-size: 1.5rem;
    letter-spacing: 0.5px;
}

#loginModal .modal-body {
    padding: 2rem;
    background-color: var(--surface-color);
}

#loginModal .form-label {
    color: var(--default-color);
    font-weight: 500;
    font-family: var(--nav-font);
    margin-bottom: 0.5rem;
}

#loginModal .form-control {
    background-color: var(--background-color);
    border: 2px solid var(--accent-color);
    color: var(--default-color);
    padding: 0.75rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-family: var(--default-font);
}

#loginModal .form-control:focus {
    background-color: var(--background-color);
    border-color: var(--heading-color);
    box-shadow: 0 0 0 0.25rem rgba(115, 68, 41, 0.15);
}

#loginModal .form-control::placeholder {
    color: var(--default-color);
    opacity: 0.7;
}

#loginModal .btn-primary {
    background-color: var(--accent-color);
    border: none;
    padding: 0.75rem 2rem;
    font-weight: 500;
    border-radius: 8px;
    font-family: var(--nav-font);
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    margin-top: 1rem;
}

#loginModal .btn-primary:hover {
    background-color: var(--heading-color);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(115, 68, 41, 0.2);
}

#loginModal .modal-footer {
    border-top: 2px solid var(--accent-color);
    padding: 1.5rem;
    background-color: var(--surface-color);
    border-radius: 0 0 15px 15px;
}

#loginModal .modal-footer p {
    color: var(--default-color);
    font-family: var(--default-font);
    margin: 0;
}

#loginModal .modal-footer a {
    color: var(--accent-color);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

#loginModal .modal-footer a:hover {
    color: var(--heading-color);
    text-decoration: underline;
}

#loginModal .btn-close {
    color: var(--default-color);
    opacity: 0.8;
    transition: all 0.3s ease;
}

#loginModal .btn-close:hover {
    opacity: 1;
    transform: rotate(90deg);
}

/* Modal backdrop */
.modal-backdrop.show {
    opacity: 0.8;
    background-color: var(--heading-color);
}

/* Hakkımda ve İletişim Sayfaları için Stiller */

/* Genel Bölüm Stilleri */
.hakkimda-section, .iletisim-section {
    background: linear-gradient(135deg, var(--off-white) 0%, var(--light-accent) 100%);
    padding: 4rem 0;
}

.section-title {
    color: var(--primary-color);
    font-weight: 700;
    position: relative;
}

.section-subtitle {
    color: var(--light-text);
    font-weight: 400;
}

.title-decoration {
    display: flex;
    justify-content: center;
    margin-top: 1rem;
}

.title-decoration span {
    display: inline-block;
    width: 30px;
    height: 5px;
    background-color: var(--primary-color);
    margin: 0 5px;
    border-radius: 5px;
}

/* Kartlar */
.profile-info-card, .content-card, .contact-info-card, .social-card, .contact-form-card, .map-card {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.05);
    padding: 2rem;
    height: 100%;
    transition: all 0.3s ease;
}

.profile-info-card:hover, .content-card:hover, .contact-info-card:hover, .social-card:hover, .contact-form-card:hover, .map-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 35px rgba(0, 0, 0, 0.1);
}

/* Hakkımda Sayfası */
.profile-image-container {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.1);
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.social-link:hover {
    background-color: var(--secondary-color);
    transform: translateY(-3px);
}

.timeline-list {
    list-style: none;
    padding-left: 0;
    position: relative;
}

.timeline-list::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 2px;
    background-color: var(--primary-color);
}

.timeline-item {
    position: relative;
    padding-left: 30px;
    margin-bottom: 25px;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -5px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: var(--primary-color);
}

.timeline-date {
    display: inline-block;
    padding: 3px 12px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 20px;
    font-size: 0.8rem;
    margin-bottom: 5px;
}

/* İletişim Sayfası */
.contact-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
}

.contact-item i {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-right: 15px;
    margin-top: 5px;
}

.contact-item h5 {
    margin-bottom: 5px;
    color: var(--primary-color);
}

.contact-form .form-control {
    border-radius: 0;
    border: 1px solid #ddd;
}

.contact-form .form-control:focus {
    box-shadow: none;
    border-color: var(--primary-color);
}

.contact-form .btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.contact-form .btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.map-container {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}
