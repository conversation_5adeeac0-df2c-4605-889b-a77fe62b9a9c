from django.db import models
from django.contrib.auth.models import User
from django.utils.text import slugify

class Kategori(models.Model):
    ad = models.CharField(max_length=100, verbose_name="<PERSON><PERSON><PERSON>")
    slug = models.SlugField(unique=True)
    aciklama = models.TextField(blank=True, verbose_name="Kategori Açıklaması")
    
    class Meta:
        verbose_name = "<PERSON>gori"
        verbose_name_plural = "Kategoriler"
        
    def __str__(self):
        return self.ad
    
    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.ad)
        super().save(*args, **kwargs)

class Etiket(models.Model):
    ad = models.CharField(max_length=50, unique=True)
    slug = models.SlugField(unique=True)
    
    class Meta:
        verbose_name = "Etiket"
        verbose_name_plural = "Etiketler"
        
    def __str__(self):
        return self.ad
    
    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.ad)
        super().save(*args, **kwargs)

class Video(models.Model):
    baslik = models.CharField(max_length=200, verbose_name="Video Başlığı")
    slug = models.SlugField(unique=True)
    aciklama = models.TextField(verbose_name="Video Açıklaması")
    youtube_url = models.URLField(verbose_name="YouTube Video URL")
    kategori = models.ForeignKey(Kategori, on_delete=models.CASCADE, related_name='videolar')
    etiketler = models.ManyToManyField(Etiket, related_name='videolar', blank=True)
    onizleme_resmi = models.ImageField(upload_to='video_onizleme/', blank=True, null=True)
    goruntulenme = models.PositiveIntegerField(default=0, verbose_name="Görüntülenme Sayısı")
    begeni = models.PositiveIntegerField(default=0, verbose_name="Beğeni Sayısı")
    tarih = models.DateTimeField(auto_now_add=True, verbose_name="Eklenme Tarihi")
    aktif = models.BooleanField(default=True, verbose_name="Aktif")
    begenenler = models.ManyToManyField(User, related_name='begenilen_videolar', blank=True)
    ekleyen = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='videolar', verbose_name="Ekleyen Kullanıcı")

    class Meta:
        verbose_name = "Video"
        verbose_name_plural = "Videolar"
        ordering = ['-tarih']

    def __str__(self):
        return self.baslik
    
    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.baslik)
        super().save(*args, **kwargs)
    
    def begeni_sayisi(self):
        return self.begenenler.count()
    
    def kullanici_begendi_mi(self, user):
        if user.is_authenticated:
            return self.begenenler.filter(id=user.id).exists()
        return False 