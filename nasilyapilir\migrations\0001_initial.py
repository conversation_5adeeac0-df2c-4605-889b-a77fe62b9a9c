# Generated by Django 5.2 on 2025-05-01 13:18

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Etiket',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ad', models.CharField(max_length=50, unique=True)),
                ('slug', models.SlugField(unique=True)),
            ],
            options={
                'verbose_name': 'Etiket',
                'verbose_name_plural': 'Etiketler',
            },
        ),
        migrations.CreateModel(
            name='Kategori',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ad', models.CharField(max_length=100, verbose_name='<PERSON><PERSON><PERSON>')),
                ('slug', models.SlugField(unique=True)),
                ('aciklama', models.TextField(blank=True, verbose_name='<PERSON>gori Açıklaması')),
            ],
            options={
                'verbose_name': 'Kategori',
                'verbose_name_plural': 'Kategoriler',
            },
        ),
        migrations.CreateModel(
            name='Video',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('baslik', models.CharField(max_length=200, verbose_name='Video Başlığı')),
                ('slug', models.SlugField(unique=True)),
                ('aciklama', models.TextField(verbose_name='Video Açıklaması')),
                ('youtube_url', models.URLField(verbose_name='YouTube Video URL')),
                ('onizleme_resmi', models.ImageField(blank=True, null=True, upload_to='video_onizleme/')),
                ('goruntulenme', models.PositiveIntegerField(default=0, verbose_name='Görüntülenme Sayısı')),
                ('begeni', models.PositiveIntegerField(default=0, verbose_name='Beğeni Sayısı')),
                ('tarih', models.DateTimeField(auto_now_add=True, verbose_name='Eklenme Tarihi')),
                ('aktif', models.BooleanField(default=True, verbose_name='Aktif')),
                ('etiketler', models.ManyToManyField(blank=True, related_name='videolar', to='nasilyapilir.etiket')),
                ('kategori', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='videolar', to='nasilyapilir.kategori')),
            ],
            options={
                'verbose_name': 'Video',
                'verbose_name_plural': 'Videolar',
                'ordering': ['-tarih'],
            },
        ),
        migrations.CreateModel(
            name='Yorum',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('icerik', models.TextField(verbose_name='Yorum')),
                ('tarih', models.DateTimeField(auto_now_add=True)),
                ('aktif', models.BooleanField(default=True)),
                ('video', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='yorumlar', to='nasilyapilir.video')),
                ('yazar', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Yorum',
                'verbose_name_plural': 'Yorumlar',
                'ordering': ['-tarih'],
            },
        ),
    ]
