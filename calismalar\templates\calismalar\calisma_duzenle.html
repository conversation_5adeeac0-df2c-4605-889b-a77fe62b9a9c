{% extends 'base.html' %}
{% load static %}

{% block title %}{{ calisma.baslik }} - Düzenle - Küp Cadısı{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'assets/css/modern-theme.css' %}">
<link rel="stylesheet" href="{% static 'assets/css/calisma_duzenle.css' %}">
{% endblock %}

{% block content %}
<section class="calisma-form-section py-5">
    <div class="container">
        <!-- <PERSON><PERSON>l<PERSON><PERSON> Bölümü -->
        <div class="section-header text-center mb-5 wow fadeIn" data-wow-delay="0.2s">
            <h1 class="display-4 fw-bold mb-3 section-title">Çalışma Düzenle</h1>
            <p class="lead section-subtitle">{{ calisma.baslik }} çalışmasını düzenleyin</p>
            <div class="title-decoration">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="form-card wow fadeInUp" data-wow-delay="0.3s">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="bi bi-pencil-square me-2"></i>Çalışma Bilgileri</h5>
                        <a href="{% url 'calismalar:calisma_detay' calisma.slug %}" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-eye me-1"></i>Görüntüle
                        </a>
                    </div>
                    <div class="card-body">
                        <form method="post" enctype="multipart/form-data" class="calisma-form">
                            {% csrf_token %}
                            
                            {% if form.errors %}
                            <div class="alert alert-danger">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                Lütfen formdaki hataları düzeltin.
                            </div>
                            {% endif %}
                            
                            <div class="row g-4">
                                <!-- Başlık -->
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="{{ form.baslik.id_for_label }}" class="form-label">Başlık <span class="text-danger">*</span></label>
                                        {{ form.baslik }}
                                        {% if form.baslik.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.baslik.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                                
                                <!-- Kategori -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="{{ form.kategori.id_for_label }}" class="form-label">Kategori</label>
                                        {{ form.kategori }}
                                        {% if form.kategori.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.kategori.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                                
                                <!-- Etiketler -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="{{ form.etiketler.id_for_label }}" class="form-label">Etiketler</label>
                                        {{ form.etiketler }}
                                        <small class="form-text text-muted">Virgülle ayırarak birden fazla etiket ekleyebilirsiniz</small>
                                        {% if form.etiketler.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.etiketler.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                                
                                <!-- Açıklama -->
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="{{ form.aciklama.id_for_label }}" class="form-label">Açıklama</label>
                                        {{ form.aciklama }}
                                        {% if form.aciklama.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.aciklama.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                                
                                <!-- Fotoğraflar -->
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="form-label"><i class="bi bi-images me-2"></i>Mevcut Fotoğraflar</label>
                                        <div class="current-photos">
                                            {% if calisma.fotograflar.exists %}
                                            <div class="row g-3">
                                                {% for fotograf in calisma.fotograflar.all %}
                                                <div class="col-md-3 col-sm-6">
                                                    <div class="photo-item">
                                                        <img src="{{ fotograf.fotograf.url }}" alt="{{ calisma.baslik }}" class="img-thumbnail">
                                                        <div class="photo-actions">
                                                            <div class="form-check">
                                                                <input class="form-check-input" type="checkbox" name="sil_fotograf_{{ fotograf.id }}" id="sil_fotograf_{{ fotograf.id }}">
                                                                <label class="form-check-label" for="sil_fotograf_{{ fotograf.id }}">
                                                                    Sil
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                {% endfor %}
                                            </div>
                                            {% else %}
                                            <p class="text-muted">Bu çalışma için henüz fotoğraf eklenmemiş</p>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Yeni Fotoğraflar -->
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="form-label"><i class="bi bi-cloud-upload me-2"></i>Yeni Fotoğraflar Ekle</label>
                                        
                                        <!-- Yeni dosya yükleme alanı -->
                                        <div class="file-upload-wrapper" id="fileUploadArea">
                                            <div class="file-upload-icon">
                                                <i class="bi bi-cloud-arrow-up"></i>
                                            </div>
                                            <div class="file-upload-text">
                                                Fotoğrafları sürükleyip bırakın veya seçmek için tıklayın
                                            </div>
                                            <input type="file" name="fotograflar" multiple id="id_fotograflar" class="custom-file-input">
                                        </div>
                                        
                                        <small class="form-text text-muted"><i class="bi bi-info-circle me-1"></i>Birden fazla fotoğraf seçebilirsiniz veya sürükleyip bırakabilirsiniz</small>
                                        {% if form.fotograflar.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.fotograflar.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                                
                                <!-- Butonlar -->
                                <div class="col-12 mt-4">
                                    <div class="form-actions">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-save me-2"></i>Kaydet
                                        </button>
                                        <a href="{% url 'calismalar:calisma_detay' calisma.slug %}" class="btn btn-outline-secondary ms-2">
                                            <i class="bi bi-x-circle me-2"></i>İptal
                                        </a>
                                        <a href="{% url 'calismalar:calisma_sil' calisma.slug %}" class="btn btn-danger ms-auto">
                                            <i class="bi bi-trash me-2"></i>Çalışmayı Sil
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/wow/1.1.2/wow.min.js"></script>
<script src="{% static 'assets/js/calisma_duzenle.js' %}"></script>
{% endblock %}
