{% extends 'base.html' %}

{% block title %}Duyurular{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2>Duyurular</h2>
    <ul class="list-group">
        {% for announcement in announcements %}
            <li class="list-group-item">
                <strong>{{ announcement.sender.username }}:</strong> {{ announcement.content }}
                <small class="text-muted">{{ announcement.timestamp|date:"d/m/Y H:i" }}</small>
                {% if not announcement.is_read %}
                    <form action="{% url 'messaging:api_mark_announcement_read' announcement.id %}" method="post" class="d-inline">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-sm btn-outline-primary float-right">Okundu Olarak İşaretle</button>
                    </form>
                {% endif %}
            </li>
        {% empty %}
            <li class="list-group-item">Hen<PERSON>z duyuru yok.</li>
        {% endfor %}
    </ul>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const forms = document.querySelectorAll('.list-group-item form');

        forms.forEach(form => {
            form.addEventListener('submit', function(event) {
                event.preventDefault(); // Formun normal submit olmasını engelle

                const form = event.target;
                const url = form.action;
                const method = form.method;
                const formData = new FormData(form);
                const csrfToken = formData.get('csrfmiddlewaretoken');

                fetch(url, {
                    method: method,
                    headers: {
                        'X-CSRFToken': csrfToken,
                        'X-Requested-With': 'XMLHttpRequest' // Django'nun is_ajax() kontrolü için
                    },
                    body: formData
                })
                .then(response => {
                    if (response.ok) {
                        // Başarılı olursa formu gizle veya butonu devre dışı bırak
                        form.style.display = 'none';
                        // İsteğe bağlı olarak duyurunun yanına "Okundu" gibi bir metin ekleyebilirsiniz.
                        const listItem = form.closest('.list-group-item');
                        const readStatusSpan = document.createElement('small');
                        readStatusSpan.classList.add('text-success', 'ml-2');
                        readStatusSpan.textContent = ' (Okundu)';
                        listItem.appendChild(readStatusSpan);

                    } else {
                        console.error('Duyuruyu okundu olarak işaretleme başarısız oldu.');
                        // Hata durumunda kullanıcıya bilgi verebilirsiniz.
                    }
                })
                .catch(error => {
                    console.error('Hata:', error);
                    // Hata durumunda kullanıcıya bilgi verebilirsiniz.
                });
            });
        });
    });
</script>
{% endblock %}