{% extends 'base.html' %}

{% load static %}

{% block title %}Çalışmalar - Küp Cadısı{% endblock %}

{% block content %}
<main class="modern-section py-5">
    <div class="container modern-container">
        <!-- CSRF Token for AJAX requests -->
        {% csrf_token %}

        <!-- Ba<PERSON><PERSON><PERSON><PERSON> Bölümü -->
        <div class="modern-header mb-5 text-center">
            <h1 class="gradient-heading display-4 fw-bold mb-4 wow fadeInUp" data-wow-delay="0.1s">Öğrenci Çalışmaları</h1>
            <p class="lead text-muted mb-3 wow fadeInUp" data-wow-delay="0.2s">Küp Cadısı atölyesinde öğrencilerimiz tarafından yapılan özgün çalışmaları keşfedin</p>
            <div class="d-flex align-items-center justify-content-center flex-wrap wow fadeInUp" data-wow-delay="0.3s">
                <div class="me-4 d-flex align-items-center mb-2">
                    <i class="bi bi-palette-fill me-2" style="color: var(--color-brown); font-size: 1.2rem;"></i>
                    <span>Yaratıcı Eserler</span>
                </div>
                <div class="me-4 d-flex align-items-center mb-2">
                    <i class="bi bi-people-fill me-2" style="color: var(--color-moss); font-size: 1.2rem;"></i>
                    <span>Yetenekli Öğrenciler</span>
                </div>
                <div class="d-flex align-items-center mb-2">
                    <i class="bi bi-stars me-2" style="color: var(--color-navy); font-size: 1.2rem;"></i>
                    <span>İlham Verici Tasarımlar</span>
                </div>
            </div>
        </div>

        <!-- Öne Çıkan Çalışmalar Slider -->
        {% if one_cikan_calismalar %}
        <div class="featured-works-slider mb-5 wow fadeIn" data-wow-delay="0.3s">
            <div class="container">
                <div class="row">
                    <div class="col-12 text-center">
                        <h2 class="slider-title mb-4"><i class="bi bi-award"></i>Öne Çıkan Çalışmalar</h2>
                        <p class="text-muted mb-5">En çok beğenilen ve görüntülenen çalışmalarımızı keşfedin</p>
                    </div>
                </div>

                <div class="swiper featured-slider">
                    <div class="swiper-wrapper">
                        {% for calisma in one_cikan_calismalar %}
                        <div class="swiper-slide">
                            <div class="featured-slide">
                                <div class="featured-image">
                                    {% if calisma.fotograflar.exists %}
                                    <img src="{{ calisma.fotograflar.all.0.fotograf.url }}" alt="{{ calisma.baslik }}" class="slide-image">
                                    {% else %}
                                    <img src="{% static 'img/placeholder.jpg' %}" alt="Görsel Yok" class="slide-image">
                                    {% endif %}
                                    <div class="featured-overlay">
                                        <a href="{% url 'calismalar:calisma_detay' calisma.slug %}" class="featured-btn">
                                            <i class="bi bi-eye me-2"></i>İncele
                                        </a>
                                    </div>
                                </div>
                                <div class="featured-content">
                                    <h3 class="featured-title">{{ calisma.baslik }}</h3>
                                    <p class="featured-author">
                                        <a href="{% url 'uyelik:kullanici_profil' calisma.user.username %}" class="d-flex align-items-center text-decoration-none">
                                            {% if calisma.user.profil.profil_fotografi %}
                                            <img src="{{ calisma.user.profil.profil_fotografi.url }}" alt="{{ calisma.user.username }}" class="rounded-circle me-2" style="width: 30px; height: 30px; object-fit: cover;">
                                            {% else %}
                                            <div class="rounded-circle bg-secondary text-white d-flex align-items-center justify-content-center me-2" style="width: 30px; height: 30px;">
                                                <i class="bi bi-person-fill"></i>
                                            </div>
                                            {% endif %}
                                            <span>{{ calisma.user.get_full_name|default:calisma.user.username }}</span>
                                        </a>
                                    </p>
                                    <div class="featured-meta">
                                        <span><i class="bi bi-heart me-1"></i>{{ calisma.begeni_sayisi }}</span>
                                        <span><i class="bi bi-eye me-1"></i>{{ calisma.goruntuleme_sayisi }}</span>
                                        {% if calisma.kategori %}
                                        <span><i class="bi bi-tag me-1"></i>{{ calisma.kategori.ad }}</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}

                        <!-- Eğer çalışma sayısı az ise, aynı çalışmaları tekrar ekle (slider'ın düzgün çalışması için) -->
                        {% if one_cikan_calismalar|length < 3 %}
                            {% for calisma in one_cikan_calismalar %}
                            <div class="swiper-slide">
                                <div class="featured-slide">
                                    <div class="featured-image">
                                        {% if calisma.fotograflar.exists %}
                                        <img src="{{ calisma.fotograflar.all.0.fotograf.url }}" alt="{{ calisma.baslik }}" class="slide-image">
                                        {% else %}
                                        <img src="{% static 'img/placeholder.jpg' %}" alt="Görsel Yok" class="slide-image">
                                        {% endif %}
                                        <div class="featured-overlay">
                                            <a href="{% url 'calismalar:calisma_detay' calisma.slug %}" class="featured-btn">
                                                <i class="bi bi-eye me-2"></i>İncele
                                            </a>
                                        </div>
                                    </div>
                                    <div class="featured-content">
                                        <h3 class="featured-title">{{ calisma.baslik }}</h3>
                                        <p class="featured-author">
                                            <a href="{% url 'uyelik:kullanici_profil' calisma.user.username %}" class="d-flex align-items-center text-decoration-none">
                                                {% if calisma.user.profil.profil_fotografi %}
                                                <img src="{{ calisma.user.profil.profil_fotografi.url }}" alt="{{ calisma.user.username }}" class="rounded-circle me-2" style="width: 30px; height: 30px; object-fit: cover;">
                                                {% else %}
                                                <div class="rounded-circle bg-secondary text-white d-flex align-items-center justify-content-center me-2" style="width: 30px; height: 30px;">
                                                    <i class="bi bi-person-fill"></i>
                                                </div>
                                                {% endif %}
                                                <span>{{ calisma.user.get_full_name|default:calisma.user.username }}</span>
                                            </a>
                                        </p>
                                        <div class="featured-meta">
                                            <span><i class="bi bi-heart me-1"></i>{{ calisma.begeni_sayisi }}</span>
                                            <span><i class="bi bi-eye me-1"></i>{{ calisma.goruntuleme_sayisi }}</span>
                                            {% if calisma.kategori %}
                                            <span><i class="bi bi-tag me-1"></i>{{ calisma.kategori.ad }}</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        {% endif %}
                    </div>
                    <div class="swiper-pagination"></div>
                    <div class="swiper-button-next"></div>
                    <div class="swiper-button-prev"></div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Filtreler -->
        <div class="modern-filters mb-4 wow fadeInUp" data-wow-delay="0.3s">
            <div class="row">
                <div class="col-md-8">
                    <div class="modern-tag-cloud">
                        <a href="{% url 'calismalar:calisma_listesi' %}" class="modern-tag {% if not kategori and not etiket %}active{% endif %}">
                            Tümü
                        </a>
                        {% for kat in kategoriler %}
                        <a href="{% url 'calismalar:kategori_calismalari' kat.slug %}" class="modern-tag {% if kategori.slug == kat.slug %}active{% endif %}">
                            {{ kat.ad }}
                        </a>
                        {% endfor %}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="animated-search-container wow fadeInRight" data-wow-delay="0.3s">
                        <form action="{% url 'calismalar:calisma_arama' %}" method="get" class="modern-search-form mb-2">
                            <div class="input-group animated-search-group">
                                <input type="text" name="q" class="form-control animated-search-input" placeholder="Çalışma ara..." value="{{ request.GET.q|default:'' }}" aria-label="Çalışma ara">
                                <button type="submit" class="modern-btn-search" aria-label="Ara">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>

                    {% if can_add_calisma %}
                    <div class="text-end mt-3">
                        <a href="{% url 'calismalar:calisma_ekle' %}" class="modern-btn modern-btn-sm animated-btn wow fadeInRight" data-wow-delay="0.4s">
                            <span class="btn-content">
                                <i class="bi bi-plus-circle me-2"></i>Yeni Çalışma Ekle
                            </span>
                            <span class="btn-glow"></span>
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Çalışma Listesi -->
        <div class="row g-4 mb-5">
            {% for calisma in page_obj %}
            <div class="col-md-6 col-lg-4 wow fadeInUp" data-wow-delay="{{ forloop.counter|divisibleby:3|yesno:'0.6,0.4,0.2' }}s">
                <div class="modern-card h-100">
                    <div class="modern-card-image" style="margin-bottom: 1rem;">
                        {% if calisma.fotograflar.exists %}
                        <img src="{{ calisma.fotograflar.all.0.fotograf.url }}" alt="{{ calisma.baslik }}" class="modern-image">
                        {% else %}
                        <img src="{% static 'img/placeholder.jpg' %}" alt="Görsel Yok" class="modern-image">
                        {% endif %}
                        <div class="modern-card-overlay">
                            <a href="{% url 'calismalar:calisma_detay' calisma.slug %}" class="modern-btn-circle">
                                <i class="bi bi-eye"></i>
                            </a>
                        </div>
                        {% if calisma.kategori %}
                        <span class="modern-badge-corner">{{ calisma.kategori.ad }}</span>
                        {% endif %}
                    </div>
                    <div class="modern-card-body">
                        <h3 class="modern-card-title">
                            <a href="{% url 'calismalar:calisma_detay' calisma.slug %}">{{ calisma.baslik }}</a>
                        </h3>
                        <!-- Kısa Açıklama -->
                        <p class="modern-card-description mb-3">{{ calisma.aciklama|truncatechars:80 }}</p>

                        <!-- Öğrenci Adı -->
                        <div class="modern-card-author mb-2">
                            <a href="{% url 'uyelik:kullanici_profil' calisma.user.username %}" class="d-flex align-items-center text-decoration-none">
                                {% if calisma.user.profil.profil_fotografi %}
                                <img src="{{ calisma.user.profil.profil_fotografi.url }}" alt="{{ calisma.user.username }}" class="rounded-circle me-2" style="width: 30px; height: 30px; object-fit: cover;">
                                {% else %}
                                <div class="rounded-circle bg-secondary text-white d-flex align-items-center justify-content-center me-2" style="width: 30px; height: 30px;">
                                    <i class="bi bi-person-fill"></i>
                                </div>
                                {% endif %}
                                <span class="fw-medium" style="color: var(--color-brown);">{{ calisma.user.get_full_name|default:calisma.user.username }}</span>
                            </a>
                        </div>

                        <!-- Meta Bilgiler -->
                        <div class="modern-card-meta">
                            <span class="modern-meta-item"><i class="bi bi-calendar me-1"></i>{{ calisma.olusturma_tarihi|date:"d.m.Y" }}</span>
                            <span class="modern-meta-item"><i class="bi bi-eye me-1"></i>{{ calisma.goruntuleme_sayisi }}</span>
                            <span class="modern-meta-item"><i class="bi bi-heart me-1"></i>{{ calisma.begeni_sayisi }}</span>
                        </div>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="col-12">
                <div class="modern-empty-state wow fadeIn" data-wow-delay="0.3s">
                    <i class="bi bi-search display-1"></i>
                    <h3 class="mt-4">Çalışma Bulunamadı</h3>
                    <p class="text-muted">Aradığınız kriterlere uygun çalışma bulunamadı.</p>
                    <div class="d-flex justify-content-center mt-3">
                        <a href="{% url 'calismalar:calisma_listesi' %}" class="modern-btn me-2">
                            <i class="bi bi-arrow-left me-2"></i>Tüm Çalışmaları Göster
                        </a>
                        {% if can_add_calisma %}
                        <a href="{% url 'calismalar:calisma_ekle' %}" class="modern-btn modern-btn-success">
                            <i class="bi bi-plus-circle me-2"></i>Yeni Çalışma Ekle
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Sayfalama -->
        {% if page_obj.has_other_pages %}
        <div class="modern-pagination wow fadeInUp" data-wow-delay="0.4s">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?sayfa={{ page_obj.previous_page_number }}{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}">
                        <i class="bi bi-chevron-left"></i>
                    </a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link"><i class="bi bi-chevron-left"></i></span>
                </li>
                {% endif %}

                {% for i in page_obj.paginator.page_range %}
                    {% if page_obj.number == i %}
                    <li class="page-item active">
                        <span class="page-link">{{ i }}</span>
                    </li>
                    {% elif i > page_obj.number|add:'-3' and i < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?sayfa={{ i }}{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}">{{ i }}</a>
                    </li>
                    {% endif %}
                {% endfor %}

                {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?sayfa={{ page_obj.next_page_number }}{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}">
                        <i class="bi bi-chevron-right"></i>
                    </a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link"><i class="bi bi-chevron-right"></i></span>
                </li>
                {% endif %}
            </ul>
        </div>
        {% endif %}
    </div>
</main>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'assets/vendor/swiper/swiper-bundle.min.css' %}">
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Öne çıkan çalışmalar slider'ını başlat
        const featuredSlider = new Swiper('.featured-slider', {
            slidesPerView: 1,
            spaceBetween: 30,
            loop: true,
            loopAdditionalSlides: 3,
            speed: 800,
            autoplay: {
                delay: 4000,
                disableOnInteraction: false,
            },
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
                dynamicBullets: true,
            },
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
            breakpoints: {
                // 768px ve üzeri ekranlarda 2 slide göster
                768: {
                    slidesPerView: 2,
                    spaceBetween: 20,
                },
                // 1024px ve üzeri ekranlarda 3 slide göster
                1024: {
                    slidesPerView: 3,
                    spaceBetween: 30,
                },
            },
            effect: 'slide',
            grabCursor: true,
            parallax: true,
            on: {
                init: function() {
                    console.log('Slider başlatıldı');

                    // Slider'daki tüm slide'ları seç
                    const slides = document.querySelectorAll('.featured-slide');

                    // Her slide'a animasyon sınıfı ekle
                    slides.forEach(function(slide) {
                        slide.classList.add('animated');
                    });
                }
            }
        });

        // Slider'ı manuel olarak başlat
        if (featuredSlider) {
            featuredSlider.autoplay.start();
            console.log('Autoplay başlatıldı');
        }

        // Slider'a hover olduğunda autoplay'i durdur, hover'dan çıkınca devam ettir
        const sliderContainer = document.querySelector('.featured-slider');
        if (sliderContainer) {
            sliderContainer.addEventListener('mouseenter', function() {
                featuredSlider.autoplay.stop();
            });

            sliderContainer.addEventListener('mouseleave', function() {
                featuredSlider.autoplay.start();
            });
        }
    });
</script>
{% endblock %}