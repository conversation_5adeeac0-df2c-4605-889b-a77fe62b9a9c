from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.contrib.auth.models import User
import json
from .models import Hakkimda, Egitim, IletisimBilgisi, SosyalMedya
from .forms import HakkimdaForm, EgitimForm, IletisimBilgisiForm, SosyalMedyaForm

def ana_sayfa(request):
    """Ana sayfa görünümü"""
    return render(request, 'anasayfa/ana_sayfa.html')

def hakkimda(request):
    """Hakkımda sayfasını render eder."""
    hakkimda = None
    admin_users = User.objects.filter(is_staff=True)

    # Önce giriş yapmış kullanıcının bilgilerini kontrol et (eğer yöneticiyse)
    if request.user.is_authenticated and request.user.is_staff:
        try:
            # <PERSON><PERSON><PERSON> yapmış yöneticinin birincil profilini kontrol et
            hakkimda = Hakkimda.objects.filter(user=request.user, is_primary=True).first()

            # Eğer birincil profil yoksa, herhangi bir profilini al
            if not hakkimda:
                hakkimda = Hakkimda.objects.filter(user=request.user).first()
        except Exception as e:
            print(f"Error getting Hakkimda: {str(e)}")

    # Eğer hakkımda bilgisi bulunamadıysa, birincil olarak işaretlenmiş herhangi bir profili göster
    if not hakkimda:
        try:
            hakkimda = Hakkimda.objects.filter(is_primary=True).first()
        except Exception as e:
            print(f"Error getting primary Hakkimda: {str(e)}")

    # Eğer birincil profil yoksa, herhangi bir yöneticinin profilini göster
    if not hakkimda:
        for admin in admin_users:
            try:
                hakkimda = Hakkimda.objects.filter(user=admin).first()
                if hakkimda:
                    break
            except Exception as e:
                print(f"Error getting Hakkimda for admin {admin.username}: {str(e)}")

    # Eğer hiçbir yöneticinin hakkımda bilgisi yoksa, varsayılan değerlerle oluştur
    if not hakkimda and admin_users.exists():
        admin_user = admin_users.first()
        hakkimda = Hakkimda(
            user=admin_user,
            baslik="Seramik Sanatçısı",
            aciklama="Seramik sanatı ile ilgileniyorum ve çeşitli tekniklerle özgün eserler üretiyorum.",
            is_primary=True  # Varsayılan olarak birincil profil olarak işaretle
        )
        hakkimda.save()

    # Eğitim bilgilerini al
    egitimler = []
    if hakkimda:
        egitimler = Egitim.objects.filter(hakkimda=hakkimda)

    # Son 4 çalışmayı al
    son_calismalar = []
    try:
        # calismalar uygulamasından Calisma modelini import et
        from calismalar.models import Calisma, CalismaFotograf

        # Eğer hakkımda bilgisi varsa, o kullanıcının son 4 çalışmasını al
        if hakkimda:
            calismalar = Calisma.objects.filter(user=hakkimda.user).order_by('-olusturma_tarihi')[:4]

            # Her çalışma için ilk fotoğrafı al
            for calisma in calismalar:
                fotograf = CalismaFotograf.objects.filter(calisma=calisma).first()
                if fotograf:
                    son_calismalar.append({
                        'calisma': calisma,
                        'fotograf': fotograf
                    })
    except Exception as e:
        print(f"Error getting Calismalar: {str(e)}")

    context = {
        'hakkimda': hakkimda,
        'egitimler': egitimler,
        'son_calismalar': son_calismalar,
        'can_edit': request.user.is_authenticated and request.user.is_staff
    }
    return render(request, 'anasayfa/hakkimda.html', context)

def iletisim(request):
    """İletişim sayfasını render eder."""
    # Yönetici kullanıcılarını bul
    admin_users = User.objects.filter(is_staff=True)

    # İletişim bilgilerini ve sosyal medya hesaplarını veritabanından çek
    iletisim_bilgisi = None
    sosyal_medya_hesaplari = []

    # Önce giriş yapmış kullanıcının bilgilerini kontrol et (eğer yöneticiyse)
    if request.user.is_authenticated and request.user.is_staff:
        try:
            # Giriş yapmış yöneticinin birincil iletişim bilgisini kontrol et
            iletisim_bilgisi = IletisimBilgisi.objects.filter(user=request.user, is_primary=True).first()

            # Eğer birincil iletişim bilgisi yoksa, herhangi bir iletişim bilgisini al
            if not iletisim_bilgisi:
                iletisim_bilgisi = IletisimBilgisi.objects.filter(user=request.user).first()

            # Giriş yapmış yöneticinin sosyal medya hesaplarını al
            sosyal_medya_hesaplari = SosyalMedya.objects.filter(user=request.user, aktif=True)
        except Exception as e:
            print(f"Error getting IletisimBilgisi: {str(e)}")

    # Eğer iletişim bilgisi bulunamadıysa, birincil olarak işaretlenmiş herhangi bir iletişim bilgisini göster
    if not iletisim_bilgisi:
        try:
            iletisim_bilgisi = IletisimBilgisi.objects.filter(is_primary=True).first()

            # Birincil iletişim bilgisinin sahibinin sosyal medya hesaplarını al
            if iletisim_bilgisi:
                # Önce birincil sosyal medya setini kontrol et
                primary_sosyal_medya = SosyalMedya.objects.filter(user=iletisim_bilgisi.user, is_primary_set=True, aktif=True)
                if primary_sosyal_medya.exists():
                    sosyal_medya_hesaplari = primary_sosyal_medya
                else:
                    # Birincil set yoksa, kullanıcının tüm aktif sosyal medya hesaplarını al
                    sosyal_medya_hesaplari = SosyalMedya.objects.filter(user=iletisim_bilgisi.user, aktif=True)
        except Exception as e:
            print(f"Error getting primary IletisimBilgisi: {str(e)}")

    # Eğer birincil iletişim bilgisi yoksa, herhangi bir yöneticinin iletişim bilgisini göster
    if not iletisim_bilgisi:
        for admin in admin_users:
            try:
                iletisim_bilgisi = IletisimBilgisi.objects.filter(user=admin).first()
                if iletisim_bilgisi:
                    # Yöneticinin sosyal medya hesaplarını al
                    sosyal_medya_hesaplari = SosyalMedya.objects.filter(user=admin, aktif=True)
                    break
            except Exception as e:
                print(f"Error getting IletisimBilgisi for admin {admin.username}: {str(e)}")

    # Varsayılan iletişim bilgileri (hiçbir yönetici bilgisi bulunamazsa)
    default_iletisim = {
        'email': '<EMAIL>',
        'telefon': '+90 (212) 123 45 67',
        'adres': 'Seramik Atölyesi, Kadıköy, İstanbul',
        'calisma_saatleri': 'Pazartesi - Cumartesi: 10:00 - 18:00'
    }

    # Varsayılan sosyal medya hesapları
    default_sosyal_medya = [
        {'platform': 'instagram', 'url': '#', 'icon': 'bi-instagram'},
        {'platform': 'facebook', 'url': '#', 'icon': 'bi-facebook'},
        {'platform': 'twitter', 'url': '#', 'icon': 'bi-twitter'},
        {'platform': 'youtube', 'url': '#', 'icon': 'bi-youtube'},
        {'platform': 'linkedin', 'url': '#', 'icon': 'bi-linkedin'}
    ]

    # İletişim bilgileri varsa, varsayılan değerleri güncelle
    if iletisim_bilgisi:
        default_iletisim['email'] = iletisim_bilgisi.email
        default_iletisim['telefon'] = iletisim_bilgisi.telefon
        default_iletisim['adres'] = iletisim_bilgisi.adres
        if hasattr(iletisim_bilgisi, 'calisma_saatleri'):
            default_iletisim['calisma_saatleri'] = iletisim_bilgisi.calisma_saatleri

    # Sosyal medya hesapları varsa, varsayılan değerleri güncelle
    if sosyal_medya_hesaplari:
        # Sosyal medya hesaplarını platform adına göre sözlük olarak hazırla
        sosyal_medya_dict = {}
        for hesap in sosyal_medya_hesaplari:
            platform = hesap.platform.lower()
            sosyal_medya_dict[platform] = {
                'platform': platform,
                'url': hesap.url,
                'icon': f'bi-{platform}'
            }

        # Varsayılan sosyal medya listesini güncelle
        for i, hesap in enumerate(default_sosyal_medya):
            platform = hesap['platform']
            if platform in sosyal_medya_dict:
                default_sosyal_medya[i] = sosyal_medya_dict[platform]

    context = {
        'iletisim': default_iletisim,
        'sosyal_medya': default_sosyal_medya,
        'can_edit': request.user.is_authenticated and request.user.is_staff
    }

    return render(request, 'anasayfa/iletisim.html', context)


@login_required
def hakkimda_duzenle(request):
    # Birden fazla Hakkimda nesnesi olabilir, ilkini al
    hakkimda = Hakkimda.objects.filter(user=request.user).first()

    if not hakkimda:
        hakkimda = Hakkimda(user=request.user)

    if request.method == 'POST':
        # Eski resmi kaydet
        eski_resim = None
        if hakkimda.resim:
            eski_resim = hakkimda.resim.path

        form = HakkimdaForm(request.POST, request.FILES, instance=hakkimda)
        if form.is_valid():
            # Yeni form kaydedildi
            yeni_hakkimda = form.save()

            # Eğer resim değiştiyse ve eski resim varsa, eski resmi sil
            if eski_resim and (not yeni_hakkimda.resim or eski_resim != yeni_hakkimda.resim.path):
                import os
                if os.path.isfile(eski_resim):
                    os.remove(eski_resim)
                    print(f"Eski profil resmi silindi: {eski_resim}")

            messages.success(request, 'Hakkımda bilgileri başarıyla güncellendi.')
        else:
            messages.error(request, 'Lütfen formu doğru şekilde doldurun.')
    else:
        form = HakkimdaForm(instance=hakkimda)
        return render(request, 'anasayfa/hakkimda_duzenle.html', {
            'form': form,
            'hakkimda': hakkimda
        })

    return redirect('anasayfa:hakkimda')

@login_required
def egitim_ekle(request):
    # Hakkimda nesnesini al veya oluştur
    hakkimda = Hakkimda.objects.filter(user=request.user).first()
    if not hakkimda:
        hakkimda = Hakkimda.objects.create(user=request.user, baslik="", aciklama="")

    if request.method == 'POST':
        form = EgitimForm(request.POST)
        if form.is_valid():
            egitim = form.save(commit=False)
            egitim.hakkimda = hakkimda
            egitim.save()
            messages.success(request, 'Eğitim bilgisi başarıyla eklendi.')
        else:
            messages.error(request, 'Lütfen formu doğru şekilde doldurun.')
    return redirect('anasayfa:hakkimda_duzenle')

@login_required
def egitim_duzenle(request, pk):
    egitim = get_object_or_404(Egitim, pk=pk, hakkimda__user=request.user)

    if request.method == 'POST':
        form = EgitimForm(request.POST, instance=egitim)
        if form.is_valid():
            form.save()
            messages.success(request, 'Eğitim bilgisi başarıyla güncellendi.')
        else:
            messages.error(request, 'Lütfen formu doğru şekilde doldurun.')
    return redirect('anasayfa:hakkimda_duzenle')

@login_required
def egitim_sil(request, pk):
    egitim = get_object_or_404(Egitim, pk=pk, hakkimda__user=request.user)
    egitim.delete()
    messages.success(request, 'Eğitim bilgisi başarıyla silindi.')
    return redirect('anasayfa:hakkimda_duzenle')

@login_required
def iletisim_duzenle(request):
    iletisim, created = IletisimBilgisi.objects.get_or_create(user=request.user)

    if request.method == 'POST':
        form = IletisimBilgisiForm(request.POST, instance=iletisim)
        if form.is_valid():
            form.save()
            messages.success(request, 'İletişim bilgileri başarıyla güncellendi.')
            return redirect('anasayfa:iletisim')
    else:
        form = IletisimBilgisiForm(instance=iletisim)

    return render(request, 'anasayfa/iletisim_duzenle.html', {
        'form': form,
        'iletisim': iletisim
    })

@login_required
def sosyal_medya_ekle(request):
    if request.method == 'POST':
        form = SosyalMedyaForm(request.POST)
        if form.is_valid():
            sosyal_medya = form.save(commit=False)
            sosyal_medya.user = request.user
            sosyal_medya.save()
            messages.success(request, 'Sosyal medya hesabı başarıyla eklendi.')
            return redirect('anasayfa:iletisim')
    else:
        form = SosyalMedyaForm()

    return render(request, 'anasayfa/sosyal_medya_ekle.html', {'form': form})

@login_required
def sosyal_medya_duzenle(request, pk):
    sosyal_medya = get_object_or_404(SosyalMedya, pk=pk, user=request.user)

    if request.method == 'POST':
        form = SosyalMedyaForm(request.POST, instance=sosyal_medya)
        if form.is_valid():
            form.save()
            messages.success(request, 'Sosyal medya hesabı başarıyla güncellendi.')
            return redirect('anasayfa:iletisim')
    else:
        form = SosyalMedyaForm(instance=sosyal_medya)

    return render(request, 'anasayfa/sosyal_medya_duzenle.html', {'form': form})

@login_required
def sosyal_medya_sil(request, pk):
    sosyal_medya = get_object_or_404(SosyalMedya, pk=pk, user=request.user)
    sosyal_medya.delete()
    messages.success(request, 'Sosyal medya hesabı başarıyla silindi.')
    return redirect('anasayfa:iletisim')

# Inline Editing Views
@login_required
@require_POST
def hakkimda_inline_update(request):
    """Hakkımda sayfasındaki içeriği AJAX ile günceller"""
    try:
        # Form verilerini al
        content_type = request.POST.get('content_type')
        content_id = request.POST.get('content_id', '0')
        content = request.POST.get('content')

        print(f"Received update request: {content_type}, {content_id}, {content}")

        # Oturum açmış kullanıcının Hakkimda nesnesini al/oluştur
        # Birden fazla Hakkimda nesnesi olabilir, ilkini al veya yeni oluştur
        hakkimda = Hakkimda.objects.filter(user=request.user).first()
        if not hakkimda:
            hakkimda = Hakkimda.objects.create(user=request.user)
            created = True
        else:
            created = False

        # Hakkimda modelini kontrol et
        print(f"Hakkimda model fields: {[f.name for f in Hakkimda._meta.get_fields()]}")

        # İçerik tipine göre güncelleme yap
        updated = False

        # İçerik tiplerini Hakkimda modeli alanlarına eşleştir
        field_mapping = {
            'hakkimda_isim': 'isim',
            'hakkimda_unvan': 'unvan',
            'hakkimda_aciklama': 'aciklama',
            'hakkimda_email': 'email',
            'hakkimda_telefon': 'telefon',
            'hakkimda_konum': 'konum',
            'hakkimda_deneyim': 'deneyim',
            'hakkimda_diller': 'diller',
            'hakkimda_egitim': 'egitim',
            'hakkimda_yolculuk': 'sanat_yolculugu',
            'hakkimda_lisans': 'lisans',
            'hakkimda_yuksek_lisans': 'yuksek_lisans',
            'hakkimda_uluslararasi': 'uluslararasi_egitim',
            'atolye_aciklama': 'atolye_aciklama'
        }

        # İçerik tipine karşılık gelen model alanını bul
        model_field = field_mapping.get(content_type)

        if model_field:
            try:
                # Modelde bu alan var mı kontrol et
                if hasattr(hakkimda, model_field):
                    # Alanı güncelle
                    setattr(hakkimda, model_field, content)
                    hakkimda.save()
                    updated = True
                    print(f"Updated {model_field} with value: {content}")
                else:
                    print(f"Field {model_field} does not exist in Hakkimda model")
                    # Sadece başarılı yanıt döndür
            except Exception as e:
                print(f"Error updating {model_field}: {str(e)}")
        else:
            print(f"No mapping found for content_type: {content_type}")

        # Özel durumlar
        if content_type == 'hakkimda_aciklama':
            hakkimda.aciklama = content
            hakkimda.save()
            updated = True
            print(f"Updated aciklama with value: {content}")

        # Tüm içerik tipleri için başarılı yanıt döndür
        return JsonResponse({'success': True, 'updated': updated})

    except Exception as e:
        import traceback
        print(f"Error in hakkimda_inline_update: {str(e)}")
        print(traceback.format_exc())
        return JsonResponse({'success': False, 'error': str(e)})

@login_required
@require_POST
def iletisim_inline_update(request):
    """İletişim sayfasındaki içeriği AJAX ile günceller"""
    try:
        data = json.loads(request.body)
        content_type = data.get('content_type')
        content_id = data.get('content_id', 0)
        content = data.get('content')

        # Oturum açmış kullanıcının IletisimBilgisi nesnesini al/oluştur
        iletisim, created = IletisimBilgisi.objects.get_or_create(user=request.user)

        # İçerik tipine ve indeksine göre güncelleme yap
        if content_type == 'iletisim_bilgisi':
            # content_id değerine göre hangi alanı güncelleyeceğimizi belirle
            if int(content_id) == 0:  # Adres
                iletisim.adres = content
            elif int(content_id) == 1:  # E-posta
                iletisim.email = content
            elif int(content_id) == 2:  # Telefon
                iletisim.telefon = content
            elif int(content_id) == 3:  # Çalışma Saatleri
                iletisim.calisma_saatleri = content
            else:
                return JsonResponse({'success': False, 'error': 'Geçersiz içerik indeksi'})

            iletisim.save()
        elif content_type == 'iletisim_baslik':
            # Bu alan doğrudan modelde yok, ancak frontend'de gösteriliyor
            pass
        elif content_type == 'sosyal_medya_url':
            # Sosyal medya bağlantılarını güncelle
            try:
                sosyal_medya, created = SosyalMedya.objects.get_or_create(
                    user=request.user,
                    platform={
                        '0': 'instagram',
                        '1': 'facebook',
                        '2': 'twitter',
                        '3': 'youtube',
                        '4': 'linkedin'
                    }.get(str(content_id), 'instagram')
                )
                sosyal_medya.url = content
                sosyal_medya.save()
            except Exception as e:
                return JsonResponse({'success': False, 'error': f'Sosyal medya güncellenemedi: {str(e)}'})
        else:
            return JsonResponse({'success': False, 'error': 'Geçersiz içerik tipi'})

        return JsonResponse({'success': True})

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


# Create your views here.

def test(request):
    """Test sayfasını render eder. Bu sayfa tüm UI bileşenlerini gösterir."""
    return render(request, 'anasayfa/test.html')

