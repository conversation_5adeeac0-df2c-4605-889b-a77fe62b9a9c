{% extends 'base.html' %}
{% load static %}

{% block title %}Konuşma - {% for participant in conversation.participants.all %}{% if participant != request.user %}{{ participant.get_full_name|default:participant.username }}{% if not forloop.last %}, {% endif %}{% endif %}{% endfor %} - <PERSON><PERSON><PERSON>{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'assets/css/modern-theme.css' %}">
<link rel="stylesheet" href="{% static 'assets/css/form-styles.css' %}">
<link rel="stylesheet" href="{% static 'assets/css/messaging.css' %}">
{% endblock %}

{% block content %}
<section class="modern-section py-5">
    <div class="container">
        <!-- <PERSON><PERSON><PERSON><PERSON><PERSON> Bölümü -->
        <div class="modern-section-header text-center mb-4 wow fadeIn" data-wow-delay="0.2s">
            <h1 class="modern-title">
                {% for participant in conversation.participants.all %}
                    {% if participant != request.user %}
                        {{ participant.get_full_name|default:participant.username }}{% if not forloop.last %}, {% endif %}
                    {% endif %}
                {% endfor %}
            </h1>
            <p class="modern-subtitle">Mesajlaşma</p>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- Geri Dön Butonu -->
                <div class="mb-3">
                    <a href="{% url 'messaging:conversation_list' %}" class="modern-btn modern-btn-outline modern-btn-sm">
                        <i class="bi bi-arrow-left me-2"></i>Konuşmalara Dön
                    </a>
                </div>

                <!-- Mesaj Konteyneri -->
                <div class="modern-card wow fadeInUp" data-wow-delay="0.3s">
                    <div class="modern-card-header">
                        <h3><i class="bi bi-chat-dots me-2"></i>Mesajlar</h3>
                    </div>
                    <div class="modern-card-body p-0">
                        <!-- Mesaj Listesi -->
                        <div class="message-container" id="messageContainer">
                            {% for message in messages %}
                            <div class="message-item {% if message.sender == request.user %}own-message{% endif %}">
                                <div class="message-avatar">
                                    {{ message.sender.get_full_name.0|default:message.sender.username.0|upper }}
                                </div>
                                <div class="message-content">
                                    <div class="message-text">{{ message.content }}</div>
                                    <div class="message-time">
                                        {{ message.timestamp|date:"H:i" }}
                                        {% if message.sender == request.user %}
                                        <span class="message-actions ms-2">
                                            <a href="#" class="text-muted edit-message" data-message-id="{{ message.id }}" title="Düzenle">
                                                <i class="bi bi-pencil-square"></i>
                                            </a>
                                            <a href="#" class="text-muted delete-message ms-1" data-message-id="{{ message.id }}" title="Sil">
                                                <i class="bi bi-trash"></i>
                                            </a>
                                        </span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% empty %}
                            <div class="empty-state text-center py-4">
                                <i class="bi bi-chat-square-text display-4 text-muted mb-3"></i>
                                <h5 class="text-muted">Henüz mesaj yok</h5>
                                <p class="text-muted">İlk mesajı göndererek konuşmayı başlatın.</p>
                            </div>
                            {% endfor %}
                        </div>

                        <!-- Mesaj Gönderme Formu -->
                        <div class="message-form">
                            <form id="messageForm" class="d-flex align-items-center gap-3">
                                {% csrf_token %}
                                <div class="flex-grow-1">
                                    <textarea
                                        id="messageInput"
                                        class="form-control message-input"
                                        placeholder="Mesajınızı yazın..."
                                        rows="1"
                                        style="resize: none;"
                                    ></textarea>
                                </div>
                                <button type="submit" class="send-button" id="sendButton">
                                    <i class="bi bi-send"></i>
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/wow/1.1.2/wow.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        new WOW().init();

        const messageForm = document.getElementById('messageForm');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const messageContainer = document.getElementById('messageContainer');

        // Auto-resize textarea
        messageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        });

        // Enter to send (Shift+Enter for new line)
        messageInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // Form submit
        messageForm.addEventListener('submit', function(e) {
            e.preventDefault();
            sendMessage();
        });

        function sendMessage() {
            const content = messageInput.value.trim();
            if (!content) return;

            // Disable button during send
            sendButton.disabled = true;
            sendButton.innerHTML = '<i class="bi bi-hourglass-split"></i>';

            // Send via API
            fetch('{% url "messaging:api_message_create" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: JSON.stringify({
                    conversation: {{ conversation.id }},
                    content: content
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.id) {
                    // Add message to UI
                    addMessageToUI({
                        id: data.id,
                        sender: '{{ request.user.get_full_name|default:request.user.username }}',
                        content: content,
                        timestamp: new Date(),
                        is_own: true
                    });

                    messageInput.value = '';
                    messageInput.style.height = 'auto';
                    scrollToBottom();
                } else {
                    alert('Mesaj gönderilemedi: ' + (data.detail || 'Bilinmeyen hata'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Bir hata oluştu. Lütfen tekrar deneyin.');
            })
            .finally(() => {
                sendButton.disabled = false;
                sendButton.innerHTML = '<i class="bi bi-send"></i>';
            });
        }

        function addMessageToUI(message) {
            const messageElement = document.createElement('div');
            messageElement.className = `message-item ${message.is_own ? 'own-message' : ''}`;
            messageElement.innerHTML = `
                <div class="message-avatar">
                    ${message.sender.charAt(0).toUpperCase()}
                </div>
                <div class="message-content">
                    <div class="message-text">${message.content}</div>
                    <div class="message-time">
                        ${new Date(message.timestamp).toLocaleTimeString('tr-TR', {hour: '2-digit', minute: '2-digit'})}
                        ${message.is_own ? `
                        <span class="message-actions ms-2">
                            <a href="#" class="text-muted edit-message" data-message-id="${message.id}" title="Düzenle">
                                <i class="bi bi-pencil-square"></i>
                            </a>
                            <a href="#" class="text-muted delete-message ms-1" data-message-id="${message.id}" title="Sil">
                                <i class="bi bi-trash"></i>
                            </a>
                        </span>` : ''}
                    </div>
                </div>
            `;

            // Remove empty state if exists
            const emptyState = messageContainer.querySelector('.empty-state');
            if (emptyState) {
                emptyState.remove();
            }

            messageContainer.appendChild(messageElement);
        }

        function scrollToBottom() {
            messageContainer.scrollTop = messageContainer.scrollHeight;
        }

        // Message actions (edit/delete)
        messageContainer.addEventListener('click', function(e) {
            if (e.target.closest('.edit-message')) {
                e.preventDefault();
                const messageId = e.target.closest('.edit-message').dataset.messageId;
                const messageElement = e.target.closest('.message-item');
                const currentContent = messageElement.querySelector('.message-text').textContent;

                const newContent = prompt('Mesajı düzenle:', currentContent);
                if (newContent && newContent !== currentContent) {
                    // API call to edit message
                    console.log('Edit message:', messageId, newContent);
                }
            } else if (e.target.closest('.delete-message')) {
                e.preventDefault();
                const messageId = e.target.closest('.delete-message').dataset.messageId;

                if (confirm('Bu mesajı silmek istediğinizden emin misiniz?')) {
                    // API call to delete message
                    console.log('Delete message:', messageId);
                }
            }
        });

        // Initial scroll to bottom
        scrollToBottom();
    });
</script>
{% endblock %}