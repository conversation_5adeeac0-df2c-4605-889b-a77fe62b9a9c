{% extends 'base.html' %}

{% block title %}Konuşma: {% for participant in conversation.participants.all %}{% if participant != request.user %}{{ participant.username }}{% if not forloop.last %}, {% endif %}{% endif %}{% endfor %}{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2>Konuşma: {% for participant in conversation.participants.all %}{% if participant != request.user %}{{ participant.username }}{% if not forloop.last %}, {% endif %}{% endif %}{% endfor %}</h2>

    <div id="chat-log" class="border p-3 mb-3" style="height: 400px; overflow-y: scroll;">
        {% for message in messages %}
            <div class="message" data-message-id="{{ message.id }}">
                <p>
                    <strong>{{ message.sender.username }}:</strong> {{ message.content }}
                    {% if message.sender == request.user %}
                        <small class="text-muted ml-2">
                            <a href="#" class="edit-message" data-message-id="{{ message.id }}"><PERSON><PERSON><PERSON><PERSON></a> |
                            <a href="#" class="delete-message" data-message-id="{{ message.id }}">Sil</a>
                        </small>
                    {% endif %}
                </p>
            </div>
        {% empty %}
            <p>Bu konuşmada henüz mesaj yok.</p>
        {% endfor %}
    </div>

    <div class="input-group">
        <input type="text" id="chat-message-input" class="form-control" placeholder="Mesajınızı yazın...">
        <div class="input-group-append">
            <button id="chat-message-submit" class="btn btn-primary" type="button">Gönder</button>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    const roomName = JSON.parse(document.getElementById('room-name').textContent);
    const userId = {{ request.user.id }}; // Mevcut kullanıcının ID'si

    const chatSocket = new WebSocket(
        'ws://'
        + window.location.host
        + '/ws/chat/'
        + roomName
        + '/'
    );

    chatSocket.onmessage = function(e) {
        const data = JSON.parse(e.data);
        const chatLog = document.getElementById('chat-log');
        const messageType = data.type;

        if (messageType === 'chat_message') {
            const messageElement = document.createElement('div');
            messageElement.classList.add('message');
            messageElement.dataset.messageId = data.message_id; // Mesaj ID'sini ekle
            messageElement.innerHTML = `
                <p>
                    <strong>${data.sender}:</strong> ${data.message}
                    ${data.sender === '{{ request.user.username }}' ?
                        `<small class="text-muted ml-2">
                            <a href="#" class="edit-message" data-message-id="${data.message_id}">Düzenle</a> |
                            <a href="#" class="delete-message" data-message-id="${data.message_id}">Sil</a>
                        </small>` : ''}
                </p>
            `;
            chatLog.appendChild(messageElement);
            chatLog.scrollTop = chatLog.scrollHeight; // Scroll to bottom
        } else if (messageType === 'message_edited') {
            const messageElement = document.querySelector(`.message[data-message-id="${data.message_id}"]`);
            if (messageElement) {
                // Mesaj içeriğini güncelle
                const contentElement = messageElement.querySelector('p strong').nextSibling;
                contentElement.textContent = `: ${data.new_content}`;
            }
        } else if (messageType === 'message_deleted') {
            const messageElement = document.querySelector(`.message[data-message-id="${data.message_id}"]`);
            if (messageElement) {
                messageElement.remove(); // Mesajı kaldır
            }
        } else if (messageType === 'user_status') {
            // Kullanıcı durumu güncellemelerini burada işleyebilirsiniz
            console.log(`User ${data.user_id} is now ${data.status}`);
            // Örneğin, kullanıcı listesindeki durumu güncelleyebilirsiniz.
        }
    };

    chatSocket.onclose = function(e) {
        console.error('Chat socket closed unexpectedly');
    };

    document.querySelector('#chat-message-input').focus();
    document.querySelector('#chat-message-input').onkeyup = function(e) {
        if (e.keyCode === 13) {  // enter, return
            document.querySelector('#chat-message-submit').click();
        }
    };

    document.querySelector('#chat-message-submit').onclick = function(e) {
        const messageInputDom = document.querySelector('#chat-message-input');
        const message = messageInputDom.value;
        chatSocket.send(JSON.stringify({
            'type': 'chat_message', // Mesaj tipini belirt
            'message': message
        }));
        messageInputDom.value = '';
    };

    // Mesaj düzenleme ve silme event listener'ları
    document.getElementById('chat-log').addEventListener('click', function(e) {
        if (e.target.classList.contains('edit-message')) {
            e.preventDefault();
            const messageId = e.target.dataset.messageId;
            const messageElement = e.target.closest('.message');
            const currentContent = messageElement.querySelector('p strong').nextSibling.textContent.trim().substring(1).trim(); // Mesaj içeriğini al

            const newContent = prompt('Mesajı düzenle:', currentContent);
            if (newContent !== null && newContent !== currentContent) {
                chatSocket.send(JSON.stringify({
                    'type': 'edit_message',
                    'message_id': messageId,
                    'content': newContent
                }));
            }
        } else if (e.target.classList.contains('delete-message')) {
            e.preventDefault();
            const messageId = e.target.dataset.messageId;
            if (confirm('Bu mesajı silmek istediğinizden emin misiniz?')) {
                chatSocket.send(JSON.stringify({
                    'type': 'delete_message',
                    'message_id': messageId
                }));
            }
        }
    });

</script>
{% endblock %}

{% block extra_body %}
<textarea id="room-name" style="display: none;">{{ conversation.id }}</textarea>
{% endblock %}