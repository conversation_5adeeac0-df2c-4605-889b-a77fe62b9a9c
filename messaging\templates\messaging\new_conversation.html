{% extends 'base.html' %}

{% block title %}Yeni <PERSON>şma Başlat{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2><PERSON><PERSON>ş<PERSON> Başlat</h2>
    <form method="post" action="{% url 'messaging:api_create_conversation' %}">
        {% csrf_token %}
        <div class="form-group">
            <label for="participants">Kullanıcı Seçin:</label>
            <select multiple class="form-control" id="participants" name="participants">
                {% for user in users %}
                    <option value="{{ user.id }}">{{ user.username }}</option>
                {% endfor %}
            </select>
            <small class="form-text text-muted">Mesajlaşmak istediğiniz kullanıcıları seçin (Ctrl/Cmd tuşu ile çoklu seçim yapabilirsiniz).</small>
        </div>
        <button type="submit" class="btn btn-primary">Konuşma Başlat</button>
    </form>
</div>
{% endblock %}