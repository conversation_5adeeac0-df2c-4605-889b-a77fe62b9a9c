{% extends 'base.html' %}
{% load static %}

{% block title %}Yeni Konuşma Başlat - Küp Cadısı{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'assets/css/modern-theme.css' %}">
<link rel="stylesheet" href="{% static 'assets/css/form-styles.css' %}">
<link rel="stylesheet" href="{% static 'assets/css/messaging.css' %}">
{% endblock %}

{% block content %}
<section class="modern-section py-5">
    <div class="container">
        <!-- <PERSON><PERSON><PERSON><PERSON><PERSON> Bölümü -->
        <div class="modern-section-header text-center mb-5 wow fadeIn" data-wow-delay="0.2s">
            <h1 class="modern-title">Yeni Konuşma Başlat</h1>
            <p class="modern-subtitle">Mesajlaşmak istediğiniz kullanıcıları seçin</p>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="modern-card wow fadeInUp" data-wow-delay="0.3s">
                    <div class="modern-card-header">
                        <h3><i class="bi bi-people me-2"></i>Kullanıcı Seçimi</h3>
                    </div>
                    <div class="modern-card-body">
                        <form id="conversationForm" method="post">
                            {% csrf_token %}

                            <div class="row g-3">
                                {% for user in users %}
                                <div class="col-md-6">
                                    <div class="user-select-item" data-user-id="{{ user.id }}">
                                        <div class="d-flex align-items-center">
                                            <div class="user-avatar me-3">
                                                {{ user.get_full_name.0|default:user.username.0|upper }}
                                            </div>
                                            <div class="user-info flex-grow-1">
                                                <h6 class="mb-1">{{ user.get_full_name|default:user.username }}</h6>
                                                <small class="text-muted">@{{ user.username }}</small>
                                                {% if user.profil.is_approved %}
                                                <span class="badge bg-success ms-2">Onaylı</span>
                                                {% endif %}
                                                {% if user.is_staff %}
                                                <span class="badge bg-primary ms-2">Yönetici</span>
                                                {% endif %}
                                            </div>
                                            <div class="user-select-check">
                                                <i class="bi bi-check-circle-fill text-success" style="display: none;"></i>
                                                <i class="bi bi-circle text-muted"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% empty %}
                                <div class="col-12">
                                    <div class="empty-state text-center py-4">
                                        <i class="bi bi-people display-4 text-muted mb-3"></i>
                                        <h5 class="text-muted">Mesajlaşabileceğiniz kullanıcı bulunamadı</h5>
                                        <p class="text-muted">Henüz onaylanmış kullanıcı bulunmuyor.</p>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>

                            <div class="selected-users mt-4" style="display: none;">
                                <h6><i class="bi bi-check2-square me-2"></i>Seçilen Kullanıcılar:</h6>
                                <div id="selectedUsersList" class="d-flex flex-wrap gap-2 mt-2"></div>
                            </div>

                            <div class="form-actions mt-4">
                                <button type="submit" class="modern-btn" id="startConversationBtn" disabled>
                                    <i class="bi bi-chat-dots me-2"></i>Konuşma Başlat
                                </button>
                                <a href="{% url 'messaging:conversation_list' %}" class="modern-btn modern-btn-outline ms-2">
                                    <i class="bi bi-arrow-left me-2"></i>Geri Dön
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/wow/1.1.2/wow.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        new WOW().init();

        const userItems = document.querySelectorAll('.user-select-item');
        const selectedUsers = new Set();
        const startBtn = document.getElementById('startConversationBtn');
        const selectedUsersContainer = document.querySelector('.selected-users');
        const selectedUsersList = document.getElementById('selectedUsersList');
        const form = document.getElementById('conversationForm');

        userItems.forEach(item => {
            item.addEventListener('click', function() {
                const userId = this.dataset.userId;
                const checkIcon = this.querySelector('.bi-check-circle-fill');
                const circleIcon = this.querySelector('.bi-circle');

                if (selectedUsers.has(userId)) {
                    // Kullanıcıyı kaldır
                    selectedUsers.delete(userId);
                    this.classList.remove('selected');
                    checkIcon.style.display = 'none';
                    circleIcon.style.display = 'inline';
                } else {
                    // Kullanıcıyı ekle
                    selectedUsers.add(userId);
                    this.classList.add('selected');
                    checkIcon.style.display = 'inline';
                    circleIcon.style.display = 'none';
                }

                updateSelectedUsers();
            });
        });

        function updateSelectedUsers() {
            if (selectedUsers.size > 0) {
                startBtn.disabled = false;
                selectedUsersContainer.style.display = 'block';

                selectedUsersList.innerHTML = '';
                selectedUsers.forEach(userId => {
                    const userItem = document.querySelector(`[data-user-id="${userId}"]`);
                    const userName = userItem.querySelector('h6').textContent;

                    const badge = document.createElement('span');
                    badge.className = 'badge bg-primary';
                    badge.textContent = userName;
                    selectedUsersList.appendChild(badge);
                });
            } else {
                startBtn.disabled = true;
                selectedUsersContainer.style.display = 'none';
            }
        }

        form.addEventListener('submit', function(e) {
            e.preventDefault();

            if (selectedUsers.size === 0) {
                alert('Lütfen en az bir kullanıcı seçin.');
                return;
            }

            // API'ye POST isteği gönder
            const formData = new FormData();
            formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);
            formData.append('participants', Array.from(selectedUsers));

            fetch('{% url "messaging:api_create_conversation" %}', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.conversation_id) {
                    window.location.href = `/messaging/conversations/${data.conversation_id}/`;
                } else if (data.id) {
                    window.location.href = `/messaging/conversations/${data.id}/`;
                } else {
                    alert('Konuşma başlatılamadı: ' + (data.detail || 'Bilinmeyen hata'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Bir hata oluştu. Lütfen tekrar deneyin.');
            });
        });
    });
</script>
{% endblock %}