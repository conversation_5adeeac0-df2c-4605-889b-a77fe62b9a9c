from django.urls import path
from .views import (
    ConversationListView, ConversationDetailView, MessageCreateView,
    AnnouncementListView, MarkAnnouncementAsReadView, ConversationCreateView, AnnouncementCreateView, # Yeni API görünümlerini import ediyoruz
    conversation_list_view, conversation_detail_view, announcement_list_view, new_conversation_view, create_announcement_view # Yeni HTML görünümlerini import ediyoruz
)

app_name = 'messaging'

urlpatterns = [
    # HTML Sayfa URL'leri
    path('conversations/', conversation_list_view, name='conversation_list'),
    path('conversations/<int:pk>/', conversation_detail_view, name='conversation_detail'),
    path('announcements/', announcement_list_view, name='announcement_list'),
    path('new/', new_conversation_view, name='new_conversation'), # Yeni konuşma başlatma sayfası
    path('announcements/create/', create_announcement_view, name='create_announcement'), # <PERSON><PERSON><PERSON> oluşturma sayfası (yöneticiler için)

    # API URL'leri
    path('api/conversations/', ConversationListView.as_view(), name='api_conversation_list'),
    path('api/conversations/create/', ConversationCreateView.as_view(), name='api_create_conversation'), # Yeni konuşma oluşturma API
    path('api/conversations/<int:pk>/', ConversationDetailView.as_view(), name='api_conversation_detail'),
    path('api/messages/create/', MessageCreateView.as_view(), name='api_message_create'),
    path('api/announcements/', AnnouncementListView.as_view(), name='api_announcement_list'),
    path('api/announcements/create/', AnnouncementCreateView.as_view(), name='api_announcement_create'), # Yeni duyuru oluşturma API
    path('api/announcements/<int:pk>/mark_read/', MarkAnnouncementAsReadView.as_view(), name='api_mark_announcement_read'),
]