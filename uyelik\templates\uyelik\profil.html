{% extends 'uyelik/profil_base.html' %}
{% load static %}

{% block title %}Profil - Küp Cadısı{% endblock %}

{% block profile_title %}Profil <PERSON>sı{% endblock %}
{% block profile_subtitle %}Kişisel bilgilerinizi ve çalışmalarınızı yönetin{% endblock %}

{% block profile_tab %}active{% endblock %}

{% block profile_extra_css %}
<link rel="stylesheet" href="{% static 'assets/css/modern-theme.css' %}">
<style>
    /* <PERSON>alış<PERSON> Kartları Stilleri */
    .modern-card-image {
        position: relative;
        height: 200px;
        overflow: hidden;
        border-top-left-radius: 16px;
        border-top-right-radius: 16px;
    }

    .modern-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s ease;
    }

    .modern-card:hover .modern-image {
        transform: scale(1.05);
    }

    .modern-card-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .modern-card:hover .modern-card-overlay {
        opacity: 1;
    }

    .modern-btn-circle {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background-color: var(--color-brown);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        transition: all 0.3s ease;
        transform: translateY(20px);
        opacity: 0;
    }

    .modern-card:hover .modern-btn-circle {
        transform: translateY(0);
        opacity: 1;
    }

    .modern-btn-circle:hover {
        background-color: var(--color-navy);
        transform: scale(1.1);
        color: white;
    }

    .modern-badge-corner {
        position: absolute;
        top: var(--space-md);
        right: var(--space-md);
        background-color: var(--color-moss);
        color: white;
        padding: var(--space-xxs) var(--space-md);
        border-radius: 20px;
        font-size: var(--font-size-xs);
        font-weight: var(--font-weight-medium);
        box-shadow: 0 2px 5px rgba(var(--color-brown-rgb), 0.2);
    }

    .modern-card-title {
        color: var(--color-brown);
        font-weight: var(--font-weight-semibold);
        margin-bottom: var(--space-xs);
    }

    .modern-card-text {
        color: var(--text-color);
        margin-bottom: var(--space-md);
        line-height: 1.5;
    }

    /* Çalışma kartları için ek stiller - modern-card sınıfını kullanmalıyız */
    /* Bu stil artık modern-theme.css dosyasında tanımlanmalıdır */
    /* .modern-card.work-card şeklinde kullanılmalıdır */

    .modern-card-body {
        padding: var(--space-lg);
    }

    /* Özel buton stilleri - modern-btn sınıfını kullanıyoruz */
    /* Bu stil artık modern-theme.css dosyasında tanımlanmıştır */
</style>
{% endblock %}

{% block profile_content %}
{% if is_own_profile and not user.profil.is_approved and not user.is_staff and not user.is_superuser %}
<div class="alert alert-warning" role="alert">
    <i class="bi bi-exclamation-triangle-fill me-2"></i>Üyeliğiniz yönetici tarafından onaylanmadan profil bilgilerinizi düzenleyemezsiniz.
</div>
{% endif %}

<!-- İçerik Alanı -->
<div class="modern-tab-content mt-4">
    <div class="modern-card">
        <div class="modern-card-header">
            <h3 id="tab-title"><i class="bi bi-person-lines-fill me-2"></i>Profil Bilgileri</h3>
        </div>
        <div class="modern-card-body">
            <div id="tab-content-container">
                <!-- İçerik AJAX ile yüklenecek -->
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Yükleniyor...</span>
                    </div>
                    <p class="mt-3">İçerik yükleniyor...</p>
                </div>
            </div>
        </div>
    </div>
</div>

{% if is_own_profile %}
<!-- Profil Fotoğrafı Modal -->
<div class="modal fade" id="profilePictureModal" tabindex="-1" aria-labelledby="profilePictureModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modern-modal">
            <div class="modern-modal-header">
                <h5 class="modern-modal-title" id="profilePictureModalLabel"><i class="bi bi-camera me-2"></i>Profil Fotoğrafını Güncelle</h5>
                <button type="button" class="modern-close-btn" data-bs-dismiss="modal" aria-label="Kapat">
                    <i class="bi bi-x-lg"></i>
                </button>
            </div>
            <div class="modern-modal-body">
                <form id="profilePictureForm" enctype="multipart/form-data">
                    <div class="mb-4">
                        <label for="profilePictureInput" class="modern-form-label">Yeni Profil Fotoğrafı Seçin</label>
                        <input class="form-control modern-form-control" type="file" id="profilePictureInput" name="profile_picture" accept="image/*">
                    </div>
                    <div class="text-center mt-4">
                        <button type="submit" class="modern-btn"><i class="bi bi-cloud-upload me-2"></i>Yükle</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Kapak Fotoğrafı Modal -->
<div class="modal fade" id="coverPhotoModal" tabindex="-1" aria-labelledby="coverPhotoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modern-modal">
            <div class="modern-modal-header">
                <h5 class="modern-modal-title" id="coverPhotoModalLabel"><i class="bi bi-image me-2"></i>Kapak Fotoğrafını Güncelle</h5>
                <button type="button" class="modern-close-btn" data-bs-dismiss="modal" aria-label="Kapat">
                    <i class="bi bi-x-lg"></i>
                </button>
            </div>
            <div class="modern-modal-body">
                <form id="coverPhotoForm" enctype="multipart/form-data">
                    <div class="mb-4">
                        <label for="coverPhotoInput" class="modern-form-label">Yeni Kapak Fotoğrafı Seçin</label>
                        <input class="form-control modern-form-control" type="file" id="coverPhotoInput" name="cover_photo" accept="image/*">
                    </div>
                    <div class="text-center mt-4">
                        <button type="submit" class="modern-btn"><i class="bi bi-cloud-upload me-2"></i>Yükle</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endif %}
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    $(document).ready(function() {
        // URL'den aktif sekmeyi al
        function getActiveTabFromURL() {
            var hash = window.location.hash;
            return hash ? hash.substring(1) : 'profile'; // Varsayılan olarak 'profile'
        }

        // URL'yi güncelle
        function updateURLWithTab(tabId) {
            window.location.hash = tabId;
        }

        // Sekme içeriğini yükle
        function loadTabContent(tabId) {
            var contentDiv = $('#tab-content-container');
            var titleElement = $('#tab-title');
            var loadingHtml = '<div class="text-center py-5"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Yükleniyor...</span></div><p class="mt-3">İçerik yükleniyor...</p></div>';

            contentDiv.html(loadingHtml);

            // Sekme başlığını güncelle
            if (tabId === 'profile') {
                titleElement.html('<i class="bi bi-person-lines-fill me-2"></i>Profil Bilgileri');
            } else if (tabId === 'about') {
                titleElement.html('<i class="bi bi-info-circle me-2"></i>Hakkımda');
            } else if (tabId === 'works') {
                titleElement.html('<i class="bi bi-journal-text me-2"></i>Çalışmalarım');
            } else if (tabId === 'friends') {
                titleElement.html('<i class="bi bi-people me-2"></i>Arkadaşlar');
            }

            var url = '';
            if (tabId === 'profile') {
                // Profil sekmesi için içeriği doğrudan göster
                showProfileContent();
                return;
            } else if (tabId === 'about') {
                url = '{% if is_own_profile %}{% url "uyelik:ajax_profil_hakkimda" %}{% else %}{% url "uyelik:ajax_kullanici_hakkinda" viewed_user.username %}{% endif %}';
            } else if (tabId === 'works') {
                url = '{% if is_own_profile %}{% url "uyelik:ajax_profil_calismalarim" %}{% else %}{% url "uyelik:ajax_kullanici_calismalar" viewed_user.username %}{% endif %}';
            } else if (tabId === 'friends') {
                url = '{% if is_own_profile %}{% url "uyelik:ajax_profil_arkadaslar" %}{% else %}{% url "uyelik:ajax_kullanici_arkadaslar" viewed_user.username %}{% endif %}';
            }

            $.ajax({
                url: url,
                type: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(response) {
                    // Sadece içerik kısmını al
                    var content;
                    if (tabId === 'works') {
                        // Çalışmalar için özel işleme
                        content = $(response).find('.modern-card .row').html();
                    } else if (tabId === 'about') {
                        // Hakkımda sekmesi için özel işleme
                        content = $(response).find('.hakkimda-content-container').html();
                    } else {
                        // Diğer sekmeler için
                        content = $(response).find('.modern-card-body').html();
                    }

                    if (!content || content.trim() === '') {
                        // İçerik bulunamadıysa tüm sayfayı göster
                        content = '<div class="alert alert-warning">İçerik düzgün yüklenemedi. <a href="' + url + '" class="alert-link">Tam sayfayı görüntülemek için tıklayın</a>.</div>';
                    }

                    contentDiv.html(content);

                    // Yüklenen içerikteki olayları yeniden bağla
                    if (tabId === 'works') {
                        // Çalışmalar sekmesindeki butonlar için
                        contentDiv.find('.btn').on('click', function(e) {
                            // Normal davranışı koru
                        });
                    }

                    // WOW.js animasyonlarını yeniden başlat
                    if (typeof WOW !== 'undefined') {
                        new WOW().init();
                    }
                },
                error: function() {
                    contentDiv.html('<div class="alert alert-danger">İçerik yüklenirken bir hata oluştu. <a href="' + url + '" class="alert-link">Tam sayfayı görüntülemek için tıklayın</a>.</div>');
                }
            });
        }

        // Profil sekmesi içeriğini göster
        function showProfileContent() {
            var contentDiv = $('#tab-content-container');
            var profileContent = `
                <div class="row">
                    <div class="col-lg-6 col-md-12 mb-3 mb-lg-0">
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="bi bi-envelope"></i>
                            </div>
                            <div class="info-content">
                                <span class="info-title">E-posta</span>
                                <span class="info-value">
                                    {% if is_own_profile %}
                                        {{ user.email }}
                                    {% else %}
                                        {% if profil.email_public %}
                                            {{ viewed_user.email }}
                                        {% else %}
                                            <em class="text-muted">Gizli</em>
                                        {% endif %}
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-12">
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="bi bi-calendar-date"></i>
                            </div>
                            <div class="info-content">
                                <span class="info-title">Üyelik Tarihi</span>
                                <span class="info-value">
                                    {% if is_own_profile %}
                                        {{ user.date_joined|date:"d F Y" }}
                                    {% else %}
                                        {{ viewed_user.date_joined|date:"d F Y" }}
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4 d-flex flex-column flex-md-row gap-2 gap-md-3">
                    <a href="#about" class="modern-btn modern-btn-outline tab-link" data-tab="about">
                        <i class="bi bi-info-circle me-1"></i> Hakkımda
                    </a>
                    <a href="#works" class="modern-btn modern-btn-outline tab-link" data-tab="works">
                        <i class="bi bi-journal-text me-1"></i> Çalışmaları Görüntüle
                    </a>
                </div>
            `;
            contentDiv.html(profileContent);

            // Yeni eklenen tab linklerine olay dinleyicisi ekle
            $('.tab-link').click(function(e) {
                e.preventDefault();
                var tabId = $(this).data('tab');
                activateTab(tabId);
            });
        }

        // Sekmeyi aktif et
        function activateTab(tabId) {
            // Aktif sekme sınıfını kaldır
            $('.modern-tab').removeClass('active');
            // İlgili sekmeye aktif sınıfı ekle
            $('.modern-tab[data-tab="' + tabId + '"]').addClass('active');

            // Sekme içeriğini yükle
            loadTabContent(tabId);

            // URL'yi güncelle
            updateURLWithTab(tabId);
        }

        // Sekme değiştirme işlevi
        $('.modern-tab').click(function(e) {
            e.preventDefault();
            var tabId = $(this).data('tab');
            activateTab(tabId);
        });

        // Sayfa yüklenirken URL'den aktif sekmeyi al ve aktif et
        var activeTab = getActiveTabFromURL();
        activateTab(activeTab);

        // Profil fotoğrafı güncelleme
        $('#profilePictureForm').submit(function(e) {
            e.preventDefault();

            var formData = new FormData(this);

            $.ajax({
                url: '/uyelik/profil/foto-guncelle/',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                headers: {
                    'X-CSRFToken': '{{ csrf_token }}'
                },
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert('Profil fotoğrafı güncellenirken bir hata oluştu: ' + response.error);
                    }
                },
                error: function(xhr, status, error) {
                    alert('Profil fotoğrafı güncellenirken bir hata oluştu.');
                }
            });
        });

        // Sayfa geçişlerinde hash değişikliğini dinle
        $(window).on('hashchange', function() {
            var activeTab = getActiveTabFromURL();
            activateTab(activeTab);
        });
    });
</script>




{% endblock %}
