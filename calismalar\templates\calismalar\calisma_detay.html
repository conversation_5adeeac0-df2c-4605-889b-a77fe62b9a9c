{% extends 'base.html' %}
{% load static %}

{% block title %}{{ calisma.baslik }} - <PERSON><PERSON><PERSON>{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@9/swiper-bundle.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/glightbox/dist/css/glightbox.min.css">
{% endblock %}

{% block content %}
<main class="modern-section py-5">
    <div class="container modern-container">
        <!-- CSRF Token for AJAX requests -->
        {% csrf_token %}

        <!-- Ba<PERSON><PERSON><PERSON><PERSON> Bölümü -->
        <div class="modern-header mb-5 wow fadeIn" data-wow-delay="0.2s">
            <h1 class="autumn-heading display-4 fw-bold mb-3">{{ calisma.baslik }}</h1>
            <div class="modern-meta text-center">

            </div>
        </div>

        <div class="row g-4 mb-5">
            <!-- Çalışma Görselleri -->
            <div class="col-lg-8 wow fadeInLeft" data-wow-delay="0.3s">
                <div class="modern-card mb-4">
                    {% if calisma.fotograflar.exists %}
                    <div class="swiper product-slider">
                        <div class="swiper-wrapper">
                            {% for fotograf in calisma.fotograflar.all %}
                            <div class="swiper-slide">
                                <div class="image-container">
                                    <img src="{{ fotograf.fotograf.url }}" alt="{{ calisma.baslik }}" class="modern-image">
                                    <a href="{{ fotograf.fotograf.url }}" class="modern-btn-circle position-absolute top-50 start-50 translate-middle" title="{{ calisma.baslik }}" data-gallery="calisma-gallery">
                                        <i class="bi bi-zoom-in"></i>
                                    </a>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        <div class="swiper-pagination"></div>
                        <div class="swiper-button-next"></div>
                        <div class="swiper-button-prev"></div>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <img src="{% static 'img/no-image.jpg' %}" alt="Görsel Yok" class="img-fluid">
                        <p class="mt-3 text-muted">Bu çalışma için henüz görsel eklenmemiş.</p>
                    </div>
                    {% endif %}
                </div>

                <!-- Çalışma Açıklaması -->
                <div class="modern-card featured-content-card mb-4">
                    <div class="featured-card-header">
                        <h3 class="modern-title"><i class="bi bi-file-text me-2"></i>Açıklama</h3>
                    </div>
                    <div class="modern-card-body">
                        <div class="modern-content">
                            {{ calisma.aciklama|linebreaks }}
                        </div>

                        <!-- Paylaşım -->
                        <div class="modern-actions" style="margin-top: var(--space-lg) !important;">
                            <div class="d-flex justify-content-end align-items-center">
                                <div class="share-buttons">
                                    {% for hesap in sosyal_medya %}
                                    {% if hesap.platform == 'facebook' %}
                                    <a href="{{ hesap.url }}" target="_blank" class="social-link moss-icon">
                                        <i class="bi bi-facebook"></i>
                                    </a>
                                    {% elif hesap.platform == 'twitter' %}
                                    <a href="{{ hesap.url }}" target="_blank" class="social-link moss-icon">
                                        <i class="bi bi-twitter"></i>
                                    </a>
                                    {% elif hesap.platform == 'instagram' %}
                                    <a href="{{ hesap.url }}" target="_blank" class="social-link moss-icon">
                                        <i class="bi bi-instagram"></i>
                                    </a>
                                    {% elif hesap.platform == 'whatsapp' %}
                                    <a href="{{ hesap.url }}" target="_blank" class="social-link moss-icon">
                                        <i class="bi bi-whatsapp"></i>
                                    </a>
                                    {% elif hesap.platform == 'youtube' %}
                                    <a href="{{ hesap.url }}" target="_blank" class="social-link moss-icon">
                                        <i class="bi bi-youtube"></i>
                                    </a>
                                    {% elif hesap.platform == 'linkedin' %}
                                    <a href="{{ hesap.url }}" target="_blank" class="social-link moss-icon">
                                        <i class="bi bi-linkedin"></i>
                                    </a>
                                    {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Yan Bilgiler -->
            <div class="col-lg-4">
                <!-- Çalışma Bilgileri - Özellikli Kart -->
                <div class="modern-card modern-card-featured mb-4 wow fadeInRight" data-wow-delay="0.3s">
                    <div class="modern-card-header">
                        <h3><i class="bi bi-info-square me-2 text-white"></i>Çalışma Bilgileri</h3>
                    </div>
                    <div class="modern-card-body">
                        <ul class="featured-info-list">
                            <li class="info-item modern-list-item">
                                <div class="info-icon autumn-icon">
                                    <i class="bi bi-person-badge"></i>
                                </div>
                                <div class="info-content">
                                    <span class="info-title">Oluşturan</span>
                                    <span class="info-value">
                                        <a href="{% url 'uyelik:kullanici_profil' calisma.user.username %}" class="creator-highlight">
                                            <i class="bi bi-person-circle me-1"></i>
                                            {{ calisma.user.get_full_name|default:calisma.user.username }}
                                        </a>
                                    </span>
                                </div>
                            </li>
                            <li class="info-item modern-list-item">
                                <div class="info-icon autumn-icon">
                                    <i class="bi bi-calendar-event"></i>
                                </div>
                                <div class="info-content">
                                    <span class="info-title">Tarih</span>
                                    <span class="info-value">{{ calisma.olusturma_tarihi|date:"d.m.Y" }}</span>
                                </div>
                            </li>
                            {% if calisma.kategori %}
                            <li class="info-item modern-list-item">
                                <div class="info-icon autumn-icon">
                                    <i class="bi bi-grid-3x3-gap"></i>
                                </div>
                                <div class="info-content">
                                    <span class="info-title">Kategori</span>
                                    <span class="info-value">
                                        <a href="{% url 'calismalar:kategori_calismalari' calisma.kategori.slug %}" class="category-link">
                                            <i class="bi bi-tag me-1"></i>{{ calisma.kategori.ad }}
                                        </a>
                                    </span>
                                </div>
                            </li>
                            {% endif %}
                            <li class="info-item modern-list-item">
                                <div class="info-icon autumn-icon">
                                    <i class="bi bi-eye-fill"></i>
                                </div>
                                <div class="info-content">
                                    <span class="info-title">Görüntülenme</span>
                                    <span class="info-value">
                                        <span class="badge bg-light text-dark">
                                            <i class="bi bi-graph-up me-1"></i>{{ calisma.goruntulenme_sayisi }}
                                        </span>
                                    </span>
                                </div>
                            </li>
                            <li class="info-item modern-list-item">
                                <div class="info-icon autumn-icon like-icon{% if kullanici_begendi %} active{% endif %}" data-calisma-id="{{ calisma.id }}">
                                    <i class="bi bi-heart{% if kullanici_begendi %}-fill{% endif %}"></i>
                                </div>
                                <div class="info-content">
                                    <span class="info-title">Beğeni</span>
                                    <span class="info-value">
                                        <span class="badge bg-light text-dark">
                                            <i class="bi bi-hand-thumbs-up me-1"></i><span id="like-count">{{ calisma.begeni_sayisi }}</span>
                                        </span>
                                    </span>
                                </div>
                            </li>
                        </ul>

                        {% if calisma.tamamlanma_yuzdesi %}
                        <div class="completion-status" style="margin-top: var(--space-lg) !important;">
                            <h5 style="margin-bottom: var(--space-md) !important;"><i class="bi bi-graph-up me-2"></i>Proje Tamamlanma Durumu</h5>
                            <div class="progress autumn-progress">
                                <div class="progress-bar" role="progressbar"
                                     style="width: {{ calisma.tamamlanma_yuzdesi|default:0 }}%"
                                     data-progress="{{ calisma.tamamlanma_yuzdesi|default:0 }}">
                                    {{ calisma.tamamlanma_yuzdesi|default:0 }}%
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Etiketler -->
                <div class="modern-card modern-card-sky mb-4 wow fadeInRight" data-wow-delay="0.4s">
                    <div class="modern-card-header">
                        <h3><i class="bi bi-tags me-2 text-white"></i>Etiketler</h3>
                    </div>
                    <div class="modern-card-body">
                        {% if calisma.etiketler.exists %}
                        <div class="modern-tag-cloud">
                            {% for etiket in calisma.etiketler.all %}
                            <a href="{% url 'calismalar:etiket_calismalari' etiket.slug %}" class="modern-tag autumn-tag">{{ etiket.ad }}</a>
                            {% endfor %}
                        </div>
                        {% else %}
                        <p class="text-muted">Bu çalışma için henüz etiket eklenmemiş.</p>
                        {% endif %}
                    </div>
                </div>

                <!-- Benzer Çalışmalar -->
                <div class="modern-card modern-card-navy mb-4 wow fadeInRight" data-wow-delay="0.5s">
                    <div class="modern-card-header">
                        <h3><i class="bi bi-collection me-2 text-white"></i>Benzer Çalışmalar</h3>
                    </div>
                    <div class="modern-card-body">
                        <div class="modern-sidebar-list">
                            {% for benzer in benzer_calismalar %}
                            <a href="{% url 'calismalar:calisma_detay' benzer.slug %}" class="modern-sidebar-item autumn-sidebar-item">
                                <div class="small-thumbnail autumn-thumbnail">
                                    {% if benzer.fotograflar.exists %}
                                    <img src="{{ benzer.fotograflar.first.fotograf.url }}" alt="{{ benzer.baslik }}">
                                    {% else %}
                                    <img src="{% static 'img/placeholder.jpg' %}" alt="Görsel Yok">
                                    {% endif %}
                                </div>
                                <div class="sidebar-content">
                                    <h6 class="autumn-title text-navy">{{ benzer.baslik }}</h6>
                                    <span class="text-muted" style="font-size: var(--font-size-sm);">{{ benzer.olusturma_tarihi|date:"d.m.Y" }}</span>
                                </div>
                            </a>
                            {% empty %}
                            <div class="text-muted text-center" style="padding-top: var(--space-md) !important; padding-bottom: var(--space-md) !important;">
                                <i class="bi bi-emoji-neutral display-4"></i>
                                <p style="margin-top: var(--space-xs) !important;">Benzer çalışma bulunamadı.</p>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>

                <!-- Yönetim Paneli (Sadece Yöneticiler) -->
                {% if user.is_authenticated and user.is_superuser %}
                <div class="modern-card admin-panel-card mb-4 wow fadeInRight" data-wow-delay="0.6s">
                    <div class="featured-card-header admin-panel-header">
                        <h3><i class="bi bi-gear-fill me-2 text-white"></i>Yönetim Paneli</h3>
                    </div>
                    <div class="modern-card-body p-0">
                        <!-- Yönetim Menüsü -->
                        <div class="admin-menu">
                            <!-- Yeni Çalışma Ekle -->
                            <a href="{% url 'calismalar:calisma_ekle' %}" class="admin-menu-item">
                                <div class="admin-menu-icon">
                                    <i class="bi bi-plus-circle-fill"></i>
                                </div>
                                <div class="admin-menu-content">
                                    <h5>Yeni Çalışma Ekle</h5>
                                    <p class="text-muted" style="font-size: var(--font-size-sm);">Portfolyonuza yeni bir çalışma ekleyin</p>
                                </div>
                                <div class="admin-menu-arrow">
                                    <i class="bi bi-chevron-right"></i>
                                </div>
                            </a>

                            <!-- Bu Çalışmayı Düzenle -->
                            <a href="{% url 'calismalar:calisma_duzenle' calisma.slug %}" class="admin-menu-item">
                                <div class="admin-menu-icon edit-icon">
                                    <i class="bi bi-pencil-square"></i>
                                </div>
                                <div class="admin-menu-content">
                                    <h5>Bu Çalışmayı Düzenle</h5>
                                    <p class="text-muted" style="font-size: var(--font-size-sm);">Mevcut çalışmanın içeriğini güncelleyin</p>
                                </div>
                                <div class="admin-menu-arrow">
                                    <i class="bi bi-chevron-right"></i>
                                </div>
                            </a>

                            <!-- Fotoğraf Ekle (Düzenleme sayfasına yönlendir) -->
                            <a href="{% url 'calismalar:calisma_duzenle' calisma.slug %}" class="admin-menu-item">
                                <div class="admin-menu-icon photo-icon">
                                    <i class="bi bi-image"></i>
                                </div>
                                <div class="admin-menu-content">
                                    <h5>Fotoğraf Ekle</h5>
                                    <p class="text-muted" style="font-size: var(--font-size-sm);">Düzenleme sayfasında fotoğraf ekle</p>
                                </div>
                                <div class="admin-menu-arrow">
                                    <i class="bi bi-chevron-right"></i>
                                </div>
                            </a>

                            <!-- Çalışmayı Sil -->
                            <a href="#" class="admin-menu-item delete-item" data-bs-toggle="modal" data-bs-target="#deleteWorkModal">
                                <div class="admin-menu-icon delete-icon">
                                    <i class="bi bi-trash"></i>
                                </div>
                                <div class="admin-menu-content">
                                    <h5>Çalışmayı Sil</h5>
                                    <p class="text-muted" style="font-size: var(--font-size-sm);">Bu çalışmayı kalıcı olarak silin</p>
                                </div>
                                <div class="admin-menu-arrow">
                                    <i class="bi bi-chevron-right"></i>
                                </div>
                            </a>


                            <!-- Çalışma Listesine Dön -->
                            <a href="{% url 'calismalar:calisma_listesi' %}" class="admin-menu-item">
                                <div class="admin-menu-icon back-icon">
                                    <i class="bi bi-arrow-left-circle"></i>
                                </div>
                                <div class="admin-menu-content">
                                    <h5>Çalışma Listesine Dön</h5>
                                    <p class="text-muted" style="font-size: var(--font-size-sm);">Tüm çalışmaları görüntüleyin</p>
                                </div>
                                <div class="admin-menu-arrow">
                                    <i class="bi bi-chevron-right"></i>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
                {% endif %}

                {% if user.is_authenticated and user.is_superuser %}


                <!-- Çalışma Silme Modal (Sadece Yöneticiler) -->
                <div class="modal fade" id="deleteWorkModal" tabindex="-1" aria-labelledby="deleteWorkModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header" style="background: linear-gradient(135deg, var(--color-brown) 0%, var(--color-sand) 100%); color: white;">
                                <h5 class="modal-title" id="deleteWorkModalLabel"><i class="bi bi-trash me-2"></i>Çalışmayı Sil</h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Kapat"></button>
                            </div>
                            <div class="modal-body">
                                <p class="mb-0">Bu çalışmayı silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.</p>
                                <p class="text-danger mt-3" style="margin-top: var(--space-md) !important;"><strong>Uyarı:</strong> Çalışma ile ilişkili tüm fotoğraflar da silinecektir.</p>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="modern-btn modern-btn-outline" data-bs-dismiss="modal">
                                    <i class="bi bi-x-circle me-1"></i>İptal
                                </button>
                                <form action="{% url 'calismalar:calisma_sil' calisma.slug %}" method="post">
                                    {% csrf_token %}
                                    <button type="submit" class="modern-btn modern-btn-primary">
                                        <i class="bi bi-trash me-1"></i>Evet, Sil
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Geri ve İleri Butonları -->
        <div class="navigation-buttons mb-4 wow fadeInUp" data-wow-delay="0.3s">
            <div class="row">
                {% if onceki_calisma %}
                <div class="col-6">
                    <a href="{% url 'calismalar:calisma_detay' onceki_calisma.slug %}" class="modern-btn-nav prev autumn-nav-btn">
                        <i class="bi bi-arrow-left me-2"></i>
                        <span class="d-none d-sm-inline">Önceki:</span> {{ onceki_calisma.baslik|truncatechars:25 }}
                    </a>
                </div>
                {% else %}
                <div class="col-6"></div>
                {% endif %}

                {% if sonraki_calisma %}
                <div class="col-6 text-end">
                    <a href="{% url 'calismalar:calisma_detay' sonraki_calisma.slug %}" class="modern-btn-nav next autumn-nav-btn">
                        {{ sonraki_calisma.baslik|truncatechars:25 }} <span class="d-none d-sm-inline">:Sonraki</span>
                        <i class="bi bi-arrow-right ms-2"></i>
                    </a>
                </div>
                {% else %}
                <div class="col-6"></div>
                {% endif %}
            </div>
        </div>
    </div>
</main>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/swiper@9/swiper-bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/glightbox/dist/js/glightbox.min.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Swiper başlatma
        const swiper = new Swiper('.product-slider', {
            loop: true,
            pagination: {
                el: '.swiper-pagination',
                clickable: true
            },
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev'
            },
            autoplay: {
                delay: 5000,
                disableOnInteraction: false
            }
        });

        // GLightbox başlatma
        const lightbox = GLightbox({
            selector: '.modern-btn-circle',
            touchNavigation: true,
            loop: true,
            autoplayVideos: true
        });

        // Beğenme işlemi - Tamamen yeniden düzenlendi
        document.addEventListener('click', function(e) {
            // Tıklanan eleman veya üst elemanlarından biri like-icon sınıfına sahip mi kontrol et
            const likeButton = e.target.closest('.like-icon');

            if (likeButton) {
                console.log('Beğeni butonuna tıklandı');
                const calismaId = likeButton.getAttribute('data-calisma-id');
                const csrfToken = document.querySelector('input[name="csrfmiddlewaretoken"]').value;

                fetch('/calismalar/calisma/{{ calisma.slug }}/begen/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': csrfToken,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: ''
                })
                .then(response => response.json())
                .then(data => {
                    console.log('Beğeni yanıtı:', data);
                    if (data.success) {
                        // Beğeni sayısını güncelle
                        const likeCount = document.getElementById('like-count');
                        if (likeCount) {
                            likeCount.textContent = data.like_count;
                        }

                        // Beğeni ikonunun görünümünü değiştir
                        const heartIcon = likeButton.querySelector('i');
                        if (data.liked) {
                            likeButton.classList.add('active');
                            if (heartIcon) {
                                heartIcon.classList.remove('bi-heart');
                                heartIcon.classList.add('bi-heart-fill');
                            }
                        } else {
                            likeButton.classList.remove('active');
                            if (heartIcon) {
                                heartIcon.classList.remove('bi-heart-fill');
                                heartIcon.classList.add('bi-heart');
                            }
                        }
                    }
                })
                .catch(error => {
                    console.error("Beğeni işlemi sırasında hata oluştu:", error);
                });
            }
        });

        // WOW.js başlatma
        new WOW().init();


    });
</script>
{% endblock %}
