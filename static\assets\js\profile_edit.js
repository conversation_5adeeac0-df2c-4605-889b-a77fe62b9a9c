/**
 * <PERSON><PERSON>p Cadısı Profil Düzenleme Script
 * Bu script, admin kullanıcıların Ha<PERSON>ımda sayfasındaki içeriği
 * tek bir modal üzerinden düzenleyebilmesini sağlar.
 */

// Sayfa yüklendiğinde çalışacak kodlar
document.addEventListener('DOMContentLoaded', function() {
    console.log('Profile edit script loaded');
    
    // Admin kullanıcısı olup olmadığını kontrol et
    const isAdmin = document.body.classList.contains('user-is-admin');
    
    if (!isAdmin) {
        console.log('User is not admin, exiting');
        return;
    }
    
    // CSRF token'ı al
    const csrfTokenElement = document.querySelector('input[name="csrfmiddlewaretoken"]');
    if (!csrfTokenElement) {
        console.error('CSRF token not found');
        return;
    }
    const csrfToken = csrfTokenElement.value;
    console.log('CSRF token found');
    
    // Düzenleme butonunu göster
    const editButton = document.getElementById('edit-profile-button');
    if (editButton) {
        editButton.classList.remove('d-none');
        console.log('Edit button displayed');
    } else {
        console.error('Edit button not found');
    }
    
    // Kaydet butonuna olay dinleyicisi ekle
    const saveButton = document.querySelector('#editProfileModal .modal-footer .modern-btn:not(.modern-btn-outline)');
    if (saveButton) {
        console.log('Save button found, adding event listener');
        saveButton.addEventListener('click', function() {
            console.log('Save button clicked');
            saveAllContent();
        });
    } else {
        console.error('Save button not found');
    }
    
    // Tüm içerikleri kaydetme fonksiyonu
    function saveAllContent() {
        console.log('saveAllContent function called');
        
        // Kaydedilecek tüm içerikleri topla
        const allInputs = document.querySelectorAll('#editProfileModal input[data-content-type], #editProfileModal textarea[data-content-type]');
        console.log('Found inputs to save:', allInputs.length);
        
        if (allInputs.length === 0) {
            console.error('No inputs found with data-content-type attribute');
            alert('Kaydedilecek içerik bulunamadı!');
            return;
        }
        
        let saveCount = 0;
        let errorCount = 0;
        let totalToSave = 0;
        
        // Kayıt için veri hazırla
        const saveData = [];
        const updatedContent = {}; // Sayfada güncellenecek içerikler
        
        allInputs.forEach(input => {
            const contentType = input.dataset.contentType;
            const contentId = input.dataset.contentId;
            const newContent = input.value;
            
            // Boş içerikler için işlem yapma
            if (!newContent.trim()) return;
            
            saveData.push({
                contentType: contentType,
                contentId: contentId,
                content: newContent
            });
            
            // Güncellenecek içerikleri kaydet
            updatedContent[contentType] = {
                id: contentId,
                value: newContent
            };
            
            totalToSave++;
        });
        
        console.log('Total items to save:', totalToSave);
        
        if (totalToSave === 0) {
            alert('Kaydedilecek içerik bulunamadı!');
            return;
        }
        
        // Her bir içeriği kaydet
        saveData.forEach(item => {
            saveContent(item.contentType, item.contentId, item.content, function(success) {
                if (success) {
                    saveCount++;
                } else {
                    errorCount++;
                }
                
                // Tüm kayıtlar tamamlandığında
                if (saveCount + errorCount === totalToSave) {
                    if (errorCount > 0) {
                        alert(`${saveCount} içerik başarıyla kaydedildi, ${errorCount} içerik kaydedilemedi.`);
                    } else {
                        // Sayfadaki içerikleri güncelle
                        updatePageContent(updatedContent);
                        
                        alert(`${saveCount} içerik başarıyla kaydedildi.`);
                        
                        // Modalı kapat
                        const modalElement = document.getElementById('editProfileModal');
                        const modal = bootstrap.Modal.getInstance(modalElement);
                        if (modal) modal.hide();
                    }
                }
            });
        });
    }
    
    // Sayfadaki içerikleri güncelle
    function updatePageContent(updatedContent) {
        console.log('Updating page content with:', updatedContent);
        
        // Sayfadaki tüm düzenlenebilir içerikleri bul
        const editableContents = document.querySelectorAll('.editable-content');
        
        // Her bir düzenlenebilir içerik için
        editableContents.forEach(element => {
            const contentType = element.dataset.contentType;
            
            // Bu içerik tipi güncellendiyse
            if (updatedContent[contentType]) {
                // İçerik metnini güncelle
                const contentTextElement = element.querySelector('.content-text');
                if (contentTextElement) {
                    contentTextElement.textContent = updatedContent[contentType].value;
                    console.log(`Updated content on page: ${contentType} = ${updatedContent[contentType].value}`);
                }
            }
        });
    }
    
    // İçeriği sunucuya gönder ve güncelle
    function saveContent(contentType, contentId, newContent, callback) {
        console.log('Saving content:', contentType, contentId);
        
        // Form verisi oluştur
        const formData = new FormData();
        formData.append('content_type', contentType);
        formData.append('content_id', contentId);
        formData.append('content', newContent);
        formData.append('csrfmiddlewaretoken', csrfToken);
        
        // AJAX ile içeriği güncelle
        fetch('/hakkimda/inline-update/', {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
                // FormData kullanıldığında Content-Type header'ı otomatik olarak ayarlanır
            },
            body: formData
        })
        .then(response => {
            console.log('Response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Response data:', data);
            if (data.success) {
                console.log('Successfully updated:', contentType, contentId);
                callback(true);
            } else {
                console.error('Update error:', data.error);
                callback(false);
            }
        })
        .catch(error => {
            console.error('AJAX error:', error);
            callback(false);
        });
    }
});
