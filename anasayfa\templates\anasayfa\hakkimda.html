{% extends 'base.html' %}
{% load static %}

{% block title %}Hakkımda - Küp Cadısı{% endblock %}

{% block extra_css %}
{% endblock %}

{% block content %}
<main class="modern-section py-5 hakkimda-section">
  <div class="container modern-container">
    <!-- CSRF Token for AJAX requests -->
    {% csrf_token %}

    <!-- Başlık Bölümü -->
    <div class="modern-header mb-5 position-relative">
      <h1 class="gradient-heading display-4 fw-bold mb-3">Hakkımda</h1>
      <p class="lead text-muted">Seramik sanatı ile olan yolculuğum ve çalışmalarım hakkında bilgi edinebilirsiniz.</p>


    </div>

    <div class="row g-4 mb-5">
      <!-- Kişisel Bilgiler -->
      <div class="col-lg-5 wow fadeInLeft" data-wow-delay="0.2s">
        <div class="modern-card modern-card-info">
          <div class="modern-card-header">
            <h3><i class="bi bi-person-circle"></i>Ki<PERSON><PERSON><PERSON> Bilgiler</h3>
          </div>
          <div class="modern-card-body position-relative">
            {% if hakkimda %}
              {% if can_edit %}
              <a href="{% url 'anasayfa:hakkimda_duzenle' %}" class="content-edit-btn-bottom" title="Bilgileri Düzenle">
                <i class="bi bi-pencil-square"></i>
              </a>
              {% endif %}
              <!-- Profil Resmi -->
              <div class="text-center mb-4">
                {% if hakkimda.resim %}
                  <img src="{{ hakkimda.resim.url }}" alt="Profil Resmi" class="profile-image rounded-circle profile-image-circle">
                {% else %}
                  <div class="profile-placeholder rounded-circle d-flex align-items-center justify-content-center profile-placeholder-circle">
                    <i class="bi bi-person profile-placeholder-icon"></i>
                  </div>
                {% endif %}
              </div>

              <!-- Başlık -->
              <div class="text-center mb-4">
                <h4 class="fw-bold" id="hakkimda-baslik">{{ hakkimda.baslik }}</h4>
                <p class="text-muted" id="hakkimda-aciklama">{{ hakkimda.aciklama|linebreaks }}</p>
              </div>

              <!-- Zaman Çizelgesi -->
              <div class="timeline-container mt-4">
                <h5 class="mb-3 text-brown"><i class="bi bi-mortarboard me-2 text-brown"></i>Eğitim Bilgilerim</h5>
                {% if egitimler %}
                  <div class="timeline">
                    {% for egitim in egitimler %}
                      <div class="timeline-item">
                        <div class="timeline-date">
                          {{ egitim.baslangic_tarihi|date:"Y" }} - {% if egitim.bitis_tarihi %}{{ egitim.bitis_tarihi|date:"Y" }}{% else %}Devam Ediyor{% endif %}
                        </div>
                        <div class="timeline-content">
                          <h6 class="text-navy">{{ egitim.bolum }}</h6>
                          <p class="text-soft">{{ egitim.okul }}</p>
                          {% if egitim.aciklama %}<p class="text-muted small">{{ egitim.aciklama }}</p>{% endif %}
                        </div>
                      </div>
                    {% endfor %}
                  </div>
                {% else %}
                  <p class="text-muted">Henüz eğitim bilgisi eklenmemiş.</p>
                {% endif %}
              </div>
            {% else %}
              <div class="text-center py-4">
                <i class="bi bi-exclamation-circle warning-icon-large"></i>
                <p class="mt-3">Henüz hakkımda bilgisi eklenmemiş.</p>
                {% if user.is_authenticated %}
                  <button class="modern-btn mt-3" onclick="showEditModal()">
                    <i class="bi bi-plus-circle me-2"></i>Bilgi Ekle
                  </button>
                {% endif %}
              </div>
            {% endif %}
          </div>
        </div>
      </div>

      <!-- Sanat Yolculuğu -->
      <div class="col-lg-7 wow fadeInRight" data-wow-delay="0.2s">
        <div class="modern-card modern-card-info">
          <div class="modern-card-header">
            <h3><i class="bi bi-palette"></i>Sanat Yolculuğum</h3>
          </div>
          <div class="modern-card-body">
            {% if hakkimda %}
              <div class="sanat-yolculugu-content">
                <!-- Sanat yolculuğu detayları -->
                <div class="mb-4 p-3 border-start border-4 border-primary">
                  <h5>Seramik Sanatına Başlangıç</h5>
                  <p>{{ hakkimda.sanat_baslangic|default:hakkimda.aciklama|linebreaks }}</p>
                </div>

                <!-- Yetenekler ve teknikler -->
                <div class="skills-section mt-5">
                  <h5 class="mb-3">
                    <span class="icon-circle icon-circle-sm">
                      <i class="bi bi-tools"></i>
                    </span>
                    Kullandığım Teknikler
                  </h5>
                  {% if hakkimda.kullandigi_teknikler %}
                    <p>{{ hakkimda.kullandigi_teknikler|linebreaks }}</p>
                  {% else %}
                    <div class="row g-2">
                      <div class="col-md-4 col-6">
                        <div class="skill-item">
                          <i class="bi bi-check-circle-fill me-2"></i>
                          <span>El Yapımı Seramik</span>
                        </div>
                      </div>
                      <div class="col-md-4 col-6">
                        <div class="skill-item">
                          <i class="bi bi-check-circle-fill me-2"></i>
                          <span>Çömlekçi Çarkı</span>
                        </div>
                      </div>
                      <div class="col-md-4 col-6">
                        <div class="skill-item">
                          <i class="bi bi-check-circle-fill me-2"></i>
                          <span>Sırlama Teknikleri</span>
                        </div>
                      </div>
                      <div class="col-md-4 col-6">
                        <div class="skill-item">
                          <i class="bi bi-check-circle-fill me-2"></i>
                          <span>Odun Pişirimi</span>
                        </div>
                      </div>
                      <div class="col-md-4 col-6">
                        <div class="skill-item">
                          <i class="bi bi-check-circle-fill me-2"></i>
                          <span>Raku Pişirimi</span>
                        </div>
                      </div>
                      <div class="col-md-4 col-6">
                        <div class="skill-item">
                          <i class="bi bi-check-circle-fill me-2"></i>
                          <span>Dekoratif Uygulamalar</span>
                        </div>
                      </div>
                    </div>
                  {% endif %}
                </div>

                <!-- Atölye Bilgileri -->
                <div class="workshop-section mt-5">
                  <h5 class="mb-3"><i class="bi bi-house-door me-2"></i>Atölye Bilgileri</h5>
                  {% if hakkimda.atolye_bilgileri %}
                    <p>{{ hakkimda.atolye_bilgileri|linebreaks }}</p>
                  {% else %}
                    <p>Kadıköy'deki atölyemde seramik sanatının büyülü dünyasını keşfetmek isteyenlerle buluşuyorum. Hem bireysel çalışmalar hem de grup dersleri için uygun bir ortam sunuyorum.</p>

                    <div class="workshop-gallery mt-4">
                      <div class="row g-3">
                        <div class="col-md-4">
                          <div class="card bg-light text-center p-3">
                            <i class="bi bi-people mb-2 workshop-icon"></i>
                            <h6>Grup Dersleri</h6>
                            <p class="small mb-0">Haftalık düzenli dersler</p>
                          </div>
                        </div>
                        <div class="col-md-4">
                          <div class="card bg-light text-center p-3">
                            <i class="bi bi-person mb-2 workshop-icon"></i>
                            <h6>Özel Dersler</h6>
                            <p class="small mb-0">Bire bir eğitim</p>
                          </div>
                        </div>
                        <div class="col-md-4">
                          <div class="card bg-light text-center p-3">
                            <i class="bi bi-calendar-event mb-2 workshop-icon"></i>
                            <h6>Atölye Etkinlikleri</h6>
                            <p class="small mb-0">Özel workshop günleri</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  {% endif %}
                </div>
              </div>
            {% else %}
              <div class="text-center py-5">
                <i class="bi bi-journal-text warning-icon-large"></i>
                <p class="mt-3">Henüz sanat yolculuğu detayları eklenmemiş.</p>
              </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>

    <div class="section-divider">
      <div class="section-divider-icon">
        <i class="bi bi-stars"></i>
      </div>
    </div>

    <!-- Animasyonlu Bileşenler -->
    <div class="container mb-5">
      <div class="row mb-4">
        <div class="col-12 text-center">
          <h2 class="mb-3">Sanat Yolculuğumda Sayılarla Ben</h2>
          <p class="lead text-muted mb-5">Seramik sanatı alanında yıllar içinde edindiğim deneyim ve başarılarımı sayılarla ifade etmek gerekirse...</p>
        </div>
      </div>

      <div class="row g-4 mb-5">
        <!-- Sayaç Kartları -->
        <div class="col-md-4">
          <div class="modern-card h-100">
            <div class="modern-card-body text-center p-4">
              <div class="counter-box mb-3">
                <span class="counter-number" data-target="150">0</span>
                <span class="counter-suffix">+</span>
              </div>
              <h3 class="fs-5">Tamamlanan Eser</h3>
              <p class="small">Bugüne kadar tamamladığım özgün seramik eserleri</p>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="modern-card h-100">
            <div class="modern-card-body text-center p-4">
              <div class="counter-box mb-3">
                <span class="counter-number" data-target="20">0</span>
                <span class="counter-suffix">+</span>
              </div>
              <h3 class="fs-5">Sergi Katılımı</h3>
              <p class="small">Ulusal ve uluslararası sergilerde yer alma</p>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="modern-card h-100">
            <div class="modern-card-body text-center p-4">
              <div class="counter-box mb-3">
                <span class="counter-number" data-target="10">0</span>
                <span class="counter-suffix"></span>
              </div>
              <h3 class="fs-5">Yıllık Deneyim</h3>
              <p class="small">Seramik sanatı alanında profesyonel deneyim</p>
            </div>
          </div>
        </div>
      </div>

      <div class="row g-4">
        <!-- Yetenek Çubukları -->
        <div class="col-md-6">
          <div class="modern-card h-100">
            <div class="modern-card-body p-4">
              <h3 class="fs-5 mb-4">
                <span class="icon-circle icon-circle-sm">
                  <i class="bi bi-stars"></i>
                </span>
                Seramik Teknikleri
              </h3>

              <div class="skill-item mb-3">
                <div class="d-flex justify-content-between mb-1">
                  <span class="skill-name">El Yapımı Seramik</span>
                  <span class="skill-percentage">98%</span>
                </div>
                <div class="skill-bar">
                  <div class="skill-progress" data-width="98"></div>
                </div>
              </div>

              <div class="skill-item mb-3">
                <div class="d-flex justify-content-between mb-1">
                  <span class="skill-name">Çömlekçi Çarkı</span>
                  <span class="skill-percentage">90%</span>
                </div>
                <div class="skill-bar">
                  <div class="skill-progress" data-width="90"></div>
                </div>
              </div>

              <div class="skill-item">
                <div class="d-flex justify-content-between mb-1">
                  <span class="skill-name">Sırlama Teknikleri</span>
                  <span class="skill-percentage">85%</span>
                </div>
                <div class="skill-bar">
                  <div class="skill-progress" data-width="85"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Zaman Çizelgesi -->
        <div class="col-md-6">
          <div class="modern-card h-100">
            <div class="modern-card-body p-4">
              <h3 class="fs-5 mb-4">Sanat Yolculuğum</h3>

              <div class="timeline">
                <div class="timeline-item">
                  <div class="timeline-dot"></div>
                  <div class="timeline-content">
                    <h4 class="fs-6">İlk Sergim</h4>
                    <p class="small mb-0">2018 - Kadıköy Sanat Galerisi</p>
                  </div>
                </div>

                <div class="timeline-item">
                  <div class="timeline-dot"></div>
                  <div class="timeline-content">
                    <h4 class="fs-6">Atölye Açılışı</h4>
                    <p class="small mb-0">2020 - Kendi atölyemi kurdum</p>
                  </div>
                </div>

                <div class="timeline-item">
                  <div class="timeline-dot"></div>
                  <div class="timeline-content">
                    <h4 class="fs-6">Uluslararası Sergi</h4>
                    <p class="small mb-0">2022 - Berlin Seramik Festivali</p>
                  </div>
                </div>

                <div class="timeline-item">
                  <div class="timeline-dot"></div>
                  <div class="timeline-content">
                    <h4 class="fs-6">Ödül Kazanma</h4>
                    <p class="small mb-0">2023 - İstanbul Seramik Yarışması Birincilik</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Galeri -->
    <div class="row mb-5">
      <div class="col-12 wow fadeInUp" data-wow-delay="0.3s">
        <div class="modern-card">
          <div class="modern-card-header position-relative">
            <h3><i class="bi bi-image"></i>Eserlerimden Örnekler</h3>
            {% if can_edit %}
            <a href="{% url 'calismalar:calisma_ekle' %}" class="content-edit-btn" title="Yeni Eser Ekle">
              <i class="bi bi-plus-lg"></i>
            </a>
            {% endif %}
          </div>
          <div class="modern-card-body">
            <div class="row g-4">
              {% if son_calismalar %}
                {% for item in son_calismalar %}
                  <div class="col-md-3 col-6">
                    <a href="{% url 'calismalar:calisma_detay' item.calisma.slug %}" class="text-decoration-none">
                      <div class="portfolio-item">
                        <img src="{{ item.fotograf.fotograf.url }}" alt="{{ item.calisma.baslik }}" class="img-fluid rounded">
                        <div class="portfolio-overlay">
                          <h6>{{ item.calisma.baslik }}</h6>
                          <p class="small">{{ item.calisma.olusturma_tarihi|date:"Y" }}</p>
                        </div>
                      </div>
                    </a>
                  </div>
                {% endfor %}

                {% if son_calismalar|length < 4 %}
                  {% for i in "x"|ljust:4|make_list %}
                    {% if forloop.counter0 >= son_calismalar|length %}
                      <div class="col-md-3 col-6">
                        <div class="portfolio-item">
                          <img src="{% static 'img/placeholder.jpg' %}" alt="Eser Örneği" class="img-fluid rounded">
                          <div class="portfolio-overlay">
                            <h6>Yakında</h6>
                            <p class="small">Yeni eserler eklenecek</p>
                          </div>
                        </div>
                      </div>
                    {% endif %}
                  {% endfor %}
                {% endif %}
              {% else %}
                <div class="col-md-3 col-6">
                  <div class="portfolio-item">
                    <img src="{% static 'img/placeholder.jpg' %}" alt="Eser Örneği" class="img-fluid rounded">
                    <div class="portfolio-overlay">
                      <h6>Seramik Kase</h6>
                      <p class="small">2023</p>
                    </div>
                  </div>
                </div>
                <div class="col-md-3 col-6">
                  <div class="portfolio-item">
                    <img src="{% static 'img/placeholder.jpg' %}" alt="Eser Örneği" class="img-fluid rounded">
                    <div class="portfolio-overlay">
                      <h6>El Yapımı Vazo</h6>
                      <p class="small">2022</p>
                    </div>
                  </div>
                </div>
                <div class="col-md-3 col-6">
                  <div class="portfolio-item">
                    <img src="{% static 'img/placeholder.jpg' %}" alt="Eser Örneği" class="img-fluid rounded">
                    <div class="portfolio-overlay">
                      <h6>Dekoratif Tabak</h6>
                      <p class="small">2023</p>
                    </div>
                  </div>
                </div>
                <div class="col-md-3 col-6">
                  <div class="portfolio-item">
                    <img src="{% static 'img/placeholder.jpg' %}" alt="Eser Örneği" class="img-fluid rounded">
                    <div class="portfolio-overlay">
                      <h6>Seramik Heykel</h6>
                      <p class="small">2021</p>
                    </div>
                  </div>
                </div>
              {% endif %}
            </div>
            <div class="text-center mt-4">
              <a href="{% url 'calismalar:yonetici_calismalari' %}" class="modern-btn">
                <i class="bi bi-collection me-2"></i>Tüm Çalışmalarım
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</main>


{% endblock %}

{% block extra_js %}
<script>
  // Sayaç animasyonu
  document.addEventListener('DOMContentLoaded', function() {
    const counters = document.querySelectorAll('.counter-number');

    const animateCounter = (counter) => {
      const target = +counter.getAttribute('data-target');
      let count = 0;
      const speed = 2000 / target; // 2 saniyede tamamlanacak şekilde

      const updateCount = () => {
        if (count < target) {
          count++;
          counter.innerText = count;
          setTimeout(updateCount, speed);
        } else {
          counter.innerText = target;
        }
      };

      updateCount();
    };

    // IntersectionObserver ile görünür olduğunda animasyonu başlat
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          animateCounter(entry.target);
          observer.unobserve(entry.target);
        }
      });
    }, { threshold: 0.5 });

    counters.forEach(counter => {
      observer.observe(counter);
    });

    // Yetenek çubukları animasyonu
    const skillBars = document.querySelectorAll('.skill-progress');

    const animateSkillBar = (bar) => {
      const width = bar.getAttribute('data-width');
      bar.style.width = '0%';
      setTimeout(() => {
        bar.style.width = width + '%';
      }, 200);
    };

    const skillObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          animateSkillBar(entry.target);
          skillObserver.unobserve(entry.target);
        }
      });
    }, { threshold: 0.5 });

    skillBars.forEach(bar => {
      skillObserver.observe(bar);
    });
  });
</script>
{% endblock %}
