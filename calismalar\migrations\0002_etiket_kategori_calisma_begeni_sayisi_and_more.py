# Generated by Django 5.2 on 2025-05-03 22:55

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('calismalar', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Etiket',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ad', models.CharField(max_length=50, unique=True, verbose_name='Etiket Adı')),
                ('slug', models.SlugField(unique=True)),
            ],
            options={
                'verbose_name': 'Etiket',
                'verbose_name_plural': 'Etiketler',
                'ordering': ['ad'],
            },
        ),
        migrations.CreateModel(
            name='Kate<PERSON><PERSON>',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ad', models.<PERSON>r<PERSON>ield(max_length=100, verbose_name='<PERSON><PERSON><PERSON>')),
                ('slug', models.SlugField(unique=True)),
                ('aciklama', models.TextField(blank=True, verbose_name='Açıklama')),
            ],
            options={
                'verbose_name': 'Kategori',
                'verbose_name_plural': 'Kategoriler',
                'ordering': ['ad'],
            },
        ),
        migrations.AddField(
            model_name='calisma',
            name='begeni_sayisi',
            field=models.PositiveIntegerField(default=0, verbose_name='Beğeni Sayısı'),
        ),
        migrations.AddField(
            model_name='calisma',
            name='goruntulenme_sayisi',
            field=models.PositiveIntegerField(default=0, verbose_name='Görüntülenme Sayısı'),
        ),
        migrations.AddIndex(
            model_name='calismafotograf',
            index=models.Index(fields=['calisma', 'sira'], name='calismalar__calisma_141140_idx'),
        ),
        migrations.AddField(
            model_name='calisma',
            name='etiketler',
            field=models.ManyToManyField(blank=True, related_name='calismalar', to='calismalar.etiket'),
        ),
        migrations.AddField(
            model_name='calisma',
            name='kategori',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='calismalar', to='calismalar.kategori'),
        ),
        migrations.AddIndex(
            model_name='calisma',
            index=models.Index(fields=['-olusturma_tarihi'], name='calismalar__olustur_eefdc4_idx'),
        ),
        migrations.AddIndex(
            model_name='calisma',
            index=models.Index(fields=['kategori'], name='calismalar__kategor_e59557_idx'),
        ),
        migrations.AddIndex(
            model_name='calisma',
            index=models.Index(fields=['user'], name='calismalar__user_id_17b03c_idx'),
        ),
    ]
