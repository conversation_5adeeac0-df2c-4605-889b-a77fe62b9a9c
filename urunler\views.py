from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.http import HttpResponseForbidden
from django.contrib import messages
from django.views.decorators.http import require_POST
from django.utils.text import slugify
from django.db.models import Q
from .models import Category, Product, ProductImage
from .forms import ProductForm, ProductImageForm

def urun_listesi(request, category_slug=None):
    category = None
    categories = Category.objects.all()
    products = Product.objects.filter(is_available=True)

    # Kategori filtreleme
    if category_slug:
        category = get_object_or_404(Category, slug=category_slug)
        products = products.filter(category=category)

    # Etiket filtreleme
    tag = request.GET.get('tag')
    if tag:
        products = products.filter(tags__slug=tag)

    # Arama filtreleme
    search_query = request.GET.get('q')
    if search_query:
        products = products.filter(
            Q(name__icontains=search_query) |
            Q(description__icontains=search_query) |
            Q(tags__name__icontains=search_query)
        ).distinct()

    # Tüm etiketleri al
    from taggit.models import Tag
    tags = Tag.objects.all()

    context = {
        'category': category,
        'categories': categories,
        'products': products,
        'tags': tags,
        'current_tag': tag,
        'search_query': search_query
    }
    return render(request, 'urunler/urun_listesi.html', context)

def urun_detay(request, id, slug):
    product = get_object_or_404(Product,
                              id=id,
                              slug=slug,
                              is_available=True)

    # Benzer ürünleri al (gelişmiş algoritma)
    similar_products = get_similar_products(product)

    context = {
        'product': product,
        'similar_products': similar_products
    }
    return render(request, 'urunler/urun_detay.html', context)

def get_similar_products(product, max_products=4):
    """
    Gelişmiş benzer ürünler algoritması.

    Benzerlik faktörleri:
    1. Etiket eşleşmesi (en yüksek ağırlık)
    2. Kategori eşleşmesi
    3. Fiyat aralığı benzerliği
    4. Aynı ekleyen kullanıcı

    Her faktör için bir puan hesaplanır ve toplam benzerlik puanına göre sıralama yapılır.
    """
    from django.db.models import Count, F, Q, Case, When, IntegerField, ExpressionWrapper, FloatField, Value, DecimalField
    from django.db.models.functions import Abs
    from decimal import Decimal
    import math

    # Mevcut ürünün bilgileri
    product_tags = list(product.tags.values_list('id', flat=True))
    product_category = product.category
    product_price = product.price or Decimal('0')
    product_user = product.ekleyen

    # Tüm ürünleri al (kendisi hariç ve stokta olanlar)
    all_products = Product.objects.filter(is_available=True).exclude(id=product.id)

    # Etiket eşleşmesi yoksa sadece kategori bazlı filtreleme yap
    if not product_tags:
        # Kategori eşleşmesi (2 puan)
        similar_products = all_products.filter(category=product_category)\
            .annotate(similarity_score=Case(
                When(category=product_category, then=2),
                default=0,
                output_field=IntegerField()
            ))

        # Fiyat aralığı benzerliği (0-1 puan)
        if product_price > 0:
            # Fiyat farkının yüzdesini hesapla (ne kadar düşükse o kadar benzer)
            similar_products = similar_products.annotate(
                price_diff=ExpressionWrapper(
                    Abs(F('price') - product_price) / (product_price + Decimal('0.01')),
                    output_field=FloatField()
                )
            ).annotate(
                price_similarity=Case(
                    # Fiyat farkı %50'den azsa, fark yüzdesine göre 0-1 arası puan ver
                    When(price_diff__lte=0.5,
                         then=ExpressionWrapper(1 - F('price_diff'), output_field=FloatField())),
                    default=0,
                    output_field=FloatField()
                )
            )

            # Toplam benzerlik puanını güncelle
            similar_products = similar_products.annotate(
                similarity_score=F('similarity_score') + F('price_similarity')
            )

        # Aynı kullanıcının ürünlerine bonus puan (1 puan)
        if product_user:
            similar_products = similar_products.annotate(
                user_match=Case(
                    When(ekleyen=product_user, then=1),
                    default=0,
                    output_field=IntegerField()
                )
            ).annotate(
                similarity_score=F('similarity_score') + F('user_match')
            )
    else:
        # Etiket eşleşmesi (her etiket için 3 puan)
        similar_products = all_products.filter(
            Q(tags__id__in=product_tags) | Q(category=product_category)
        ).annotate(
            # Eşleşen etiket sayısı
            matching_tags_count=Count('tags', filter=Q(tags__id__in=product_tags)),
            # Kategori eşleşmesi (2 puan)
            category_match=Case(
                When(category=product_category, then=2),
                default=0,
                output_field=IntegerField()
            )
        ).annotate(
            # Etiket benzerlik puanı (her etiket 3 puan)
            tag_similarity=ExpressionWrapper(
                F('matching_tags_count') * 3,
                output_field=IntegerField()
            ),
            # Başlangıç benzerlik puanı
            similarity_score=F('tag_similarity') + F('category_match')
        )

        # Fiyat aralığı benzerliği (0-1 puan)
        if product_price > 0:
            # Fiyat farkının yüzdesini hesapla (ne kadar düşükse o kadar benzer)
            similar_products = similar_products.annotate(
                price_diff=ExpressionWrapper(
                    Abs(F('price') - product_price) / (product_price + Decimal('0.01')),
                    output_field=FloatField()
                )
            ).annotate(
                price_similarity=Case(
                    # Fiyat farkı %50'den azsa, fark yüzdesine göre 0-1 arası puan ver
                    When(price_diff__lte=0.5,
                         then=ExpressionWrapper(1 - F('price_diff'), output_field=FloatField())),
                    default=0,
                    output_field=FloatField()
                )
            )

            # Toplam benzerlik puanını güncelle
            similar_products = similar_products.annotate(
                similarity_score=F('similarity_score') + F('price_similarity')
            )

        # Aynı kullanıcının ürünlerine bonus puan (1 puan)
        if product_user:
            similar_products = similar_products.annotate(
                user_match=Case(
                    When(ekleyen=product_user, then=1),
                    default=0,
                    output_field=IntegerField()
                )
            ).annotate(
                similarity_score=F('similarity_score') + F('user_match')
            )

    # Benzerlik puanına göre sırala ve en fazla max_products kadar ürün döndür
    similar_products = similar_products.order_by('-similarity_score', '-created_at')[:max_products]

    return similar_products

@login_required
def urun_ekle(request):
    """Yeni bir ürün ekler."""
    # Sadece süper kullanıcılar ürün ekleyebilir
    if not request.user.is_superuser:
        messages.error(request, 'Ürün eklemek için yönetici yetkisi gereklidir.')
        return redirect('urunler:urun_listesi')

    # Fotoğraf formset oluştur
    from django.forms import inlineformset_factory
    FotografFormset = inlineformset_factory(
        Product,
        ProductImage,
        form=ProductImageForm,
        extra=1,
        can_delete=False
    )

    if request.method == 'POST':
        form = ProductForm(request.POST)
        formset = FotografFormset(request.POST, request.FILES, prefix='fotograflar')

        if form.is_valid():
            # Ürünü kaydet
            urun = form.save(commit=False)
            urun.ekleyen = request.user

            # Slug oluştur
            urun.slug = slugify(urun.name)
            urun.save()
            form.save_m2m()  # Etiketleri kaydet

            # Formset'i kaydet
            if formset.is_valid():
                fotograflar = formset.save(commit=False)
                for i, fotograf in enumerate(fotograflar):
                    fotograf.product = urun
                    # Fotoğraf dosyasının varlığını kontrol et
                    if hasattr(fotograf, 'image') and fotograf.image:
                        # İlk fotoğrafı ana görsel olarak ayarla
                        if i == 0:
                            fotograf.is_main = True
                        fotograf.save()

            messages.success(request, 'Ürün başarıyla eklendi.')
            return redirect('urunler:urun_detay', id=urun.id, slug=urun.slug)
        else:
            messages.error(request, 'Lütfen formdaki hataları düzeltin.')
    else:
        form = ProductForm()
        formset = FotografFormset(prefix='fotograflar')

    return render(request, 'urunler/urun_form.html', {
        'form': form,
        'formset': formset,
        'title': 'Yeni Ürün Ekle',
    })

@login_required
def urun_duzenle(request, id, slug):
    """Var olan bir ürünü düzenler."""
    # Sadece süper kullanıcılar ürün düzenleyebilir
    if not request.user.is_superuser:
        messages.error(request, 'Ürün düzenlemek için yönetici yetkisi gereklidir.')
        return redirect('urunler:urun_detay', id=id, slug=slug)

    # Ürünü al
    urun = get_object_or_404(Product, id=id, slug=slug)

    # Fotoğraf formset oluştur
    from django.forms import inlineformset_factory
    FotografFormset = inlineformset_factory(
        Product,
        ProductImage,
        form=ProductImageForm,
        extra=1,
        can_delete=True
    )

    if request.method == 'POST':
        form = ProductForm(request.POST, instance=urun)
        formset = FotografFormset(request.POST, request.FILES, instance=urun, prefix='fotograflar')

        if form.is_valid() and formset.is_valid():
            # Ürünü kaydet
            urun = form.save(commit=False)

            # Slug güncelle
            urun.slug = slugify(urun.name)
            urun.save()
            form.save_m2m()  # Etiketleri kaydet

            # Formset'i kaydet
            fotograflar = formset.save(commit=False)

            # Silinen fotoğrafları işle
            for obj in formset.deleted_objects:
                obj.delete()

            # Yeni fotoğrafları kaydet
            for fotograf in fotograflar:
                # Fotoğraf dosyasının varlığını kontrol et
                if hasattr(fotograf, 'image') and fotograf.image:
                    fotograf.save()

            # Ana görsel kontrolü
            has_main = ProductImage.objects.filter(product=urun, is_main=True).exists()
            if not has_main and urun.images.exists():
                # Ana görsel yoksa ilk fotoğrafı ana görsel yap
                ilk_fotograf = urun.images.first()
                ilk_fotograf.is_main = True
                ilk_fotograf.save()

            messages.success(request, 'Ürün başarıyla güncellendi.')
            return redirect('urunler:urun_detay', id=urun.id, slug=urun.slug)
        else:
            messages.error(request, 'Lütfen formdaki hataları düzeltin.')
    else:
        form = ProductForm(instance=urun)
        formset = FotografFormset(instance=urun, prefix='fotograflar')

    return render(request, 'urunler/urun_form.html', {
        'form': form,
        'formset': formset,
        'urun': urun,
        'title': f'{urun.name} - Düzenle',
    })

@login_required
def urun_sil(request, id, slug):
    """Bir ürünü siler."""
    # Sadece süper kullanıcılar ürün silebilir
    if not request.user.is_superuser:
        messages.error(request, 'Ürün silmek için yönetici yetkisi gereklidir.')
        return redirect('urunler:urun_detay', id=id, slug=slug)

    product = get_object_or_404(Product, id=id, slug=slug)

    if request.method == 'POST':
        # Ürünü sil (Product.delete() metodu override edilmiş ve ilişkili fotoğrafları da siliyor)
        product.delete()

        messages.success(request, 'Ürün başarıyla silindi.')
        return redirect('urunler:urun_listesi')

    return render(request, 'urunler/urun_detay.html', {
        'product': product,
        'show_delete_modal': True,
        'title': f'{product.name}'
    })

@login_required
@require_POST
def fotograf_ekle(request, id, slug):
    """Bir ürüne fotoğraf ekler."""
    # Sadece süper kullanıcılar fotoğraf ekleyebilir
    if not request.user.is_superuser:
        messages.error(request, 'Fotoğraf eklemek için yönetici yetkisi gereklidir.')
        return redirect('urunler:urun_detay', id=id, slug=slug)

    product = get_object_or_404(Product, id=id, slug=slug)

    # Fotoğraf ekleme işlemi
    if 'image' in request.FILES:
        image = request.FILES['image']
        is_main = request.POST.get('is_main', False)

        # Yeni fotoğraf oluştur
        product_image = ProductImage(product=product, image=image)

        # Ana görsel olarak ayarla
        if is_main:
            # Diğer tüm görsellerin ana görsel özelliğini kaldır
            ProductImage.objects.filter(product=product, is_main=True).update(is_main=False)
            product_image.is_main = True

        product_image.save()
        messages.success(request, 'Fotoğraf başarıyla eklendi.')
    else:
        messages.error(request, 'Lütfen bir fotoğraf seçin.')

    return redirect('urunler:urun_detay', id=id, slug=slug)
