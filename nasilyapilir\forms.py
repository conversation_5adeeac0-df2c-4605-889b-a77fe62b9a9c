from django import forms
from .models import Video, Kategori

class VideoForm(forms.ModelForm):
    """Video oluşturma ve düzenleme formu."""
    class Meta:
        model = Video
        fields = ['baslik', 'aciklama', 'youtube_url', 'kategori', 'tags', 'onizleme_resmi', 'aktif']
        widgets = {
            'baslik': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Video başlığını girin'}),
            'aciklama': forms.Textarea(attrs={'class': 'form-control', 'rows': 5, 'placeholder': 'Video açıklamasını girin'}),
            'youtube_url': forms.URLInput(attrs={'class': 'form-control', 'placeholder': 'YouTube video URL\'sini girin'}),
            'kategori': forms.Select(attrs={'class': 'form-control'}),
            'tags': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Etiketleri virgülle ayırarak girin'}),
            'onizleme_resmi': forms.FileInput(attrs={'class': 'form-control'}),
            'aktif': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
        labels = {
            'baslik': 'Video Başlığı',
            'aciklama': 'Açıklama',
            'youtube_url': 'YouTube URL',
            'kategori': 'Kategori',
            'tags': 'Etiketler',
            'onizleme_resmi': 'Önizleme Resmi',
            'aktif': 'Aktif',
        }
        help_texts = {
            'baslik': 'Videonun başlığını girin.',
            'aciklama': 'Video hakkında detaylı bilgi verin.',
            'youtube_url': 'YouTube video linkini girin (örn: https://www.youtube.com/watch?v=...).',
            'kategori': 'Videonun ait olduğu kategoriyi seçin.',
            'tags': 'Video ile ilgili etiketleri virgülle ayırarak girin (örn: ahşap, el yapımı, diy).',
            'onizleme_resmi': 'Video için önizleme resmi yükleyin (isteğe bağlı).',
            'aktif': 'Video yayında görünsün mü?',
        }

    def clean_youtube_url(self):
        """YouTube URL'sini doğrula."""
        url = self.cleaned_data['youtube_url']
        if 'youtube.com' not in url and 'youtu.be' not in url:
            raise forms.ValidationError('Geçerli bir YouTube URL\'si girin.')
        return url

    def clean_baslik(self):
        """Başlık temizleme ve XSS koruması."""
        baslik = self.cleaned_data['baslik']
        # Basit XSS koruması
        baslik = baslik.replace('<', '&lt;').replace('>', '&gt;')
        return baslik
