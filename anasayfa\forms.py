from django import forms
from django.contrib.auth.forms import UserCreationForm
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.conf import settings
from .models import Hakkimda, Egitim, IletisimBilgisi, SosyalMedya

class KayitForm(UserCreationForm):
    email = forms.EmailField(required=True)

    class Meta:
        model = User
        fields = ('username', 'email', 'password1', 'password2')

    def save(self, commit=True):
        user = super().save(commit=False)
        user.email = self.cleaned_data['email']
        if commit:
            user.save()
        return user

class ProfilForm(forms.ModelForm):
    class Meta:
        model = User
        fields = ('first_name', 'last_name', 'email')

class HakkimdaForm(forms.ModelForm):
    class Meta:
        model = Hakkimda
        fields = ['baslik', 'aciklama', 'resim', 'sanat_baslangic', 'kullandigi_teknikler', 'atolye_bilgileri']
        widgets = {
            'baslik': forms.TextInput(attrs={'class': 'form-control'}),
            'aciklama': forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
            'resim': forms.FileInput(attrs={'class': 'form-control'}),
            'sanat_baslangic': forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
            'kullandigi_teknikler': forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
            'atolye_bilgileri': forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
        }

    def clean_resim(self):
        resim = self.cleaned_data.get('resim')
        if resim and hasattr(resim, 'content_type'):
            # Yeni yüklenen dosya için kontroller
            # Dosya boyutu kontrolü
            if resim.size > settings.MAX_UPLOAD_SIZE:
                max_size_mb = settings.MAX_UPLOAD_SIZE / (1024 * 1024)
                raise ValidationError(f'Dosya boyutu {max_size_mb}MB\'dan küçük olmalıdır.')

            # Dosya türü kontrolü
            if resim.content_type not in settings.CONTENT_TYPES:
                raise ValidationError('Sadece JPEG, PNG, GIF ve WEBP formatları desteklenmektedir.')

        return resim

class EgitimForm(forms.ModelForm):
    class Meta:
        model = Egitim
        fields = ['okul', 'bolum', 'baslangic_tarihi', 'bitis_tarihi', 'aciklama']
        widgets = {
            'okul': forms.TextInput(attrs={'class': 'form-control'}),
            'bolum': forms.TextInput(attrs={'class': 'form-control'}),
            'baslangic_tarihi': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'bitis_tarihi': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'aciklama': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }

class IletisimBilgisiForm(forms.ModelForm):
    class Meta:
        model = IletisimBilgisi
        fields = ['email', 'telefon', 'adres']
        widgets = {
            'email': forms.EmailInput(attrs={'class': 'form-control'}),
            'telefon': forms.TextInput(attrs={'class': 'form-control'}),
            'adres': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }

class SosyalMedyaForm(forms.ModelForm):
    class Meta:
        model = SosyalMedya
        fields = ['platform', 'url']
        widgets = {
            'platform': forms.Select(attrs={'class': 'form-control'}),
            'url': forms.URLInput(attrs={'class': 'form-control'}),
        }
