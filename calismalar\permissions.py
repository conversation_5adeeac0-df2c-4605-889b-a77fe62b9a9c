from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from django.db.models import Q

from .models import Calisma, CalismaFotograf

def setup_calisma_permissions():
    """
    Çalışma Yöneticileri grubu için gerekli izinleri oluşturur ve atar.
    """
    # Çalışma Yöneticileri grubunu al veya oluştur
    group, created = Group.objects.get_or_create(name='Çalışma Yöneticileri')
    
    # Çalışma ve CalismaFotograf modelleri için ContentType'ları al
    calisma_content_type = ContentType.objects.get_for_model(Calisma)
    fotograf_content_type = ContentType.objects.get_for_model(CalismaFotograf)
    
    # Çalışma modeli için izinleri al
    calisma_permissions = Permission.objects.filter(
        content_type=calisma_content_type,
        codename__in=['add_calisma', 'change_calisma', 'delete_calisma', 'view_calisma']
    )
    
    # CalismaFotograf modeli için izinleri al
    fotograf_permissions = Permission.objects.filter(
        content_type=fotograf_content_type,
        codename__in=['add_calismafotograf', 'change_calismafotograf', 'delete_calismafotograf', 'view_calismafotograf']
    )
    
    # Tüm izinleri gruba ekle
    for perm in list(calisma_permissions) + list(fotograf_permissions):
        group.permissions.add(perm)
    
    return group

def check_calisma_permission(user, calisma=None):
    """
    Kullanıcının çalışma üzerinde yetkisi olup olmadığını kontrol eder.
    
    Aşağıdaki durumlarda True döner:
    1. Kullanıcı süper kullanıcı ise
    2. Kullanıcı "Çalışma Yöneticileri" grubuna dahilse ve çalışma kendisine aitse
    3. Kullanıcı staff (yönetici) ise
    
    Args:
        user: Kontrol edilecek kullanıcı
        calisma: İsteğe bağlı, belirli bir çalışma için kontrol yapılacaksa
        
    Returns:
        bool: Kullanıcının yetkisi varsa True, yoksa False
    """
    # Süper kullanıcılar her zaman yetkilidir
    if user.is_superuser:
        return True
    
    # Yöneticiler (staff) her zaman yetkilidir
    if user.is_staff:
        return True
    
    # Çalışma Yöneticileri grubunda olup olmadığını kontrol et
    is_calisma_yoneticisi = user.groups.filter(name='Çalışma Yöneticileri').exists()
    
    # Eğer çalışma belirtilmişse ve kullanıcı Çalışma Yöneticisi ise,
    # sadece kendi çalışmalarını düzenleyebilir
    if calisma and is_calisma_yoneticisi:
        return calisma.user == user
    
    # Eğer çalışma belirtilmemişse ve kullanıcı Çalışma Yöneticisi ise,
    # yeni çalışma ekleyebilir
    if calisma is None and is_calisma_yoneticisi:
        return True
    
    return False
