{% extends 'base.html' %}
{% load static %}
{% load video_filters %}

{% block title %}{{ video.baslik }} - Kü<PERSON> Cadısı{% endblock %}

{% block extra_css %}
{% endblock %}

{% block content %}
<section class="modern-section py-3">
    <div class="container modern-container">
        <!-- <PERSON>ş<PERSON>ık Bölümü -->
        <div class="modern-header text-center mb-5 wow fadeIn" data-wow-delay="0.2s">
            <h1 class="autumn-heading">Seramik Sanatında İlk Adımlar</h1>
            <p class="lead text-muted"><PERSON> emeğiyle şekillenen her seramik hikayesi... Videolarımızla birlikte siz de deneyin!</p>
        </div>

        <div class="row g-4">
            <!-- Video İçeriği -->
            <div class="col-lg-8 wow fadeInUp" data-wow-delay="0.3s">
                <div class="modern-video-container">
                    <div class="modern-card">
                        <div class="modern-video-player">
                            <iframe src="{{ video.youtube_url|youtube_embed }}"
                                    title="{{ video.baslik }}"
                                    allowfullscreen></iframe>
                        </div>
                        <div class="modern-card-body">
                            <h2 class="modern-title">{{ video.baslik }}</h2>
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <span class="modern-badge autumn-tag">{{ video.kategori.ad }}</span>
                                <span class="text-muted small"><i class="bi bi-calendar3 me-1 text-moss"></i>{{ video.tarih|date:"d.m.Y" }}</span>
                            </div>
                            <div class="modern-card-text mb-3">{{ video.aciklama|linebreaks }}</div>
                            <div class="d-flex align-items-center justify-content-between mt-3">
                                <div class="text-muted small">
                                    <i class="bi bi-eye me-1 text-navy"></i> {{ video.goruntulenme }} görüntüleme
                                </div>
                                <button type="button" id="begeniBtn" class="modern-like-btn {% if kullanici_begendi %}active{% endif %}">
                                    <i class="bi {% if kullanici_begendi %}bi-hand-thumbs-up-fill{% else %}bi-hand-thumbs-up{% endif %}"></i>
                                    <span id="begeniSayisi">{{ video.begeni_sayisi }}</span> Beğen
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                {% load yorum_tags %}
                {% yorumlari_goster video %}


            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="modern-card mb-4 wow fadeInRight" data-wow-delay="0.3s">
                    <div class="modern-card-header autumn-card-header">
                        <h3><i class="bi bi-info-circle me-2 text-brown"></i>Video Hakkında</h3>
                    </div>

                    <div class="modern-card-body p-0 sidebar-body">
                        <div class="modern-info-list">
                            <div class="modern-info-item modern-list-item">
                                <strong>Ekleyen:</strong>
                                <a href="{% url 'uyelik:profil' %}" class="text-decoration-none d-flex align-items-center">
                                    <span class="text-navy me-1">{{ video.ekleyen.username }}</span>
                                    <i class="bi bi-person-circle text-navy"></i>
                                </a>
                            </div>
                            <div class="modern-info-item modern-list-item">
                                <strong>Yüklenme Tarihi:</strong>
                                <span><i class="bi bi-calendar3 me-1 text-moss"></i>{{ video.tarih|date:"d F Y" }}</span>
                            </div>
                            <div class="modern-info-item modern-list-item">
                                <strong>Kategori:</strong>
                                <span><i class="bi bi-tag me-1 text-brown"></i>{{ video.kategori.ad }}</span>
                            </div>
                            <div class="modern-info-item modern-list-item">
                                <strong>Görüntüleme:</strong>
                                <span><i class="bi bi-eye-fill me-1 text-navy"></i>{{ video.goruntulenme }}</span>
                            </div>
                            <div class="modern-info-item modern-list-item">
                                <strong>Beğeni:</strong>
                                <span><i class="bi bi-hand-thumbs-up-fill me-1 text-brown"></i>{{ video.begeni_sayisi }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modern-card mb-4 wow fadeInRight" data-wow-delay="0.4s">
                    <div class="modern-card-header autumn-card-header">
                        <h3><i class="bi bi-collection-play me-2 text-brown"></i>Benzer Videolar</h3>
                    </div>
                    <div class="modern-card-body p-0 sidebar-body">
                        <ul class="modern-sidebar-list">
                            {% for benzer_video in benzer_videolar %}
                            <li>
                                <a href="{% url 'nasilyapilir:video_detay' benzer_video.slug %}" class="modern-sidebar-item autumn-sidebar-item">
                                    <div class="modern-sidebar-thumbnail">
                                        <img src="{{ benzer_video.youtube_url|youtube_thumbnail }}" alt="{{ benzer_video.baslik }}">
                                        <div class="modern-play-button">
                                            <i class="bi bi-play-circle"></i>
                                        </div>
                                    </div>
                                    <div class="modern-sidebar-info">
                                        <h6 class="mb-1 autumn-title text-navy">{{ benzer_video.baslik }}</h6>
                                        <div class="d-flex align-items-center small text-muted">
                                            <span class="me-2"><i class="bi bi-eye me-1 text-navy"></i>{{ benzer_video.goruntulenme }}</span>
                                            <span><i class="bi bi-calendar3 me-1 text-moss"></i>{{ benzer_video.tarih|date:"d.m.Y" }}</span>
                                        </div>
                                    </div>
                                </a>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>

                <div class="modern-card mb-4 wow fadeInRight" data-wow-delay="0.4s">
                    <div class="modern-card-header autumn-card-header">
                        <h3><i class="bi bi-fire me-2 text-brown"></i>Popüler Videolar</h3>
                    </div>
                    <div class="modern-card-body p-0 sidebar-body">
                        <ul class="modern-sidebar-list">
                            {% for populer_video in populer_videolar %}
                            <li>
                                <a href="{% url 'nasilyapilir:video_detay' populer_video.slug %}" class="modern-sidebar-item autumn-sidebar-item">
                                    <div class="modern-sidebar-thumbnail">
                                        <img src="{{ populer_video.youtube_url|youtube_thumbnail }}" alt="{{ populer_video.baslik }}">
                                        <div class="modern-play-button">
                                            <i class="bi bi-play-circle"></i>
                                        </div>
                                    </div>
                                    <div class="modern-sidebar-info">
                                        <h6 class="mb-1 autumn-title text-navy">{{ populer_video.baslik }}</h6>
                                        <div class="d-flex align-items-center small text-muted">
                                            <span class="me-2"><i class="bi bi-eye me-1 text-navy"></i>{{ populer_video.goruntulenme }}</span>
                                            <span><i class="bi bi-calendar3 me-1 text-moss"></i>{{ populer_video.tarih|date:"d.m.Y" }}</span>
                                        </div>
                                    </div>
                                </a>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>

                <div class="modern-card mb-4 wow fadeInRight" data-wow-delay="0.5s">
                    <div class="modern-card-header autumn-card-header">
                        <h3><i class="bi bi-tags me-2 text-brown"></i>Etiketler</h3>
                    </div>
                    <div class="modern-card-body p-0 sidebar-body">
                        <div class="modern-tag-cloud">
                            {% for tag in video.tags.all %}
                            <a href="{% url 'nasilyapilir:video_listesi' %}?etiket={{ tag.slug }}"
                               class="modern-tag autumn-tag">
                                {{ tag.name }}
                            </a>
                            {% endfor %}
                        </div>
                    </div>
                </div>

                <!-- Yönetim Paneli (Sadece Yöneticiler) -->
                {% if user.is_authenticated and user.is_superuser %}
                <div class="modern-card admin-panel-card mb-4 wow fadeInRight" data-wow-delay="0.6s">
                    <div class="featured-card-header admin-panel-header">
                        <h3><i class="bi bi-gear-fill me-2 text-white"></i>Yönetim Paneli</h3>
                    </div>
                    <div class="modern-card-body p-0">
                        <!-- Yönetim Menüsü -->
                        <div class="admin-menu">
                            <!-- Yeni Video Ekle -->
                            <a href="{% url 'nasilyapilir:video_olustur' %}" class="admin-menu-item">
                                <div class="admin-menu-icon">
                                    <i class="bi bi-plus-circle-fill"></i>
                                </div>
                                <div class="admin-menu-content">
                                    <h5>Yeni Video Ekle</h5>
                                    <p class="text-muted" style="font-size: var(--font-size-sm);">Nasıl yapılır koleksiyonuna yeni video ekleyin</p>
                                </div>
                                <div class="admin-menu-arrow">
                                    <i class="bi bi-chevron-right"></i>
                                </div>
                            </a>

                            <!-- Bu Videoyu Düzenle -->
                            <a href="{% url 'nasilyapilir:video_duzenle' video.slug %}" class="admin-menu-item">
                                <div class="admin-menu-icon edit-icon">
                                    <i class="bi bi-pencil-square"></i>
                                </div>
                                <div class="admin-menu-content">
                                    <h5>Bu Videoyu Düzenle</h5>
                                    <p class="text-muted" style="font-size: var(--font-size-sm);">Mevcut videonun bilgilerini güncelleyin</p>
                                </div>
                                <div class="admin-menu-arrow">
                                    <i class="bi bi-chevron-right"></i>
                                </div>
                            </a>

                            <!-- Videoyu Sil -->
                            <a href="#" class="admin-menu-item delete-item" data-bs-toggle="modal" data-bs-target="#deleteVideoModal">
                                <div class="admin-menu-icon delete-icon">
                                    <i class="bi bi-trash"></i>
                                </div>
                                <div class="admin-menu-content">
                                    <h5>Videoyu Sil</h5>
                                    <p class="text-muted" style="font-size: var(--font-size-sm);">Bu videoyu kalıcı olarak silin</p>
                                </div>
                                <div class="admin-menu-arrow">
                                    <i class="bi bi-chevron-right"></i>
                                </div>
                            </a>

                            <!-- Video Listesine Dön -->
                            <a href="{% url 'nasilyapilir:video_listesi' %}" class="admin-menu-item">
                                <div class="admin-menu-icon back-icon">
                                    <i class="bi bi-arrow-left-circle"></i>
                                </div>
                                <div class="admin-menu-content">
                                    <h5>Video Listesine Dön</h5>
                                    <p class="text-muted" style="font-size: var(--font-size-sm);">Tüm videoları görüntüleyin</p>
                                </div>
                                <div class="admin-menu-arrow">
                                    <i class="bi bi-chevron-right"></i>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Video Silme Modal (Sadece Yöneticiler) -->
                {% if user.is_authenticated and user.is_superuser %}
                <div class="modal fade" id="deleteVideoModal" tabindex="-1" aria-labelledby="deleteVideoModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header" style="background: linear-gradient(135deg, var(--color-brown) 0%, var(--color-sand) 100%); color: white;">
                                <h5 class="modal-title" id="deleteVideoModalLabel"><i class="bi bi-trash me-2"></i>Videoyu Sil</h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Kapat"></button>
                            </div>
                            <div class="modal-body">
                                <p class="mb-0">Bu videoyu silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.</p>
                                <p class="text-danger mt-3" style="margin-top: var(--space-md) !important;"><strong>Uyarı:</strong> Video kalıcı olarak silinecektir.</p>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="modern-btn modern-btn-outline" data-bs-dismiss="modal">
                                    <i class="bi bi-x-circle me-1"></i>İptal
                                </button>
                                <form action="{% url 'nasilyapilir:video_sil' video.slug %}" method="post" style="display: inline;">
                                    {% csrf_token %}
                                    <button type="submit" class="modern-btn modern-btn-primary">
                                        <i class="bi bi-trash me-1"></i>Evet, Sil
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Yönetim Paneli Stilleri -->
<style>
/* Yönetim Paneli Kartı */
.admin-panel-card {
    border: none;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.admin-panel-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.admin-panel-header {
    background: linear-gradient(135deg, #A66F3F 0%, #734429 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 15px 15px 0 0;
}

.admin-panel-header h3 {
    margin: 0;
    font-weight: 600;
    font-size: 1.2rem;
}

/* Admin Menü */
.admin-menu {
    padding: 0;
}

.admin-menu-item {
    display: flex;
    align-items: center;
    padding: 1.2rem 1.5rem;
    text-decoration: none;
    color: #333;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.admin-menu-item:last-child {
    border-bottom: none;
}

.admin-menu-item:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #A66F3F;
    text-decoration: none;
    transform: translateX(5px);
}

.admin-menu-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 4px;
    background: #A66F3F;
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.admin-menu-item:hover::before {
    transform: scaleY(1);
}

.admin-menu-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.3rem;
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    color: #1976d2;
    transition: all 0.3s ease;
}

.admin-menu-icon.edit-icon {
    background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%);
    color: #f57c00;
}

.admin-menu-icon.delete-icon {
    background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
    color: #d32f2f;
}

.admin-menu-icon.back-icon {
    background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
    color: #7b1fa2;
}

.admin-menu-item:hover .admin-menu-icon {
    transform: scale(1.1) rotate(5deg);
}

.admin-menu-content {
    flex: 1;
}

.admin-menu-content h5 {
    margin: 0 0 0.3rem 0;
    font-weight: 600;
    font-size: 1rem;
    color: #333;
}

.admin-menu-content p {
    margin: 0;
    font-size: 0.85rem;
    color: #666;
    line-height: 1.4;
}

.admin-menu-arrow {
    font-size: 1.2rem;
    color: #ccc;
    transition: all 0.3s ease;
}

.admin-menu-item:hover .admin-menu-arrow {
    color: #A66F3F;
    transform: translateX(5px);
}

/* Modal Stilleri */
.modal-content {
    border-radius: 15px;
    border: none;
    overflow: hidden;
}

.modal-header {
    border-bottom: none;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    padding: 1rem 1.5rem;
}

.modern-btn {
    padding: 0.6rem 1.2rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.modern-btn-outline {
    background: transparent;
    border: 2px solid #6c757d;
    color: #6c757d;
}

.modern-btn-outline:hover {
    background: #6c757d;
    color: white;
}

.modern-btn-primary {
    background: linear-gradient(135deg, #A66F3F 0%, #734429 100%);
    color: white;
}

.modern-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(166, 111, 63, 0.3);
}

/* Responsive */
@media (max-width: 768px) {
    .admin-menu-item {
        padding: 1rem;
    }

    .admin-menu-icon {
        width: 40px;
        height: 40px;
        font-size: 1.1rem;
    }

    .admin-menu-content h5 {
        font-size: 0.9rem;
    }

    .admin-menu-content p {
        font-size: 0.8rem;
    }
}
</style>

<!-- AJAX beğeni işlevselliği için -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    $(document).ready(function() {
        // Beğeni butonu işlevi
        $('#begeniBtn').click(function(e) {
            e.preventDefault();

            console.log('Beğeni butonuna tıklandı - jQuery');

            $.ajax({
                url: "{% url 'nasilyapilir:video_begen' video.slug %}",
                type: 'POST',
                headers: {
                    'X-CSRFToken': '{{ csrf_token }}',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(data) {
                    console.log('Başarılı yanıt:', data);

                    // Beğeni sayısını güncelle
                    $('#begeniSayisi').text(data.begeni_sayisi);

                    // Beğeni ikonu güncelle
                    var $icon = $('#begeniBtn i');
                    if (data.begendi) {
                        $('#begeniBtn').addClass('active');
                        $icon.removeClass('bi-hand-thumbs-up').addClass('bi-hand-thumbs-up-fill');
                    } else {
                        $('#begeniBtn').removeClass('active');
                        $icon.removeClass('bi-hand-thumbs-up-fill').addClass('bi-hand-thumbs-up');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Beğeni hatası:', error);
                    alert('Beğeni işlemi sırasında bir hata oluştu.');
                }
            });

            return false; // Bağlantı tıklamasını engelle
        });
    });
</script>
{% endblock %}