from django.contrib.auth import get_user_model
from .models import Notification, Conversation, Message

User = get_user_model()

def create_notification(recipient, notification_type, title, message, sender=None, conversation=None, message_obj=None, announcement=None):
    """
    Bildirim oluşturur
    """
    notification = Notification.objects.create(
        recipient=recipient,
        sender=sender,
        notification_type=notification_type,
        title=title,
        message=message,
        conversation=conversation,
        message_obj=message_obj,
        announcement=announcement
    )
    return notification

def create_message_notification(message_obj):
    """
    Yeni mesaj bildirimi oluşturur
    """
    conversation = message_obj.conversation
    sender = message_obj.sender
    
    # Konuşmadaki diğer katılımcılara bildirim gönder
    for participant in conversation.participants.exclude(id=sender.id):
        create_notification(
            recipient=participant,
            sender=sender,
            notification_type='new_message',
            title=f'{sender.get_full_name() or sender.username} size mesaj gönder<PERSON>',
            message=message_obj.content[:100] + ('...' if len(message_obj.content) > 100 else ''),
            conversation=conversation,
            message_obj=message_obj
        )

def create_conversation_notification(conversation, creator):
    """
    Yeni konuşma bildirimi oluşturur
    """
    # Konuşmadaki diğer katılımcılara bildirim gönder
    for participant in conversation.participants.exclude(id=creator.id):
        create_notification(
            recipient=participant,
            sender=creator,
            notification_type='new_conversation',
            title=f'{creator.get_full_name() or creator.username} sizinle yeni bir konuşma başlattı',
            message='Yeni konuşmaya katılmak için tıklayın.',
            conversation=conversation
        )

def get_unread_notifications_count(user):
    """
    Kullanıcının okunmamış bildirim sayısını döndürür
    """
    return user.notifications.filter(is_read=False).count()

def mark_notification_as_read(notification_id, user):
    """
    Bildirimi okundu olarak işaretler
    """
    try:
        notification = Notification.objects.get(id=notification_id, recipient=user)
        notification.is_read = True
        notification.save()
        return True
    except Notification.DoesNotExist:
        return False

def mark_all_notifications_as_read(user):
    """
    Kullanıcının tüm bildirimlerini okundu olarak işaretler
    """
    user.notifications.filter(is_read=False).update(is_read=True)

def get_recent_notifications(user, limit=10):
    """
    Kullanıcının son bildirimlerini getirir
    """
    return user.notifications.all()[:limit]
