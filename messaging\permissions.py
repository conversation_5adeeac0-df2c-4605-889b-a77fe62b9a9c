from rest_framework import permissions
from uyelik.models import Profil
from .models import Conversation

class IsApprovedUserOrAdmin(permissions.BasePermission):
    """
    Kullanıcının onaylanmış bir kullanıcı veya yönetici olmasını gerektirir.
    """
    def has_permission(self, request, view):
        # <PERSON><PERSON> kullan<PERSON>c<PERSON>lar her zaman izinlidir
        if request.user and (request.user.is_staff or request.user.is_superuser):
            return True
        # Onaylanmış kullanıcılar izinlidir
        if request.user and request.user.is_authenticated:
            try:
                # Kullanıcının bir profili ve bu profilin onaylı olup olmadığını kontrol et
                return hasattr(request.user, 'profil') and request.user.profil.is_approved
            except Profil.DoesNotExist:
                return False
        # Diğer durumlarda izin yok
        return False

class IsParticipantOfConversation(permissions.BasePermission):
    """
    Kullanıcının belirli bir konuş<PERSON>ın katılımcısı olmasını gerektirir.
    """
    def has_object_permission(self, request, view, obj):
        # <PERSON>uma izinleri tüm katılımcılara açıktır
        if request.method in permissions.SAFE_METHODS:
            return request.user in obj.participants.all()
        # Yazma izinleri yalnızca katılımcılara açıktır
        return request.user in obj.participants.all()