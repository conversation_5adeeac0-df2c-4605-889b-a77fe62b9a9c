from django.db import models
from django.utils.text import slugify
from django.contrib.auth.models import User
from taggit.managers import TaggableManager

# Create your models here.

class Category(models.Model):
    name = models.CharField(max_length=100, verbose_name="<PERSON><PERSON><PERSON>")
    slug = models.SlugField(unique=True, verbose_name="URL Yolu")
    description = models.TextField(blank=True, verbose_name="Açıklama")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "<PERSON><PERSON><PERSON>"
        verbose_name_plural = "Kategoriler"
        ordering = ['name']

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    def __str__(self):
        return self.name

class Product(models.Model):
    category = models.ForeignKey(Category, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="<PERSON><PERSON><PERSON>")
    name = models.CharField(max_length=100, verbose_name="<PERSON><PERSON><PERSON><PERSON>")
    slug = models.SlugField(unique=True, verbose_name="URL Yolu")
    description = models.TextField(verbose_name="Açıklama")
    price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="Fiyat")
    stock_quantity = models.PositiveIntegerField(default=0, verbose_name="Stok Miktarı")
    is_available = models.BooleanField(default=True, verbose_name="Stokta Var mı?")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    ekleyen = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='urunler', verbose_name="Ekleyen Kullanıcı")
    tags = TaggableManager(blank=True, verbose_name="Etiketler", help_text="Ürünle ilgili etiketleri virgülle ayırarak girin")

    class Meta:
        verbose_name = "Ürün"
        verbose_name_plural = "Ürünler"
        ordering = ['-created_at']

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        """Ürün silindiğinde ilişkili fotoğrafları da sil."""
        # Fotoğrafları al
        product_images = list(self.images.all())

        # Önce modeli sil (CASCADE ile fotoğraf kayıtları da silinecek)
        super().delete(*args, **kwargs)

        # Fotoğraf dosyalarını fiziksel olarak sil
        for image in product_images:
            if image.image:
                storage, path = image.image.storage, image.image.path
                if storage.exists(path):
                    storage.delete(path)

    def __str__(self):
        return self.name

class ProductImage(models.Model):
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='images')
    image = models.ImageField(upload_to='products/%Y/%m/%d/', verbose_name="Ürün Görseli")
    is_main = models.BooleanField(default=False, verbose_name="Ana Görsel")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Ürün Görseli"
        verbose_name_plural = "Ürün Görselleri"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.product.name} - {self.id}"

    def delete(self, *args, **kwargs):
        """Fotoğraf dosyasını fiziksel olarak sil."""
        # Dosya yolunu al
        if self.image:
            storage, path = self.image.storage, self.image.path

            # Modeli sil
            super().delete(*args, **kwargs)

            # Dosyayı sil
            if storage.exists(path):
                storage.delete(path)
        else:
            # Resim yoksa sadece modeli sil
            super().delete(*args, **kwargs)
