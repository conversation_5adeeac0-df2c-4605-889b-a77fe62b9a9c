{% extends 'base.html' %}
{% load static %}

{% block title %}{% if form.instance.id %}Çalışma Düzenle{% else %}Yeni <PERSON>alı<PERSON>{% endif %} - Küp Cadısı{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'assets/css/modern-theme.css' %}">
{% endblock %}

{% block content %}
<section class="modern-section py-5">
    <div class="container">
        <!-- Başlık Bölümü -->
        <div class="modern-section-header text-center mb-5 wow fadeIn" data-wow-delay="0.2s">
            <h1 class="modern-title">{% if form.instance.id %}Çalışma Düzenle{% else %}Yeni Çalışma E<PERSON>{% endif %}</h1>
            <p class="modern-subtitle">Küp Cadısı atölyesine yeni çalışmanızı ekleyin veya mevcut çalışmanız<PERSON> düzenleyin</p>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="modern-card wow fadeInUp" data-wow-delay="0.3s">
                    <div class="modern-card-header">
                        <h3><i class="bi bi-pencil-square me-2"></i>Çalışma Bilgileri</h3>
                    </div>
                    <div class="modern-card-body">
                        <form method="post" enctype="multipart/form-data" class="calisma-form">
                            {% csrf_token %}

                            {% if form.errors %}
                            <div class="alert alert-danger">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                Lütfen formdaki hataları düzeltin.
                            </div>
                            {% endif %}

                            <div class="row g-4">
                                <!-- Başlık -->
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="{{ form.baslik.id_for_label }}" class="form-label">Başlık <span class="text-danger">*</span></label>
                                        {{ form.baslik }}
                                        {% if form.baslik.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.baslik.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Kategori -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="{{ form.kategori.id_for_label }}" class="form-label">Kategori</label>
                                        {{ form.kategori }}
                                        {% if form.kategori.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.kategori.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Etiketler -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="{{ form.etiketler.id_for_label }}" class="form-label">Etiketler</label>
                                        {{ form.etiketler }}
                                        <small class="form-text text-muted">Birden fazla etiket seçebilirsiniz</small>
                                        {% if form.etiketler.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.etiketler.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Açıklama -->
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="{{ form.aciklama.id_for_label }}" class="form-label">Açıklama <span class="text-danger">*</span></label>
                                        {{ form.aciklama }}
                                        {% if form.aciklama.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.aciklama.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Fotoğraflar -->
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="form-label">Fotoğraflar</label>
                                        {{ formset.management_form }}

                                        <div class="photo-formset">
                                            {% for foto_form in formset %}
                                            <div class="photo-form-item mb-3">
                                                <div class="row align-items-center">
                                                    <div class="col-md-10">
                                                        {{ foto_form.id }}
                                                        {{ foto_form.fotograf }}
                                                        {% if foto_form.fotograf.errors %}
                                                        <div class="invalid-feedback d-block">
                                                            {{ foto_form.fotograf.errors }}
                                                        </div>
                                                        {% endif %}
                                                    </div>
                                                    <div class="col-md-2">
                                                        {% if foto_form.instance.pk %}
                                                        <div class="d-flex align-items-center">
                                                            <img src="{{ foto_form.instance.fotograf.url }}" alt="Önizleme" class="img-thumbnail me-2" style="height: 50px;">
                                                            {{ foto_form.DELETE }}
                                                            <label for="{{ foto_form.DELETE.id_for_label }}" class="form-check-label ms-2">Sil</label>
                                                        </div>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                            </div>
                                            {% endfor %}
                                        </div>

                                        <button type="button" id="add-photo" class="modern-btn modern-btn-sm mt-2">
                                            <i class="bi bi-plus-circle me-2"></i>Fotoğraf Ekle
                                        </button>
                                    </div>
                                </div>

                                <!-- Butonlar -->
                                <div class="col-12 mt-4">
                                    <div class="form-actions">
                                        <button type="submit" class="modern-btn">
                                            <i class="bi bi-check-circle me-2"></i>{% if form.instance.id %}Güncelle{% else %}Kaydet{% endif %}
                                        </button>
                                        <a href="{% url 'calismalar:calisma_listesi' %}" class="modern-btn modern-btn-outline ms-2">
                                            <i class="bi bi-x-circle me-2"></i>İptal
                                        </a>
                                        {% if form.instance.id %}
                                        <a href="#" class="modern-btn modern-btn-danger ms-auto" data-bs-toggle="modal" data-bs-target="#deleteModal">
                                            <i class="bi bi-trash me-2"></i>Sil
                                        </a>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

{% if form.instance.id %}
<!-- Silme Onay Modalı -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content modern-modal">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel"><i class="bi bi-exclamation-triangle me-2"></i>Çalışmayı Sil</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Kapat"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <p>Bu çalışmayı silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.</p>
                    <p class="fw-bold mt-2">{{ form.instance.baslik }}</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="modern-btn modern-btn-outline" data-bs-dismiss="modal">İptal</button>
                <form action="{% url 'calismalar:calisma_sil' form.instance.slug %}" method="post">
                    {% csrf_token %}
                    <button type="submit" class="modern-btn modern-btn-danger">Sil</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endif %}

<style>
    /* Modern renk paleti */
    :root {
        --primary-color: #734429;
        --secondary-color: #402401;
        --accent-color: #D9B391;
        --light-accent: #E0D8C8;
        --dark-bg: #2D1A00;
        --text-color: #402401;
        --light-text: #734429;
        --white: #ffffff;
        --off-white: #F8F5F0;
        --light-gray: #E9E4D9;
        --success-color: #2E7D32;
        --error-color: #C62828;
    }

    /* Genel stil */
    .calisma-form-section {
        background: linear-gradient(135deg, var(--off-white) 0%, var(--light-accent) 100%);
        padding: 4rem 0;
    }

    /* Başlık bölümü */
    .section-header {
        margin-bottom: 3rem;
    }

    .section-title {
        color: var(--primary-color);
        font-weight: 700;
        margin-bottom: 1rem;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
    }

    .section-subtitle {
        color: var(--text-color);
        font-size: 1.25rem;
        max-width: 800px;
        margin: 0 auto;
    }

    .title-decoration {
        display: flex;
        justify-content: center;
        margin-top: 1.5rem;
        gap: 8px;
    }

    .title-decoration span {
        height: 4px;
        width: 30px;
        border-radius: 50px;
        background-color: var(--accent-color);
    }

    .title-decoration span:nth-child(2) {
        width: 60px;
        background-color: var(--primary-color);
    }

    /* Form Kartı */
    .form-card {
        border-radius: 20px;
        overflow: hidden;
        background: var(--white);
        box-shadow: 0 10px 25px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
    }

    .form-card:hover {
        box-shadow: 0 15px 35px rgba(64, 36, 1, 0.1);
    }

    .card-header {
        padding: 1.5rem;
        background: linear-gradient(to right, var(--accent-color), var(--light-accent));
        color: var(--secondary-color);
        border-radius: 20px 20px 0 0;
        font-weight: 600;
        text-shadow: 0 1px 1px rgba(255,255,255,0.5);
    }

    .card-body {
        padding: 2rem;
    }

    /* Form Elemanları */
    .form-label {
        color: var(--text-color);
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .form-control, .form-select {
        padding: 0.8rem 1.2rem;
        border-radius: 10px;
        border: 1px solid var(--light-gray);
        background-color: var(--off-white);
        color: var(--text-color);
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.25rem rgba(115, 68, 41, 0.25);
        background-color: var(--white);
    }

    textarea.form-control {
        min-height: 150px;
    }

    .form-text {
        color: var(--light-text);
        font-size: 0.85rem;
        margin-top: 0.5rem;
    }

    .invalid-feedback {
        color: var(--error-color);
        font-size: 0.85rem;
        margin-top: 0.5rem;
    }

    /* Fotoğraf Formset */
    .photo-form-item {
        padding: 1rem;
        border-radius: 10px;
        background-color: var(--off-white);
        transition: all 0.3s ease;
    }

    .photo-form-item:hover {
        background-color: var(--light-accent);
    }

    /* Butonlar */
    .form-actions {
        display: flex;
        align-items: center;
    }

    .btn-primary {
        background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
        border: none;
        padding: 0.8rem 1.5rem;
        border-radius: 50px;
        color: var(--white);
        font-weight: 600;
        box-shadow: 0 5px 15px rgba(115, 68, 41, 0.3);
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(115, 68, 41, 0.4);
    }

    .btn-outline-primary {
        color: var(--primary-color);
        border-color: var(--primary-color);
        border-radius: 50px;
        padding: 0.6rem 1.2rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn-outline-primary:hover {
        background-color: var(--primary-color);
        color: var(--white);
    }

    .btn-outline-secondary {
        color: var(--text-color);
        border-color: var(--light-gray);
        border-radius: 50px;
        padding: 0.8rem 1.5rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn-outline-secondary:hover {
        background-color: var(--light-gray);
        color: var(--text-color);
    }

    .btn-danger {
        background: linear-gradient(to right, #C62828, #B71C1C);
        border: none;
        padding: 0.8rem 1.5rem;
        border-radius: 50px;
        color: var(--white);
        font-weight: 600;
        box-shadow: 0 5px 15px rgba(198, 40, 40, 0.3);
        transition: all 0.3s ease;
    }

    .btn-danger:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(198, 40, 40, 0.4);
    }

    /* Modal */
    .modal-content {
        border-radius: 20px;
        border: none;
        box-shadow: 0 15px 35px rgba(0,0,0,0.2);
    }

    .modal-header {
        background: linear-gradient(to right, var(--accent-color), var(--light-accent));
        color: var(--secondary-color);
        border-radius: 20px 20px 0 0;
        padding: 1.5rem;
    }

    .modal-footer {
        border-top: 1px solid var(--light-gray);
        padding: 1.5rem;
    }

    /* Animasyonlar */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .wow {
        animation: fadeIn 0.8s ease forwards;
    }

    .fadeIn { animation-name: fadeIn; }
    .fadeInUp { animation-name: fadeInUp; }

    @keyframes fadeInUp {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // WOW.js başlatma
        new WOW().init();

        // Form elemanlarını düzenleme
        const formControls = document.querySelectorAll('.form-control, .form-select');
        formControls.forEach(control => {
            control.classList.add('form-control');

            if (control.tagName === 'SELECT') {
                control.classList.add('form-select');
            }
        });

        // Fotoğraf ekleme fonksiyonu
        const addPhotoBtn = document.getElementById('add-photo');
        const photoFormset = document.querySelector('.photo-formset');
        const totalForms = document.getElementById('id_fotograflar-TOTAL_FORMS');

        if (addPhotoBtn && photoFormset && totalForms) {
            addPhotoBtn.addEventListener('click', function() {
                const formCount = parseInt(totalForms.value);
                const newForm = photoFormset.querySelector('.photo-form-item').cloneNode(true);

                // Form elemanlarını temizle ve ID'leri güncelle
                const inputs = newForm.querySelectorAll('input');
                inputs.forEach(input => {
                    if (input.getAttribute('name')) {
                        const name = input.getAttribute('name').replace(/\d+/, formCount);
                        input.setAttribute('name', name);
                    }

                    if (input.getAttribute('id')) {
                        const id = input.getAttribute('id').replace(/\d+/, formCount);
                        input.setAttribute('id', id);
                    }

                    if (input.type === 'file') {
                        input.value = '';
                    } else if (input.type === 'checkbox') {
                        input.checked = false;
                    }
                });

                // Önizleme resmini kaldır
                const imgPreview = newForm.querySelector('img');
                if (imgPreview && imgPreview.parentElement) {
                    imgPreview.parentElement.remove();
                }

                // Hata mesajlarını temizle
                const errorMessages = newForm.querySelectorAll('.invalid-feedback');
                errorMessages.forEach(error => {
                    error.remove();
                });

                photoFormset.appendChild(newForm);
                totalForms.value = formCount + 1;

                // Yeni eklenen form elemanına odaklan
                const newFileInput = newForm.querySelector('input[type="file"]');
                if (newFileInput) {
                    newFileInput.focus();
                }
            });
        }
    });
</script>
{% endblock %}