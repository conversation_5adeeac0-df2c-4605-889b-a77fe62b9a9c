{% extends 'base.html' %}
{% load static %}

{% block title %}{% if form.instance.id %}Çalışma Düzenle{% else %}<PERSON>ni <PERSON>ı<PERSON>{% endif %} - <PERSON>üp Cadısı{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'assets/css/modern-theme.css' %}">
<link rel="stylesheet" href="{% static 'assets/css/form-styles.css' %}">
{% endblock %}

{% block content %}
<section class="modern-section py-5">
    <div class="container">
        <!-- Başlık Bölümü -->
        <div class="modern-section-header text-center mb-5 wow fadeIn" data-wow-delay="0.2s">
            <h1 class="modern-title">{% if form.instance.id %}Çalışma Düzenle{% else %}Yeni Çalış<PERSON>{% endif %}</h1>
            <p class="modern-subtitle"><PERSON>üp Cadısı atölyesine yeni çalışmanızı ekleyin veya mevcut çalışmanızı düzenleyin</p>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="modern-card wow fadeInUp" data-wow-delay="0.3s">
                    <div class="modern-card-header">
                        <h3><i class="bi bi-pencil-square me-2"></i>Çalışma Bilgileri</h3>
                    </div>
                    <div class="modern-card-body">
                        <form method="post" enctype="multipart/form-data" class="calisma-form">
                            {% csrf_token %}

                            {% if form.errors %}
                            <div class="alert alert-danger">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                Lütfen formdaki hataları düzeltin.
                            </div>
                            {% endif %}

                            <div class="row g-4">
                                <!-- Başlık -->
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="{{ form.baslik.id_for_label }}" class="form-label">Başlık <span class="text-danger">*</span></label>
                                        {{ form.baslik }}
                                        {% if form.baslik.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.baslik.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Kategori -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="{{ form.kategori.id_for_label }}" class="form-label">Kategori</label>
                                        {{ form.kategori }}
                                        {% if form.kategori.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.kategori.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Etiketler -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="{{ form.tags.id_for_label }}" class="form-label">Etiketler</label>
                                        {{ form.tags }}
                                        <small class="form-text text-muted">Etiketleri virgülle ayırarak girin (örn: ahşap, el yapımı, vintage)</small>
                                        {% if form.tags.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.tags.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Açıklama -->
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="{{ form.aciklama.id_for_label }}" class="form-label">Açıklama <span class="text-danger">*</span></label>
                                        {{ form.aciklama }}
                                        {% if form.aciklama.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.aciklama.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Fotoğraflar -->
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="form-label">Fotoğraflar</label>
                                        {{ formset.management_form }}

                                        <div class="photo-formset">
                                            {% for foto_form in formset %}
                                            <div class="photo-form-item mb-3">
                                                <div class="row align-items-center">
                                                    <div class="col-md-10">
                                                        {{ foto_form.id }}
                                                        {{ foto_form.fotograf }}
                                                        {% if foto_form.fotograf.errors %}
                                                        <div class="invalid-feedback d-block">
                                                            {{ foto_form.fotograf.errors }}
                                                        </div>
                                                        {% endif %}
                                                    </div>
                                                    <div class="col-md-2">
                                                        {% if foto_form.instance.pk %}
                                                        <div class="d-flex align-items-center">
                                                            <img src="{{ foto_form.instance.fotograf.url }}" alt="Önizleme" class="img-thumbnail me-2" style="height: 50px;">
                                                            {{ foto_form.DELETE }}
                                                            <label for="{{ foto_form.DELETE.id_for_label }}" class="form-check-label ms-2">Sil</label>
                                                        </div>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                            </div>
                                            {% endfor %}
                                        </div>

                                        <button type="button" id="add-photo" class="modern-btn modern-btn-sm mt-2">
                                            <i class="bi bi-plus-circle me-2"></i>Fotoğraf Ekle
                                        </button>
                                    </div>
                                </div>

                                <!-- Butonlar -->
                                <div class="col-12 mt-4">
                                    <div class="form-actions">
                                        <button type="submit" class="modern-btn">
                                            <i class="bi bi-check-circle me-2"></i>{% if form.instance.id %}Güncelle{% else %}Kaydet{% endif %}
                                        </button>
                                        <a href="{% url 'calismalar:calisma_listesi' %}" class="modern-btn modern-btn-outline ms-2">
                                            <i class="bi bi-x-circle me-2"></i>İptal
                                        </a>
                                        {% if form.instance.id %}
                                        <a href="#" class="modern-btn modern-btn-danger ms-auto" data-bs-toggle="modal" data-bs-target="#deleteModal">
                                            <i class="bi bi-trash me-2"></i>Sil
                                        </a>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

{% if form.instance.id %}
<!-- Silme Onay Modalı -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content modern-modal">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel"><i class="bi bi-exclamation-triangle me-2"></i>Çalışmayı Sil</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Kapat"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <p>Bu çalışmayı silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.</p>
                    <p class="fw-bold mt-2">{{ form.instance.baslik }}</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="modern-btn modern-btn-outline" data-bs-dismiss="modal">İptal</button>
                <form action="{% url 'calismalar:calisma_sil' form.instance.slug %}" method="post">
                    {% csrf_token %}
                    <button type="submit" class="modern-btn modern-btn-danger">Sil</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endif %}



<script>
    document.addEventListener('DOMContentLoaded', function() {
        // WOW.js başlatma
        new WOW().init();

        // Form elemanlarını düzenleme
        const formControls = document.querySelectorAll('.form-control, .form-select');
        formControls.forEach(control => {
            control.classList.add('form-control');

            if (control.tagName === 'SELECT') {
                control.classList.add('form-select');
            }
        });

        // Fotoğraf ekleme fonksiyonu
        const addPhotoBtn = document.getElementById('add-photo');
        const photoFormset = document.querySelector('.photo-formset');
        const totalForms = document.getElementById('id_fotograflar-TOTAL_FORMS');

        if (addPhotoBtn && photoFormset && totalForms) {
            addPhotoBtn.addEventListener('click', function() {
                const formCount = parseInt(totalForms.value);
                const newForm = photoFormset.querySelector('.photo-form-item').cloneNode(true);

                // Form elemanlarını temizle ve ID'leri güncelle
                const inputs = newForm.querySelectorAll('input');
                inputs.forEach(input => {
                    if (input.getAttribute('name')) {
                        const name = input.getAttribute('name').replace(/\d+/, formCount);
                        input.setAttribute('name', name);
                    }

                    if (input.getAttribute('id')) {
                        const id = input.getAttribute('id').replace(/\d+/, formCount);
                        input.setAttribute('id', id);
                    }

                    if (input.type === 'file') {
                        input.value = '';
                    } else if (input.type === 'checkbox') {
                        input.checked = false;
                    }
                });

                // Önizleme resmini kaldır
                const imgPreview = newForm.querySelector('img');
                if (imgPreview && imgPreview.parentElement) {
                    imgPreview.parentElement.remove();
                }

                // Hata mesajlarını temizle
                const errorMessages = newForm.querySelectorAll('.invalid-feedback');
                errorMessages.forEach(error => {
                    error.remove();
                });

                photoFormset.appendChild(newForm);
                totalForms.value = formCount + 1;

                // Yeni eklenen form elemanına odaklan
                const newFileInput = newForm.querySelector('input[type="file"]');
                if (newFileInput) {
                    newFileInput.focus();
                }
            });
        }
    });
</script>
{% endblock %}