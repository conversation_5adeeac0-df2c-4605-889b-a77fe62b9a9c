from django.contrib import admin
from .models import Conversation, Message, Announcement, UserAnnouncementReadStatus

@admin.register(Conversation)
class ConversationAdmin(admin.ModelAdmin):
    list_display = ('id', 'created_at', 'updated_at')
    filter_horizontal = ('participants',)
    search_fields = ('participants__username',)

@admin.register(Message)
class MessageAdmin(admin.ModelAdmin):
    list_display = ('conversation', 'sender', 'timestamp', 'content')
    list_filter = ('timestamp', 'sender')
    search_fields = ('sender__username', 'content')

@admin.register(Announcement)
class AnnouncementAdmin(admin.ModelAdmin):
    list_display = ('sender', 'timestamp', 'content')
    list_filter = ('timestamp', 'sender')
    search_fields = ('sender__username', 'content')

@admin.register(UserAnnouncementReadStatus)
class UserAnnouncementReadStatusAdmin(admin.ModelAdmin):
    list_display = ('user', 'announcement', 'is_read', 'read_at')
    list_filter = ('is_read', 'read_at')
    search_fields = ('user__username', 'announcement__content')
