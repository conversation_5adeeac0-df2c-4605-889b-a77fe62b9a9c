# Generated by Django 5.2 on 2025-05-26 16:18

import taggit.managers
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('calismalar', '0004_alter_calismafotograf_fotograf'),
        ('taggit', '0006_rename_taggeditem_content_type_object_id_taggit_tagg_content_8fc721_idx'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='calisma',
            name='etiketler',
        ),
        migrations.AddField(
            model_name='calisma',
            name='tags',
            field=taggit.managers.TaggableManager(blank=True, help_text='Çalışmayla ilgili etiketleri virgülle ayırarak girin', through='taggit.TaggedItem', to='taggit.Tag', verbose_name='Etiketler'),
        ),
    ]
