<svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="smallGrid" width="10" height="10" patternUnits="userSpaceOnUse">
      <path d="M 10 0 L 0 0 0 10" fill="none" stroke="currentColor" stroke-width="0.5" opacity="0.3"/>
    </pattern>
    <pattern id="grid" width="100" height="100" patternUnits="userSpaceOnUse">
      <rect width="100" height="100" fill="url(#smallGrid)"/>
      <path d="M 100 0 L 0 0 0 100" fill="none" stroke="currentColor" stroke-width="1" opacity="0.5"/>
    </pattern>
  </defs>
  <rect width="100" height="100" fill="url(#grid)" opacity="0.3"/>
  <circle cx="50" cy="50" r="5" fill="currentColor" opacity="0.2"/>
  <circle cx="20" cy="80" r="3" fill="currentColor" opacity="0.15"/>
  <circle cx="80" cy="20" r="3" fill="currentColor" opacity="0.15"/>
</svg>
