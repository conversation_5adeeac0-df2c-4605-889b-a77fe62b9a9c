{% extends 'base.html' %}
{% load static %}
{% load video_filters %}

{% block title %}Nasıl Yapılır? - <PERSON><PERSON><PERSON> Cadısı{% endblock %}

{% block extra_css %}
{% endblock %}

{% block content %}
<section class="modern-section py-5">
    <div class="container">
        <!-- <PERSON>şlık Bölümü -->
        <div class="modern-header mb-5 text-center wow fadeIn" data-wow-delay="0.2s">
            <h1 class="autumn-heading display-4 fw-bold mb-3">Seramik Sanatında İlk Adımlar</h1>
            <p class="lead text-muted"><PERSON> emeğiyle şekillenen her seramik hikayesi... Videolarımızla birlikte siz de deneyin!</p>
        </div>

        <div class="row gx-5">
            <!-- Kategori ve Filtreleme Sidebar -->
            <div class="col-lg-3 wow fadeInLeft" data-wow-delay="0.3s">
                <div class="modern-card mb-4 kategori-karti">
                    <div class="modern-card-header autumn-card-header">
                        <h3><i class="bi bi-list-ul me-2 text-brown"></i>Kategoriler</h3>
                    </div>
                    <div class="modern-card-body p-0">
                        <ul class="modern-list kategori-listesi">
                            <li>
                                <a href="{% url 'nasilyapilir:video_listesi' %}"
                                   class="modern-list-item d-flex justify-content-between align-items-center {% if not request.GET.kategori %}active{% endif %}">
                                    <span>Tümü</span>
                                    <span class="modern-badge">{{ total_videos }}</span>
                                </a>
                            </li>
                            {% for kategori in kategoriler %}
                            <li>
                                <a href="?kategori={{ kategori.slug }}"
                                   class="modern-list-item d-flex justify-content-between align-items-center {% if request.GET.kategori == kategori.slug %}active{% endif %}">
                                    <span>{{ kategori.ad }}</span>
                                    <span class="modern-badge">{{ kategori.videolar.count }}</span>
                                </a>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>

                <div class="modern-card wow fadeInLeft etiketler-karti" data-wow-delay="0.4s">
                    <div class="modern-card-header autumn-card-header">
                        <h3><i class="bi bi-tags me-2 text-brown"></i>Popüler Etiketler</h3>
                    </div>
                    <div class="modern-card-body">
                        <div class="modern-tag-cloud">
                            {% for etiket in etiketler %}
                            <a href="?etiket={{ etiket.slug }}"
                               class="modern-tag {% if request.GET.etiket == etiket.slug %}active{% endif %}">
                                {{ etiket.ad }}
                            </a>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Video Listesi -->
            <div class="col-lg-9">
                <div class="row row-cols-1 row-cols-md-2 row-cols-lg-2 g-4 mb-4">
                    {% for video in videolar %}
                    <div class="col wow fadeInUp" data-wow-delay="0.3s">
                        <div class="modern-card h-100 video-card">
                            <a href="{% url 'nasilyapilir:video_detay' video.slug %}" class="modern-card-link d-flex flex-column h-100">
                                <div class="modern-card-img video-thumbnail">
                                    <img src="{{ video.youtube_url|youtube_thumbnail }}" alt="{{ video.baslik }}">
                                    <span class="modern-badge-corner">{{ video.kategori.ad }}</span>
                                    <div class="modern-play-button">
                                        <i class="bi bi-play-circle-fill"></i>
                                    </div>
                                </div>
                                <div class="modern-card-body d-flex flex-column">
                                    <h5 class="mb-1 autumn-title text-navy">{{ video.baslik }}</h5>
                                    <div class="d-flex justify-content-between align-items-center mt-auto">
                                        <div class="text-muted small">
                                            <span class="me-2"><i class="bi bi-eye me-1 text-navy"></i>{{ video.goruntulenme }}</span>
                                            <span><i class="bi bi-hand-thumbs-up me-1 text-brown"></i>{{ video.begeni }}</span>
                                        </div>
                                        <div class="text-muted small"><i class="bi bi-calendar3 me-1 text-moss"></i>{{ video.tarih|date:"d.m.Y" }}</div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                    {% empty %}
                    <div class="col-12 wow fadeIn" data-wow-delay="0.3s">
                        <div class="modern-card">
                            <div class="modern-card-body text-center py-5">
                                <i class="bi bi-camera-video fs-1 text-muted mb-3"></i>
                                <p>Henüz video eklenmemiş.</p>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- Sayfalama -->
                {% if is_paginated %}
                <nav aria-label="Video sayfalama" class="mt-5 wow fadeInUp" data-wow-delay="0.5s">
                    <ul class="modern-pagination">
                        {% if page_obj.has_previous %}
                        <li>
                            <a class="modern-page-link"
                               href="?page={{ page_obj.previous_page_number }}{% if request.GET.kategori %}&kategori={{ request.GET.kategori }}{% endif %}{% if request.GET.etiket %}&etiket={{ request.GET.etiket }}{% endif %}">
                                <i class="bi bi-chevron-left"></i>
                            </a>
                        </li>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                        <li>
                            <a class="modern-page-link {% if page_obj.number == num %}active{% endif %}"
                               href="?page={{ num }}{% if request.GET.kategori %}&kategori={{ request.GET.kategori }}{% endif %}{% if request.GET.etiket %}&etiket={{ request.GET.etiket }}{% endif %}">
                                {{ num }}
                            </a>
                        </li>
                        {% endfor %}

                        {% if page_obj.has_next %}
                        <li>
                            <a class="modern-page-link"
                               href="?page={{ page_obj.next_page_number }}{% if request.GET.kategori %}&kategori={{ request.GET.kategori }}{% endif %}{% if request.GET.etiket %}&etiket={{ request.GET.etiket }}{% endif %}">
                                <i class="bi bi-chevron-right"></i>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
{% endblock %}